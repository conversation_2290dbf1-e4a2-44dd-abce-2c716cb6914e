var Ti = Object.defineProperty;
var ra = (e) => {
  throw TypeError(e);
};
var wi = (e, t, r) => t in e ? Ti(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;
var Ci = (e, t) => () => (t || e((t = { exports: {} }).exports, t), t.exports);
var ht = (e, t, r) => wi(e, typeof t != "symbol" ? t + "" : t, r), an = (e, t, r) => t.has(e) || ra("Cannot " + r);
var k = (e, t, r) => (an(e, t, "read from private field"), r ? r.call(e) : t.get(e)), oe = (e, t, r) => t.has(e) ? ra("Cannot add the same private member more than once") : t instanceof WeakSet ? t.add(e) : t.set(e, r), X = (e, t, r, n) => (an(e, t, "write to private field"), n ? n.call(e, r) : t.set(e, r), r), ve = (e, t, r) => (an(e, t, "access private method"), r);
import * as Fe from "react";
import Hn, { useContext as Ei, memo as ds, useCallback as Mr, useState as Fi, useEffect as Nr, useMemo as vn, useRef as ps, Fragment as Oi } from "react";
import { useCurrentFrame as mr, interpolate as P, Easing as Cr, Audio as Ri, delayRender as vs, continueRender as Ar, OffthreadVideo as _i, Sequence as na, AbsoluteFill as gs, registerRoot as Ui, Composition as Li } from "remotion";
var td = Ci((g) => {
  var Er = { exports: {} }, Vt = {};
  /**
   * @license React
   * react-jsx-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
  var aa;
  function Ai() {
    if (aa) return Vt;
    aa = 1;
    var e = Symbol.for("react.transitional.element"), t = Symbol.for("react.fragment");
    function r(n, a, s) {
      var i = null;
      if (s !== void 0 && (i = "" + s), a.key !== void 0 && (i = "" + a.key), "key" in a) {
        s = {};
        for (var o in a)
          o !== "key" && (s[o] = a[o]);
      } else s = a;
      return a = s.ref, {
        $$typeof: e,
        type: n,
        key: i,
        ref: a !== void 0 ? a : null,
        props: s
      };
    }
    return Vt.Fragment = t, Vt.jsx = r, Vt.jsxs = r, Vt;
  }
  var qt = {};
  /**
   * @license React
   * react-jsx-runtime.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
  var sa;
  function Ii() {
    return sa || (sa = 1, process.env.NODE_ENV !== "production" && function() {
      function e(y) {
        if (y == null) return null;
        if (typeof y == "function")
          return y.$$typeof === q ? null : y.displayName || y.name || null;
        if (typeof y == "string") return y;
        switch (y) {
          case S:
            return "Fragment";
          case x:
            return "Profiler";
          case m:
            return "StrictMode";
          case I:
            return "Suspense";
          case W:
            return "SuspenseList";
          case Q:
            return "Activity";
        }
        if (typeof y == "object")
          switch (typeof y.tag == "number" && console.error(
            "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
          ), y.$$typeof) {
            case v:
              return "Portal";
            case b:
              return (y.displayName || "Context") + ".Provider";
            case E:
              return (y._context.displayName || "Context") + ".Consumer";
            case M:
              var L = y.render;
              return y = y.displayName, y || (y = L.displayName || L.name || "", y = y !== "" ? "ForwardRef(" + y + ")" : "ForwardRef"), y;
            case U:
              return L = y.displayName || null, L !== null ? L : e(y.type) || "Memo";
            case w:
              L = y._payload, y = y._init;
              try {
                return e(y(L));
              } catch {
              }
          }
        return null;
      }
      function t(y) {
        return "" + y;
      }
      function r(y) {
        try {
          t(y);
          var L = !1;
        } catch {
          L = !0;
        }
        if (L) {
          L = console;
          var Z = L.error, pe = typeof Symbol == "function" && Symbol.toStringTag && y[Symbol.toStringTag] || y.constructor.name || "Object";
          return Z.call(
            L,
            "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
            pe
          ), t(y);
        }
      }
      function n(y) {
        if (y === S) return "<>";
        if (typeof y == "object" && y !== null && y.$$typeof === w)
          return "<...>";
        try {
          var L = e(y);
          return L ? "<" + L + ">" : "<...>";
        } catch {
          return "<...>";
        }
      }
      function a() {
        var y = ae.A;
        return y === null ? null : y.getOwner();
      }
      function s() {
        return Error("react-stack-top-frame");
      }
      function i(y) {
        if (ne.call(y, "key")) {
          var L = Object.getOwnPropertyDescriptor(y, "key").get;
          if (L && L.isReactWarning) return !1;
        }
        return y.key !== void 0;
      }
      function o(y, L) {
        function Z() {
          ee || (ee = !0, console.error(
            "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
            L
          ));
        }
        Z.isReactWarning = !0, Object.defineProperty(y, "key", {
          get: Z,
          configurable: !0
        });
      }
      function u() {
        var y = e(this.type);
        return ie[y] || (ie[y] = !0, console.error(
          "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
        )), y = this.props.ref, y !== void 0 ? y : null;
      }
      function l(y, L, Z, pe, Me, Ce, ft, Ft) {
        return Z = Ce.ref, y = {
          $$typeof: d,
          type: y,
          key: L,
          props: Ce,
          _owner: Me
        }, (Z !== void 0 ? Z : null) !== null ? Object.defineProperty(y, "ref", {
          enumerable: !1,
          get: u
        }) : Object.defineProperty(y, "ref", { enumerable: !1, value: null }), y._store = {}, Object.defineProperty(y._store, "validated", {
          configurable: !1,
          enumerable: !1,
          writable: !0,
          value: 0
        }), Object.defineProperty(y, "_debugInfo", {
          configurable: !1,
          enumerable: !1,
          writable: !0,
          value: null
        }), Object.defineProperty(y, "_debugStack", {
          configurable: !1,
          enumerable: !1,
          writable: !0,
          value: ft
        }), Object.defineProperty(y, "_debugTask", {
          configurable: !1,
          enumerable: !1,
          writable: !0,
          value: Ft
        }), Object.freeze && (Object.freeze(y.props), Object.freeze(y)), y;
      }
      function c(y, L, Z, pe, Me, Ce, ft, Ft) {
        var he = L.children;
        if (he !== void 0)
          if (pe)
            if (K(he)) {
              for (pe = 0; pe < he.length; pe++)
                h(he[pe]);
              Object.freeze && Object.freeze(he);
            } else
              console.error(
                "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
              );
          else h(he);
        if (ne.call(L, "key")) {
          he = e(y);
          var Ot = Object.keys(L).filter(function(ki) {
            return ki !== "key";
          });
          pe = 0 < Ot.length ? "{key: someKey, " + Ot.join(": ..., ") + ": ...}" : "{key: someKey}", se[he + pe] || (Ot = 0 < Ot.length ? "{" + Ot.join(": ..., ") + ": ...}" : "{}", console.error(
            `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
            pe,
            he,
            Ot,
            he
          ), se[he + pe] = !0);
        }
        if (he = null, Z !== void 0 && (r(Z), he = "" + Z), i(L) && (r(L.key), he = "" + L.key), "key" in L) {
          Z = {};
          for (var nn in L)
            nn !== "key" && (Z[nn] = L[nn]);
        } else Z = L;
        return he && o(
          Z,
          typeof y == "function" ? y.displayName || y.name || "Unknown" : y
        ), l(
          y,
          he,
          Ce,
          Me,
          a(),
          Z,
          ft,
          Ft
        );
      }
      function h(y) {
        typeof y == "object" && y !== null && y.$$typeof === d && y._store && (y._store.validated = 1);
      }
      var f = Hn, d = Symbol.for("react.transitional.element"), v = Symbol.for("react.portal"), S = Symbol.for("react.fragment"), m = Symbol.for("react.strict_mode"), x = Symbol.for("react.profiler"), E = Symbol.for("react.consumer"), b = Symbol.for("react.context"), M = Symbol.for("react.forward_ref"), I = Symbol.for("react.suspense"), W = Symbol.for("react.suspense_list"), U = Symbol.for("react.memo"), w = Symbol.for("react.lazy"), Q = Symbol.for("react.activity"), q = Symbol.for("react.client.reference"), ae = f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ne = Object.prototype.hasOwnProperty, K = Array.isArray, te = console.createTask ? console.createTask : function() {
        return null;
      };
      f = {
        react_stack_bottom_frame: function(y) {
          return y();
        }
      };
      var ee, ie = {}, le = f.react_stack_bottom_frame.bind(
        f,
        s
      )(), ce = te(n(s)), se = {};
      qt.Fragment = S, qt.jsx = function(y, L, Z, pe, Me) {
        var Ce = 1e4 > ae.recentlyCreatedOwnerStacks++;
        return c(
          y,
          L,
          Z,
          !1,
          pe,
          Me,
          Ce ? Error("react-stack-top-frame") : le,
          Ce ? te(n(y)) : ce
        );
      }, qt.jsxs = function(y, L, Z, pe, Me) {
        var Ce = 1e4 > ae.recentlyCreatedOwnerStacks++;
        return c(
          y,
          L,
          Z,
          !0,
          pe,
          Me,
          Ce ? Error("react-stack-top-frame") : le,
          Ce ? te(n(y)) : ce
        );
      };
    }()), qt;
  }
  var ia;
  function Di() {
    return ia || (ia = 1, process.env.NODE_ENV === "production" ? Er.exports = Ai() : Er.exports = Ii()), Er.exports;
  }
  var N = Di();
  const ms = Hn.createContext(null), yr = () => Ei(ms), oa = {
    fontFamily: "Inter, sans-serif",
    fontSize: "2.5rem",
    lineHeight: 1.4,
    textAlign: "center",
    color: "#FFFFFF",
    textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
    padding: "24px",
    highlightStyle: {
      backgroundColor: "rgba(20, 184, 166, 0.95)",
      scale: 1.1,
      fontWeight: 600,
      textShadow: "2px 2px 4px rgba(0,0,0,0.3)"
    }
  }, Pi = ({
    overlay: e
  }) => {
    const { playerMetadata: { fps: t } } = yr(), n = mr() / t * 1e3, a = e.styles || oa, s = e.captions.find(
      (o) => n >= o.startMs && n <= o.endMs
    );
    if (!s) return null;
    const i = (o) => {
      var u;
      return (u = o == null ? void 0 : o.words) == null ? void 0 : u.map((l, c) => {
        const h = n >= l.startMs && n <= l.endMs, f = h ? Math.min((n - l.startMs) / 300, 1) : 0, d = a.highlightStyle || oa.highlightStyle;
        return /* @__PURE__ */ N.jsx(
          "span",
          {
            className: "inline-block transition-all duration-200",
            style: {
              color: h ? d == null ? void 0 : d.color : a.color,
              backgroundColor: h ? d == null ? void 0 : d.backgroundColor : "transparent",
              opacity: h ? 1 : 0.85,
              transform: h ? `scale(${1 + (d != null && d.scale ? (d.scale - 1) * f : 0.08)})` : "scale(1)",
              fontWeight: h ? (d == null ? void 0 : d.fontWeight) || 600 : a.fontWeight || 400,
              textShadow: h ? d == null ? void 0 : d.textShadow : a.textShadow,
              padding: (d == null ? void 0 : d.padding) || "4px 8px",
              borderRadius: (d == null ? void 0 : d.borderRadius) || "4px",
              margin: "0 2px"
            },
            children: l.word
          },
          `${l.word}-${c}`
        );
      });
    };
    return /* @__PURE__ */ N.jsx(
      "div",
      {
        className: "absolute inset-0 flex items-center justify-center p-4",
        style: {
          ...a,
          width: "100%",
          height: "100%"
        },
        children: /* @__PURE__ */ N.jsx(
          "div",
          {
            className: "leading-relaxed tracking-wide",
            style: {
              whiteSpace: "pre-wrap",
              width: "100%",
              textAlign: "center",
              wordBreak: "break-word",
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "center",
              alignItems: "center",
              gap: "2px"
            },
            children: i(s)
          }
        )
      }
    );
  }, Gt = {
    fade: {
      name: "Fade",
      preview: "Simple fade in/out",
      enter: (e) => ({
        opacity: P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        opacity: P(e, [t - 15, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    },
    slideRight: {
      name: "Slide",
      preview: "Slide in from left",
      isPro: !0,
      enter: (e) => ({
        transform: `translateX(${P(e, [0, 15], [-100, 0], {
          extrapolateRight: "clamp"
        })}%)`,
        opacity: P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `translateX(${P(
          e,
          [t - 15, t],
          [0, 100],
          { extrapolateLeft: "clamp" }
        )}%)`,
        opacity: P(e, [t - 15, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    },
    scale: {
      name: "Scale",
      preview: "Scale in/out",
      enter: (e) => ({
        transform: `scale(${P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        })})`,
        opacity: P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `scale(${P(
          e,
          [t - 15, t],
          [1, 0],
          { extrapolateLeft: "clamp" }
        )})`,
        opacity: P(e, [t - 15, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    },
    bounce: {
      name: "Bounce",
      preview: "Elastic bounce entrance",
      isPro: !0,
      enter: (e) => ({
        transform: `translateY(${P(
          e,
          [0, 10, 13, 15],
          [100, -10, 5, 0],
          { extrapolateRight: "clamp" }
        )}px)`,
        opacity: P(e, [0, 10], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `translateY(${P(
          e,
          [t - 15, t - 13, t - 10, t],
          [0, 5, -10, 100],
          { extrapolateLeft: "clamp" }
        )}px)`,
        opacity: P(e, [t - 10, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    },
    flipX: {
      name: "Flip",
      preview: "3D flip around X axis",
      isPro: !0,
      enter: (e) => ({
        transform: `perspective(400px) rotateX(${P(
          e,
          [0, 15],
          [90, 0],
          { extrapolateRight: "clamp" }
        )}deg)`,
        opacity: P(e, [0, 5, 15], [0, 0.7, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `perspective(400px) rotateX(${P(
          e,
          [t - 15, t],
          [0, -90],
          { extrapolateLeft: "clamp" }
        )}deg)`,
        opacity: P(
          e,
          [t - 15, t - 5, t],
          [1, 0.7, 0],
          {
            extrapolateLeft: "clamp"
          }
        )
      })
    },
    zoomBlur: {
      name: "Zoom",
      preview: "Zoom with blur effect",
      isPro: !0,
      enter: (e) => ({
        transform: `scale(${P(e, [0, 15], [1.5, 1], {
          extrapolateRight: "clamp"
        })})`,
        opacity: P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        }),
        filter: `blur(${P(e, [0, 15], [10, 0], {
          extrapolateRight: "clamp"
        })}px)`
      }),
      exit: (e, t) => ({
        transform: `scale(${P(
          e,
          [t - 15, t],
          [1, 1.5],
          { extrapolateLeft: "clamp" }
        )})`,
        opacity: P(e, [t - 15, t], [1, 0], {
          extrapolateLeft: "clamp"
        }),
        filter: `blur(${P(e, [t - 15, t], [0, 10], {
          extrapolateLeft: "clamp"
        })}px)`
      })
    },
    slideUp: {
      name: "Slide",
      preview: "Modern slide from bottom",
      enter: (e) => ({
        transform: `translateY(${P(e, [0, 15], [30, 0], {
          extrapolateRight: "clamp"
        })}px)`,
        opacity: P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `translateY(${P(
          e,
          [t - 15, t],
          [0, -30],
          { extrapolateLeft: "clamp" }
        )}px)`,
        opacity: P(e, [t - 15, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    },
    snapRotate: {
      name: "Snap",
      preview: "Quick rotate with snap",
      isPro: !0,
      enter: (e) => ({
        transform: `rotate(${P(e, [0, 8, 12, 15], [-10, 5, -2, 0], {
          extrapolateRight: "clamp"
        })}deg) scale(${P(e, [0, 15], [0.8, 1], {
          extrapolateRight: "clamp"
        })})`,
        opacity: P(e, [0, 10], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `rotate(${P(
          e,
          [t - 15, t - 12, t - 8, t],
          [0, -2, 5, -10],
          { extrapolateLeft: "clamp" }
        )}deg) scale(${P(e, [t - 15, t], [1, 0.8], {
          extrapolateLeft: "clamp"
        })})`,
        opacity: P(e, [t - 10, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    },
    glitch: {
      name: "Glitch",
      preview: "Digital glitch effect",
      isPro: !0,
      enter: (e) => {
        const t = P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        }), r = e % 3 === 0 ? (Math.random() * 10 - 5) * (1 - t) : 0, n = e % 4 === 0 ? (Math.random() * 8 - 4) * (1 - t) : 0;
        return {
          transform: `translate(${r}px, ${n}px) scale(${P(
            e,
            [0, 3, 6, 10, 15],
            [0.9, 1.05, 0.95, 1.02, 1],
            { extrapolateRight: "clamp" }
          )})`,
          opacity: P(e, [0, 3, 5, 15], [0, 0.7, 0.8, 1], {
            extrapolateRight: "clamp"
          })
        };
      },
      exit: (e, t) => {
        const r = P(e, [t - 15, t], [0, 1], {
          extrapolateLeft: "clamp"
        }), n = (t - e) % 3 === 0 ? (Math.random() * 10 - 5) * r : 0, a = (t - e) % 4 === 0 ? (Math.random() * 8 - 4) * r : 0;
        return {
          transform: `translate(${n}px, ${a}px) scale(${P(
            e,
            [t - 15, t - 10, t - 6, t - 3, t],
            [1, 1.02, 0.95, 1.05, 0.9],
            { extrapolateLeft: "clamp" }
          )})`,
          opacity: P(
            e,
            [t - 15, t - 5, t - 3, t],
            [1, 0.8, 0.7, 0],
            {
              extrapolateLeft: "clamp"
            }
          )
        };
      }
    },
    swipeReveal: {
      name: "Swipe",
      preview: "Reveals content with a swipe",
      isPro: !0,
      enter: (e) => ({
        transform: `translateX(${P(e, [0, 15], [0, 0], {
          extrapolateRight: "clamp"
        })}px)`,
        opacity: 1,
        clipPath: `inset(0 ${P(e, [0, 15], [100, 0], {
          extrapolateRight: "clamp"
        })}% 0 0)`
      }),
      exit: (e, t) => ({
        transform: `translateX(${P(
          e,
          [t - 15, t],
          [0, 0],
          { extrapolateLeft: "clamp" }
        )}px)`,
        opacity: 1,
        clipPath: `inset(0 0 0 ${P(
          e,
          [t - 15, t],
          [0, 100],
          { extrapolateLeft: "clamp" }
        )}%)`
      })
    },
    floatIn: {
      name: "Float",
      preview: "Smooth floating entrance",
      enter: (e) => ({
        transform: `translate(${P(e, [0, 15], [10, 0], {
          extrapolateRight: "clamp"
        })}px, ${P(e, [0, 15], [-20, 0], {
          extrapolateRight: "clamp"
        })}px)`,
        opacity: P(e, [0, 15], [0, 1], {
          extrapolateRight: "clamp"
        })
      }),
      exit: (e, t) => ({
        transform: `translate(${P(
          e,
          [t - 15, t],
          [0, -10],
          { extrapolateLeft: "clamp" }
        )}px, ${P(e, [t - 15, t], [0, -20], {
          extrapolateLeft: "clamp"
        })}px)`,
        opacity: P(e, [t - 15, t], [1, 0], {
          extrapolateLeft: "clamp"
        })
      })
    }
  };
  function ua() {
    return process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
  }
  function ys(e) {
    return e.startsWith("http://") || e.startsWith("https://") ? e : e.startsWith("/") ? `${ua()}${e}` : `${ua()}/${e}`;
  }
  const Mi = ({
    overlay: e,
    baseUrl: t
  }) => {
    var h;
    const r = mr(), { playerMetadata: { fps: n } } = yr();
    let a = e.src;
    e.src.startsWith("/") && t ? a = `${t}${e.src}` : e.src.startsWith("/") && (a = ys(e.src));
    let s = ((h = e.styles) == null ? void 0 : h.volume) ?? 1;
    const i = e.fadeInDuration ?? 0, o = e.fadeOutDuration ?? 0, u = i * n, l = o * n, c = e.durationInFrames;
    if (u > 0 && r < u) {
      const f = P(
        r,
        [0, u],
        [0, 1],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
          easing: Cr.out(Cr.cubic)
        }
      );
      s *= f;
    }
    if (l > 0 && r > c - l) {
      const f = P(
        r,
        [c - l, c],
        [1, 0],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
          easing: Cr.out(Cr.cubic)
        }
      );
      s *= f;
    }
    return /* @__PURE__ */ N.jsx(
      Ri,
      {
        src: a,
        startFrom: e.startFromSound || 0,
        volume: s,
        playbackRate: e.speed ?? 1
      }
    );
  }, Ni = ds(() => /* @__PURE__ */ N.jsx(
    "div",
    {
      style: {
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "rgba(0, 0, 0, 0.1)",
        borderRadius: "4px"
      },
      children: "加载中..."
    }
  ));
  Ni.displayName = "LoadingPlaceholder";
  const Bi = ds(() => /* @__PURE__ */ N.jsx(
    "div",
    {
      style: {
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "rgba(255, 0, 0, 0.1)",
        borderRadius: "4px"
      },
      children: "贴纸未找到"
    }
  ));
  Bi.displayName = "NotFoundPlaceholder";
  const Gi = ({ overlay: e }) => {
    var u, l, c, h, f;
    const { playerMetadata: { fps: t } } = yr(), r = mr(), n = r >= e.durationInFrames - t, a = !n && ((u = e.styles.animation) != null && u.enter) ? (l = Gt[e.styles.animation.enter]) == null ? void 0 : l.enter(
      r,
      e.durationInFrames
    ) : {}, s = n && ((c = e.styles.animation) != null && c.exit) ? (h = Gt[e.styles.animation.exit]) == null ? void 0 : h.exit(
      r,
      e.durationInFrames
    ) : {}, i = {
      width: "100%",
      height: "100%",
      objectPosition: e.styles.objectPosition,
      opacity: e.styles.opacity,
      transform: e.styles.transform || "none",
      filter: e.styles.filter || "none",
      borderRadius: e.styles.borderRadius || "0px",
      boxShadow: e.styles.boxShadow || "none",
      border: e.styles.border || "none",
      ...n ? s : a
    }, o = {
      width: "100%",
      height: "100%",
      padding: e.styles.padding || "0px",
      backgroundColor: e.styles.paddingBackgroundColor || "transparent",
      display: "flex",
      // Use flexbox for centering
      alignItems: "center",
      justifyContent: "center"
    };
    return /* @__PURE__ */ N.jsx(
      "div",
      {
        style: {
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: "4px",
          opacity: ((f = e.styles) == null ? void 0 : f.opacity) || 1,
          transform: `rotate(${e.rotation || 0}deg)`,
          ...o
        },
        children: /* @__PURE__ */ N.jsx(
          "img",
          {
            src: e.localSrc || e.src,
            style: {
              width: "100%",
              height: "100%",
              objectFit: "contain",
              ...i
            },
            alt: "贴纸"
          }
        )
      }
    );
  };
  /*! https://mths.be/codepointat v0.2.0 by @mathias */
  String.prototype.codePointAt || function() {
    var e = function() {
      try {
        var r = {}, n = Object.defineProperty, a = n(r, r, r) && n;
      } catch {
      }
      return a;
    }(), t = function(r) {
      if (this == null)
        throw TypeError();
      var n = String(this), a = n.length, s = r ? Number(r) : 0;
      if (s != s && (s = 0), !(s < 0 || s >= a)) {
        var i = n.charCodeAt(s), o;
        return (
          // check if it’s the start of a surrogate pair
          i >= 55296 && i <= 56319 && // high surrogate
          a > s + 1 && (o = n.charCodeAt(s + 1), o >= 56320 && o <= 57343) ? (i - 55296) * 1024 + o - 56320 + 65536 : i
        );
      }
    };
    e ? e(String.prototype, "codePointAt", {
      value: t,
      configurable: !0,
      writable: !0
    }) : String.prototype.codePointAt = t;
  }();
  var Wn = 0, xs = -3;
  function Jt() {
    this.table = new Uint16Array(16), this.trans = new Uint16Array(288);
  }
  function Hi(e, t) {
    this.source = e, this.sourceIndex = 0, this.tag = 0, this.bitcount = 0, this.dest = t, this.destLen = 0, this.ltree = new Jt(), this.dtree = new Jt();
  }
  var bs = new Jt(), Ss = new Jt(), zn = new Uint8Array(30), jn = new Uint16Array(30), ks = new Uint8Array(30), Ts = new Uint16Array(30), Wi = new Uint8Array([
    16,
    17,
    18,
    0,
    8,
    7,
    9,
    6,
    10,
    5,
    11,
    4,
    12,
    3,
    13,
    2,
    14,
    1,
    15
  ]), la = new Jt(), Ne = new Uint8Array(320);
  function ws(e, t, r, n) {
    var a, s;
    for (a = 0; a < r; ++a)
      e[a] = 0;
    for (a = 0; a < 30 - r; ++a)
      e[a + r] = a / r | 0;
    for (s = n, a = 0; a < 30; ++a)
      t[a] = s, s += 1 << e[a];
  }
  function zi(e, t) {
    var r;
    for (r = 0; r < 7; ++r)
      e.table[r] = 0;
    for (e.table[7] = 24, e.table[8] = 152, e.table[9] = 112, r = 0; r < 24; ++r)
      e.trans[r] = 256 + r;
    for (r = 0; r < 144; ++r)
      e.trans[24 + r] = r;
    for (r = 0; r < 8; ++r)
      e.trans[168 + r] = 280 + r;
    for (r = 0; r < 112; ++r)
      e.trans[176 + r] = 144 + r;
    for (r = 0; r < 5; ++r)
      t.table[r] = 0;
    for (t.table[5] = 32, r = 0; r < 32; ++r)
      t.trans[r] = r;
  }
  var ca = new Uint16Array(16);
  function sn(e, t, r, n) {
    var a, s;
    for (a = 0; a < 16; ++a)
      e.table[a] = 0;
    for (a = 0; a < n; ++a)
      e.table[t[r + a]]++;
    for (e.table[0] = 0, s = 0, a = 0; a < 16; ++a)
      ca[a] = s, s += e.table[a];
    for (a = 0; a < n; ++a)
      t[r + a] && (e.trans[ca[t[r + a]]++] = a);
  }
  function ji(e) {
    e.bitcount-- || (e.tag = e.source[e.sourceIndex++], e.bitcount = 7);
    var t = e.tag & 1;
    return e.tag >>>= 1, t;
  }
  function Be(e, t, r) {
    if (!t)
      return r;
    for (; e.bitcount < 24; )
      e.tag |= e.source[e.sourceIndex++] << e.bitcount, e.bitcount += 8;
    var n = e.tag & 65535 >>> 16 - t;
    return e.tag >>>= t, e.bitcount -= t, n + r;
  }
  function gn(e, t) {
    for (; e.bitcount < 24; )
      e.tag |= e.source[e.sourceIndex++] << e.bitcount, e.bitcount += 8;
    var r = 0, n = 0, a = 0, s = e.tag;
    do
      n = 2 * n + (s & 1), s >>>= 1, ++a, r += t.table[a], n -= t.table[a];
    while (n >= 0);
    return e.tag = s, e.bitcount -= a, t.trans[r + n];
  }
  function Zi(e, t, r) {
    var n, a, s, i, o, u;
    for (n = Be(e, 5, 257), a = Be(e, 5, 1), s = Be(e, 4, 4), i = 0; i < 19; ++i)
      Ne[i] = 0;
    for (i = 0; i < s; ++i) {
      var l = Be(e, 3, 0);
      Ne[Wi[i]] = l;
    }
    for (sn(la, Ne, 0, 19), o = 0; o < n + a; ) {
      var c = gn(e, la);
      switch (c) {
        case 16:
          var h = Ne[o - 1];
          for (u = Be(e, 2, 3); u; --u)
            Ne[o++] = h;
          break;
        case 17:
          for (u = Be(e, 3, 3); u; --u)
            Ne[o++] = 0;
          break;
        case 18:
          for (u = Be(e, 7, 11); u; --u)
            Ne[o++] = 0;
          break;
        default:
          Ne[o++] = c;
          break;
      }
    }
    sn(t, Ne, 0, n), sn(r, Ne, n, a);
  }
  function fa(e, t, r) {
    for (; ; ) {
      var n = gn(e, t);
      if (n === 256)
        return Wn;
      if (n < 256)
        e.dest[e.destLen++] = n;
      else {
        var a, s, i, o;
        for (n -= 257, a = Be(e, zn[n], jn[n]), s = gn(e, r), i = e.destLen - Be(e, ks[s], Ts[s]), o = i; o < i + a; ++o)
          e.dest[e.destLen++] = e.dest[o];
      }
    }
  }
  function Vi(e) {
    for (var t, r, n; e.bitcount > 8; )
      e.sourceIndex--, e.bitcount -= 8;
    if (t = e.source[e.sourceIndex + 1], t = 256 * t + e.source[e.sourceIndex], r = e.source[e.sourceIndex + 3], r = 256 * r + e.source[e.sourceIndex + 2], t !== (~r & 65535))
      return xs;
    for (e.sourceIndex += 4, n = t; n; --n)
      e.dest[e.destLen++] = e.source[e.sourceIndex++];
    return e.bitcount = 0, Wn;
  }
  function qi(e, t) {
    var r = new Hi(e, t), n, a, s;
    do {
      switch (n = ji(r), a = Be(r, 2, 0), a) {
        case 0:
          s = Vi(r);
          break;
        case 1:
          s = fa(r, bs, Ss);
          break;
        case 2:
          Zi(r, r.ltree, r.dtree), s = fa(r, r.ltree, r.dtree);
          break;
        default:
          s = xs;
      }
      if (s !== Wn)
        throw new Error("Data error");
    } while (!n);
    return r.destLen < r.dest.length ? typeof r.dest.slice == "function" ? r.dest.slice(0, r.destLen) : r.dest.subarray(0, r.destLen) : r.dest;
  }
  zi(bs, Ss);
  ws(zn, jn, 4, 3);
  ws(ks, Ts, 2, 1);
  zn[28] = 0;
  jn[28] = 258;
  var $i = qi;
  function Rt(e, t, r, n, a) {
    return Math.pow(1 - a, 3) * e + 3 * Math.pow(1 - a, 2) * a * t + 3 * (1 - a) * Math.pow(a, 2) * r + Math.pow(a, 3) * n;
  }
  function ct() {
    this.x1 = Number.NaN, this.y1 = Number.NaN, this.x2 = Number.NaN, this.y2 = Number.NaN;
  }
  ct.prototype.isEmpty = function() {
    return isNaN(this.x1) || isNaN(this.y1) || isNaN(this.x2) || isNaN(this.y2);
  };
  ct.prototype.addPoint = function(e, t) {
    typeof e == "number" && ((isNaN(this.x1) || isNaN(this.x2)) && (this.x1 = e, this.x2 = e), e < this.x1 && (this.x1 = e), e > this.x2 && (this.x2 = e)), typeof t == "number" && ((isNaN(this.y1) || isNaN(this.y2)) && (this.y1 = t, this.y2 = t), t < this.y1 && (this.y1 = t), t > this.y2 && (this.y2 = t));
  };
  ct.prototype.addX = function(e) {
    this.addPoint(e, null);
  };
  ct.prototype.addY = function(e) {
    this.addPoint(null, e);
  };
  ct.prototype.addBezier = function(e, t, r, n, a, s, i, o) {
    var u = [e, t], l = [r, n], c = [a, s], h = [i, o];
    this.addPoint(e, t), this.addPoint(i, o);
    for (var f = 0; f <= 1; f++) {
      var d = 6 * u[f] - 12 * l[f] + 6 * c[f], v = -3 * u[f] + 9 * l[f] - 9 * c[f] + 3 * h[f], S = 3 * l[f] - 3 * u[f];
      if (v === 0) {
        if (d === 0)
          continue;
        var m = -S / d;
        0 < m && m < 1 && (f === 0 && this.addX(Rt(u[f], l[f], c[f], h[f], m)), f === 1 && this.addY(Rt(u[f], l[f], c[f], h[f], m)));
        continue;
      }
      var x = Math.pow(d, 2) - 4 * S * v;
      if (!(x < 0)) {
        var E = (-d + Math.sqrt(x)) / (2 * v);
        0 < E && E < 1 && (f === 0 && this.addX(Rt(u[f], l[f], c[f], h[f], E)), f === 1 && this.addY(Rt(u[f], l[f], c[f], h[f], E)));
        var b = (-d - Math.sqrt(x)) / (2 * v);
        0 < b && b < 1 && (f === 0 && this.addX(Rt(u[f], l[f], c[f], h[f], b)), f === 1 && this.addY(Rt(u[f], l[f], c[f], h[f], b)));
      }
    }
  };
  ct.prototype.addQuad = function(e, t, r, n, a, s) {
    var i = e + 0.6666666666666666 * (r - e), o = t + 2 / 3 * (n - t), u = i + 1 / 3 * (a - e), l = o + 1 / 3 * (s - t);
    this.addBezier(e, t, i, o, u, l, a, s);
  };
  function ge() {
    this.commands = [], this.fill = "black", this.stroke = null, this.strokeWidth = 1;
  }
  ge.prototype.moveTo = function(e, t) {
    this.commands.push({
      type: "M",
      x: e,
      y: t
    });
  };
  ge.prototype.lineTo = function(e, t) {
    this.commands.push({
      type: "L",
      x: e,
      y: t
    });
  };
  ge.prototype.curveTo = ge.prototype.bezierCurveTo = function(e, t, r, n, a, s) {
    this.commands.push({
      type: "C",
      x1: e,
      y1: t,
      x2: r,
      y2: n,
      x: a,
      y: s
    });
  };
  ge.prototype.quadTo = ge.prototype.quadraticCurveTo = function(e, t, r, n) {
    this.commands.push({
      type: "Q",
      x1: e,
      y1: t,
      x: r,
      y: n
    });
  };
  ge.prototype.close = ge.prototype.closePath = function() {
    this.commands.push({
      type: "Z"
    });
  };
  ge.prototype.extend = function(e) {
    if (e.commands)
      e = e.commands;
    else if (e instanceof ct) {
      var t = e;
      this.moveTo(t.x1, t.y1), this.lineTo(t.x2, t.y1), this.lineTo(t.x2, t.y2), this.lineTo(t.x1, t.y2), this.close();
      return;
    }
    Array.prototype.push.apply(this.commands, e);
  };
  ge.prototype.getBoundingBox = function() {
    for (var e = new ct(), t = 0, r = 0, n = 0, a = 0, s = 0; s < this.commands.length; s++) {
      var i = this.commands[s];
      switch (i.type) {
        case "M":
          e.addPoint(i.x, i.y), t = n = i.x, r = a = i.y;
          break;
        case "L":
          e.addPoint(i.x, i.y), n = i.x, a = i.y;
          break;
        case "Q":
          e.addQuad(n, a, i.x1, i.y1, i.x, i.y), n = i.x, a = i.y;
          break;
        case "C":
          e.addBezier(n, a, i.x1, i.y1, i.x2, i.y2, i.x, i.y), n = i.x, a = i.y;
          break;
        case "Z":
          n = t, a = r;
          break;
        default:
          throw new Error("Unexpected path command " + i.type);
      }
    }
    return e.isEmpty() && e.addPoint(0, 0), e;
  };
  ge.prototype.draw = function(e) {
    e.beginPath();
    for (var t = 0; t < this.commands.length; t += 1) {
      var r = this.commands[t];
      r.type === "M" ? e.moveTo(r.x, r.y) : r.type === "L" ? e.lineTo(r.x, r.y) : r.type === "C" ? e.bezierCurveTo(r.x1, r.y1, r.x2, r.y2, r.x, r.y) : r.type === "Q" ? e.quadraticCurveTo(r.x1, r.y1, r.x, r.y) : r.type === "Z" && e.closePath();
    }
    this.fill && (e.fillStyle = this.fill, e.fill()), this.stroke && (e.strokeStyle = this.stroke, e.lineWidth = this.strokeWidth, e.stroke());
  };
  ge.prototype.toPathData = function(e) {
    e = e !== void 0 ? e : 2;
    function t(i) {
      return Math.round(i) === i ? "" + Math.round(i) : i.toFixed(e);
    }
    function r() {
      for (var i = arguments, o = "", u = 0; u < arguments.length; u += 1) {
        var l = i[u];
        l >= 0 && u > 0 && (o += " "), o += t(l);
      }
      return o;
    }
    for (var n = "", a = 0; a < this.commands.length; a += 1) {
      var s = this.commands[a];
      s.type === "M" ? n += "M" + r(s.x, s.y) : s.type === "L" ? n += "L" + r(s.x, s.y) : s.type === "C" ? n += "C" + r(s.x1, s.y1, s.x2, s.y2, s.x, s.y) : s.type === "Q" ? n += "Q" + r(s.x1, s.y1, s.x, s.y) : s.type === "Z" && (n += "Z");
    }
    return n;
  };
  ge.prototype.toSVG = function(e) {
    var t = '<path d="';
    return t += this.toPathData(e), t += '"', this.fill && this.fill !== "black" && (this.fill === null ? t += ' fill="none"' : t += ' fill="' + this.fill + '"'), this.stroke && (t += ' stroke="' + this.stroke + '" stroke-width="' + this.strokeWidth + '"'), t += "/>", t;
  };
  ge.prototype.toDOMElement = function(e) {
    var t = this.toPathData(e), r = document.createElementNS("http://www.w3.org/2000/svg", "path");
    return r.setAttribute("d", t), r;
  };
  function Cs(e) {
    throw new Error(e);
  }
  function ha(e, t) {
    e || Cs(t);
  }
  var G = { fail: Cs, argument: ha, assert: ha }, da = 32768, pa = 2147483648, Ht = {}, C = {}, H = {};
  function De(e) {
    return function() {
      return e;
    };
  }
  C.BYTE = function(e) {
    return G.argument(e >= 0 && e <= 255, "Byte value should be between 0 and 255."), [e];
  };
  H.BYTE = De(1);
  C.CHAR = function(e) {
    return [e.charCodeAt(0)];
  };
  H.CHAR = De(1);
  C.CHARARRAY = function(e) {
    typeof e > "u" && (e = "", console.warn("Undefined CHARARRAY encountered and treated as an empty string. This is probably caused by a missing glyph name."));
    for (var t = [], r = 0; r < e.length; r += 1)
      t[r] = e.charCodeAt(r);
    return t;
  };
  H.CHARARRAY = function(e) {
    return typeof e > "u" ? 0 : e.length;
  };
  C.USHORT = function(e) {
    return [e >> 8 & 255, e & 255];
  };
  H.USHORT = De(2);
  C.SHORT = function(e) {
    return e >= da && (e = -(2 * da - e)), [e >> 8 & 255, e & 255];
  };
  H.SHORT = De(2);
  C.UINT24 = function(e) {
    return [e >> 16 & 255, e >> 8 & 255, e & 255];
  };
  H.UINT24 = De(3);
  C.ULONG = function(e) {
    return [e >> 24 & 255, e >> 16 & 255, e >> 8 & 255, e & 255];
  };
  H.ULONG = De(4);
  C.LONG = function(e) {
    return e >= pa && (e = -(2 * pa - e)), [e >> 24 & 255, e >> 16 & 255, e >> 8 & 255, e & 255];
  };
  H.LONG = De(4);
  C.FIXED = C.ULONG;
  H.FIXED = H.ULONG;
  C.FWORD = C.SHORT;
  H.FWORD = H.SHORT;
  C.UFWORD = C.USHORT;
  H.UFWORD = H.USHORT;
  C.LONGDATETIME = function(e) {
    return [0, 0, 0, 0, e >> 24 & 255, e >> 16 & 255, e >> 8 & 255, e & 255];
  };
  H.LONGDATETIME = De(8);
  C.TAG = function(e) {
    return G.argument(e.length === 4, "Tag should be exactly 4 ASCII characters."), [
      e.charCodeAt(0),
      e.charCodeAt(1),
      e.charCodeAt(2),
      e.charCodeAt(3)
    ];
  };
  H.TAG = De(4);
  C.Card8 = C.BYTE;
  H.Card8 = H.BYTE;
  C.Card16 = C.USHORT;
  H.Card16 = H.USHORT;
  C.OffSize = C.BYTE;
  H.OffSize = H.BYTE;
  C.SID = C.USHORT;
  H.SID = H.USHORT;
  C.NUMBER = function(e) {
    return e >= -107 && e <= 107 ? [e + 139] : e >= 108 && e <= 1131 ? (e = e - 108, [(e >> 8) + 247, e & 255]) : e >= -1131 && e <= -108 ? (e = -e - 108, [(e >> 8) + 251, e & 255]) : e >= -32768 && e <= 32767 ? C.NUMBER16(e) : C.NUMBER32(e);
  };
  H.NUMBER = function(e) {
    return C.NUMBER(e).length;
  };
  C.NUMBER16 = function(e) {
    return [28, e >> 8 & 255, e & 255];
  };
  H.NUMBER16 = De(3);
  C.NUMBER32 = function(e) {
    return [29, e >> 24 & 255, e >> 16 & 255, e >> 8 & 255, e & 255];
  };
  H.NUMBER32 = De(5);
  C.REAL = function(e) {
    var t = e.toString(), r = /\.(\d*?)(?:9{5,20}|0{5,20})\d{0,2}(?:e(.+)|$)/.exec(t);
    if (r) {
      var n = parseFloat("1e" + ((r[2] ? +r[2] : 0) + r[1].length));
      t = (Math.round(e * n) / n).toString();
    }
    for (var a = "", s = 0, i = t.length; s < i; s += 1) {
      var o = t[s];
      o === "e" ? a += t[++s] === "-" ? "c" : "b" : o === "." ? a += "a" : o === "-" ? a += "e" : a += o;
    }
    a += a.length & 1 ? "f" : "ff";
    for (var u = [30], l = 0, c = a.length; l < c; l += 2)
      u.push(parseInt(a.substr(l, 2), 16));
    return u;
  };
  H.REAL = function(e) {
    return C.REAL(e).length;
  };
  C.NAME = C.CHARARRAY;
  H.NAME = H.CHARARRAY;
  C.STRING = C.CHARARRAY;
  H.STRING = H.CHARARRAY;
  Ht.UTF8 = function(e, t, r) {
    for (var n = [], a = r, s = 0; s < a; s++, t += 1)
      n[s] = e.getUint8(t);
    return String.fromCharCode.apply(null, n);
  };
  Ht.UTF16 = function(e, t, r) {
    for (var n = [], a = r / 2, s = 0; s < a; s++, t += 2)
      n[s] = e.getUint16(t);
    return String.fromCharCode.apply(null, n);
  };
  C.UTF16 = function(e) {
    for (var t = [], r = 0; r < e.length; r += 1) {
      var n = e.charCodeAt(r);
      t[t.length] = n >> 8 & 255, t[t.length] = n & 255;
    }
    return t;
  };
  H.UTF16 = function(e) {
    return e.length * 2;
  };
  var mn = {
    "x-mac-croatian": (
      // Python: 'mac_croatian'
      "ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊©⁄€‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ"
    ),
    "x-mac-cyrillic": (
      // Python: 'mac_cyrillic'
      "АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю"
    ),
    "x-mac-gaelic": (
      // http://unicode.org/Public/MAPPINGS/VENDORS/APPLE/GAELIC.TXT
      "ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØḂ±≤≥ḃĊċḊḋḞḟĠġṀæøṁṖṗɼƒſṠ«»… ÀÃÕŒœ–—“”‘’ṡẛÿŸṪ€‹›Ŷŷṫ·Ỳỳ⁊ÂÊÁËÈÍÎÏÌÓÔ♣ÒÚÛÙıÝýŴŵẄẅẀẁẂẃ"
    ),
    "x-mac-greek": (
      // Python: 'mac_greek'
      "Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦€ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ­"
    ),
    "x-mac-icelandic": (
      // Python: 'mac_iceland'
      "ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"
    ),
    "x-mac-inuit": (
      // http://unicode.org/Public/MAPPINGS/VENDORS/APPLE/INUIT.TXT
      "ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ°ᒡᒥᒦ•¶ᒧ®©™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ… ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł"
    ),
    "x-mac-ce": (
      // Python: 'mac_latin2'
      "ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ"
    ),
    macintosh: (
      // Python: 'mac_roman'
      "ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"
    ),
    "x-mac-romanian": (
      // Python: 'mac_romanian'
      "ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂȘ∞±≤≥¥µ∂∑∏π∫ªºΩăș¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›Țț‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"
    ),
    "x-mac-turkish": (
      // Python: 'mac_turkish'
      "ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙˆ˜¯˘˙˚¸˝˛ˇ"
    )
  };
  Ht.MACSTRING = function(e, t, r, n) {
    var a = mn[n];
    if (a !== void 0) {
      for (var s = "", i = 0; i < r; i++) {
        var o = e.getUint8(t + i);
        o <= 127 ? s += String.fromCharCode(o) : s += a[o & 127];
      }
      return s;
    }
  };
  var Fr = typeof WeakMap == "function" && /* @__PURE__ */ new WeakMap(), Or, Xi = function(e) {
    if (!Or) {
      Or = {};
      for (var t in mn)
        Or[t] = new String(t);
    }
    var r = Or[e];
    if (r !== void 0) {
      if (Fr) {
        var n = Fr.get(r);
        if (n !== void 0)
          return n;
      }
      var a = mn[e];
      if (a !== void 0) {
        for (var s = {}, i = 0; i < a.length; i++)
          s[a.charCodeAt(i)] = i + 128;
        return Fr && Fr.set(r, s), s;
      }
    }
  };
  C.MACSTRING = function(e, t) {
    var r = Xi(t);
    if (r !== void 0) {
      for (var n = [], a = 0; a < e.length; a++) {
        var s = e.charCodeAt(a);
        if (s >= 128 && (s = r[s], s === void 0))
          return;
        n[a] = s;
      }
      return n;
    }
  };
  H.MACSTRING = function(e, t) {
    var r = C.MACSTRING(e, t);
    return r !== void 0 ? r.length : 0;
  };
  function yn(e) {
    return e >= -128 && e <= 127;
  }
  function Yi(e, t, r) {
    for (var n = 0, a = e.length; t < a && n < 64 && e[t] === 0; )
      ++t, ++n;
    return r.push(128 | n - 1), t;
  }
  function Qi(e, t, r) {
    for (var n = 0, a = e.length, s = t; s < a && n < 64; ) {
      var i = e[s];
      if (!yn(i) || i === 0 && s + 1 < a && e[s + 1] === 0)
        break;
      ++s, ++n;
    }
    r.push(n - 1);
    for (var o = t; o < s; ++o)
      r.push(e[o] + 256 & 255);
    return s;
  }
  function Ji(e, t, r) {
    for (var n = 0, a = e.length, s = t; s < a && n < 64; ) {
      var i = e[s];
      if (i === 0 || yn(i) && s + 1 < a && yn(e[s + 1]))
        break;
      ++s, ++n;
    }
    r.push(64 | n - 1);
    for (var o = t; o < s; ++o) {
      var u = e[o];
      r.push(u + 65536 >> 8 & 255, u + 256 & 255);
    }
    return s;
  }
  C.VARDELTAS = function(e) {
    for (var t = 0, r = []; t < e.length; ) {
      var n = e[t];
      n === 0 ? t = Yi(e, t, r) : n >= -128 && n <= 127 ? t = Qi(e, t, r) : t = Ji(e, t, r);
    }
    return r;
  };
  C.INDEX = function(e) {
    for (var t = 1, r = [t], n = [], a = 0; a < e.length; a += 1) {
      var s = C.OBJECT(e[a]);
      Array.prototype.push.apply(n, s), t += s.length, r.push(t);
    }
    if (n.length === 0)
      return [0, 0];
    for (var i = [], o = 1 + Math.floor(Math.log(t) / Math.log(2)) / 8 | 0, u = [void 0, C.BYTE, C.USHORT, C.UINT24, C.ULONG][o], l = 0; l < r.length; l += 1) {
      var c = u(r[l]);
      Array.prototype.push.apply(i, c);
    }
    return Array.prototype.concat(
      C.Card16(e.length),
      C.OffSize(o),
      i,
      n
    );
  };
  H.INDEX = function(e) {
    return C.INDEX(e).length;
  };
  C.DICT = function(e) {
    for (var t = [], r = Object.keys(e), n = r.length, a = 0; a < n; a += 1) {
      var s = parseInt(r[a], 0), i = e[s];
      t = t.concat(C.OPERAND(i.value, i.type)), t = t.concat(C.OPERATOR(s));
    }
    return t;
  };
  H.DICT = function(e) {
    return C.DICT(e).length;
  };
  C.OPERATOR = function(e) {
    return e < 1200 ? [e] : [12, e - 1200];
  };
  C.OPERAND = function(e, t) {
    var r = [];
    if (Array.isArray(t))
      for (var n = 0; n < t.length; n += 1)
        G.argument(e.length === t.length, "Not enough arguments given for type" + t), r = r.concat(C.OPERAND(e[n], t[n]));
    else if (t === "SID")
      r = r.concat(C.NUMBER(e));
    else if (t === "offset")
      r = r.concat(C.NUMBER32(e));
    else if (t === "number")
      r = r.concat(C.NUMBER(e));
    else if (t === "real")
      r = r.concat(C.REAL(e));
    else
      throw new Error("Unknown operand type " + t);
    return r;
  };
  C.OP = C.BYTE;
  H.OP = H.BYTE;
  var Rr = typeof WeakMap == "function" && /* @__PURE__ */ new WeakMap();
  C.CHARSTRING = function(e) {
    if (Rr) {
      var t = Rr.get(e);
      if (t !== void 0)
        return t;
    }
    for (var r = [], n = e.length, a = 0; a < n; a += 1) {
      var s = e[a];
      r = r.concat(C[s.type](s.value));
    }
    return Rr && Rr.set(e, r), r;
  };
  H.CHARSTRING = function(e) {
    return C.CHARSTRING(e).length;
  };
  C.OBJECT = function(e) {
    var t = C[e.type];
    return G.argument(t !== void 0, "No encoding function for type " + e.type), t(e.value);
  };
  H.OBJECT = function(e) {
    var t = H[e.type];
    return G.argument(t !== void 0, "No sizeOf function for type " + e.type), t(e.value);
  };
  C.TABLE = function(e) {
    for (var t = [], r = e.fields.length, n = [], a = [], s = 0; s < r; s += 1) {
      var i = e.fields[s], o = C[i.type];
      G.argument(o !== void 0, "No encoding function for field type " + i.type + " (" + i.name + ")");
      var u = e[i.name];
      u === void 0 && (u = i.value);
      var l = o(u);
      i.type === "TABLE" ? (a.push(t.length), t = t.concat([0, 0]), n.push(l)) : t = t.concat(l);
    }
    for (var c = 0; c < n.length; c += 1) {
      var h = a[c], f = t.length;
      G.argument(f < 65536, "Table " + e.tableName + " too big."), t[h] = f >> 8, t[h + 1] = f & 255, t = t.concat(n[c]);
    }
    return t;
  };
  H.TABLE = function(e) {
    for (var t = 0, r = e.fields.length, n = 0; n < r; n += 1) {
      var a = e.fields[n], s = H[a.type];
      G.argument(s !== void 0, "No sizeOf function for field type " + a.type + " (" + a.name + ")");
      var i = e[a.name];
      i === void 0 && (i = a.value), t += s(i), a.type === "TABLE" && (t += 2);
    }
    return t;
  };
  C.RECORD = C.TABLE;
  H.RECORD = H.TABLE;
  C.LITERAL = function(e) {
    return e;
  };
  H.LITERAL = function(e) {
    return e.length;
  };
  function me(e, t, r) {
    if (t.length && (t[0].name !== "coverageFormat" || t[0].value === 1))
      for (var n = 0; n < t.length; n += 1) {
        var a = t[n];
        this[a.name] = a.value;
      }
    if (this.tableName = e, this.fields = t, r)
      for (var s = Object.keys(r), i = 0; i < s.length; i += 1) {
        var o = s[i], u = r[o];
        this[o] !== void 0 && (this[o] = u);
      }
  }
  me.prototype.encode = function() {
    return C.TABLE(this);
  };
  me.prototype.sizeOf = function() {
    return H.TABLE(this);
  };
  function Kt(e, t, r) {
    r === void 0 && (r = t.length);
    var n = new Array(t.length + 1);
    n[0] = { name: e + "Count", type: "USHORT", value: r };
    for (var a = 0; a < t.length; a++)
      n[a + 1] = { name: e + a, type: "USHORT", value: t[a] };
    return n;
  }
  function xn(e, t, r) {
    var n = t.length, a = new Array(n + 1);
    a[0] = { name: e + "Count", type: "USHORT", value: n };
    for (var s = 0; s < n; s++)
      a[s + 1] = { name: e + s, type: "TABLE", value: r(t[s], s) };
    return a;
  }
  function er(e, t, r) {
    var n = t.length, a = [];
    a[0] = { name: e + "Count", type: "USHORT", value: n };
    for (var s = 0; s < n; s++)
      a = a.concat(r(t[s], s));
    return a;
  }
  function Br(e) {
    e.format === 1 ? me.call(
      this,
      "coverageTable",
      [{ name: "coverageFormat", type: "USHORT", value: 1 }].concat(Kt("glyph", e.glyphs))
    ) : e.format === 2 ? me.call(
      this,
      "coverageTable",
      [{ name: "coverageFormat", type: "USHORT", value: 2 }].concat(er("rangeRecord", e.ranges, function(t) {
        return [
          { name: "startGlyphID", type: "USHORT", value: t.start },
          { name: "endGlyphID", type: "USHORT", value: t.end },
          { name: "startCoverageIndex", type: "USHORT", value: t.index }
        ];
      }))
    ) : G.assert(!1, "Coverage format must be 1 or 2.");
  }
  Br.prototype = Object.create(me.prototype);
  Br.prototype.constructor = Br;
  function Gr(e) {
    me.call(
      this,
      "scriptListTable",
      er("scriptRecord", e, function(t, r) {
        var n = t.script, a = n.defaultLangSys;
        return G.assert(!!a, "Unable to write GSUB: script " + t.tag + " has no default language system."), [
          { name: "scriptTag" + r, type: "TAG", value: t.tag },
          { name: "script" + r, type: "TABLE", value: new me("scriptTable", [
            { name: "defaultLangSys", type: "TABLE", value: new me("defaultLangSys", [
              { name: "lookupOrder", type: "USHORT", value: 0 },
              { name: "reqFeatureIndex", type: "USHORT", value: a.reqFeatureIndex }
            ].concat(Kt("featureIndex", a.featureIndexes))) }
          ].concat(er("langSys", n.langSysRecords, function(s, i) {
            var o = s.langSys;
            return [
              { name: "langSysTag" + i, type: "TAG", value: s.tag },
              { name: "langSys" + i, type: "TABLE", value: new me("langSys", [
                { name: "lookupOrder", type: "USHORT", value: 0 },
                { name: "reqFeatureIndex", type: "USHORT", value: o.reqFeatureIndex }
              ].concat(Kt("featureIndex", o.featureIndexes))) }
            ];
          }))) }
        ];
      })
    );
  }
  Gr.prototype = Object.create(me.prototype);
  Gr.prototype.constructor = Gr;
  function Hr(e) {
    me.call(
      this,
      "featureListTable",
      er("featureRecord", e, function(t, r) {
        var n = t.feature;
        return [
          { name: "featureTag" + r, type: "TAG", value: t.tag },
          { name: "feature" + r, type: "TABLE", value: new me("featureTable", [
            { name: "featureParams", type: "USHORT", value: n.featureParams }
          ].concat(Kt("lookupListIndex", n.lookupListIndexes))) }
        ];
      })
    );
  }
  Hr.prototype = Object.create(me.prototype);
  Hr.prototype.constructor = Hr;
  function Wr(e, t) {
    me.call(this, "lookupListTable", xn("lookup", e, function(r) {
      var n = t[r.lookupType];
      return G.assert(!!n, "Unable to write GSUB lookup type " + r.lookupType + " tables."), new me("lookupTable", [
        { name: "lookupType", type: "USHORT", value: r.lookupType },
        { name: "lookupFlag", type: "USHORT", value: r.lookupFlag }
      ].concat(xn("subtable", r.subtables, n)));
    }));
  }
  Wr.prototype = Object.create(me.prototype);
  Wr.prototype.constructor = Wr;
  var O = {
    Table: me,
    Record: me,
    Coverage: Br,
    ScriptList: Gr,
    FeatureList: Hr,
    LookupList: Wr,
    ushortList: Kt,
    tableList: xn,
    recordList: er
  };
  function va(e, t) {
    return e.getUint8(t);
  }
  function zr(e, t) {
    return e.getUint16(t, !1);
  }
  function Ki(e, t) {
    return e.getInt16(t, !1);
  }
  function Zn(e, t) {
    return e.getUint32(t, !1);
  }
  function Es(e, t) {
    var r = e.getInt16(t, !1), n = e.getUint16(t + 2, !1);
    return r + n / 65535;
  }
  function eo(e, t) {
    for (var r = "", n = t; n < t + 4; n += 1)
      r += String.fromCharCode(e.getInt8(n));
    return r;
  }
  function to(e, t, r) {
    for (var n = 0, a = 0; a < r; a += 1)
      n <<= 8, n += e.getUint8(t + a);
    return n;
  }
  function ro(e, t, r) {
    for (var n = [], a = t; a < r; a += 1)
      n.push(e.getUint8(a));
    return n;
  }
  function no(e) {
    for (var t = "", r = 0; r < e.length; r += 1)
      t += String.fromCharCode(e[r]);
    return t;
  }
  var ao = {
    byte: 1,
    uShort: 2,
    short: 2,
    uLong: 4,
    fixed: 4,
    longDateTime: 8,
    tag: 4
  };
  function p(e, t) {
    this.data = e, this.offset = t, this.relativeOffset = 0;
  }
  p.prototype.parseByte = function() {
    var e = this.data.getUint8(this.offset + this.relativeOffset);
    return this.relativeOffset += 1, e;
  };
  p.prototype.parseChar = function() {
    var e = this.data.getInt8(this.offset + this.relativeOffset);
    return this.relativeOffset += 1, e;
  };
  p.prototype.parseCard8 = p.prototype.parseByte;
  p.prototype.parseUShort = function() {
    var e = this.data.getUint16(this.offset + this.relativeOffset);
    return this.relativeOffset += 2, e;
  };
  p.prototype.parseCard16 = p.prototype.parseUShort;
  p.prototype.parseSID = p.prototype.parseUShort;
  p.prototype.parseOffset16 = p.prototype.parseUShort;
  p.prototype.parseShort = function() {
    var e = this.data.getInt16(this.offset + this.relativeOffset);
    return this.relativeOffset += 2, e;
  };
  p.prototype.parseF2Dot14 = function() {
    var e = this.data.getInt16(this.offset + this.relativeOffset) / 16384;
    return this.relativeOffset += 2, e;
  };
  p.prototype.parseULong = function() {
    var e = Zn(this.data, this.offset + this.relativeOffset);
    return this.relativeOffset += 4, e;
  };
  p.prototype.parseOffset32 = p.prototype.parseULong;
  p.prototype.parseFixed = function() {
    var e = Es(this.data, this.offset + this.relativeOffset);
    return this.relativeOffset += 4, e;
  };
  p.prototype.parseString = function(e) {
    var t = this.data, r = this.offset + this.relativeOffset, n = "";
    this.relativeOffset += e;
    for (var a = 0; a < e; a++)
      n += String.fromCharCode(t.getUint8(r + a));
    return n;
  };
  p.prototype.parseTag = function() {
    return this.parseString(4);
  };
  p.prototype.parseLongDateTime = function() {
    var e = Zn(this.data, this.offset + this.relativeOffset + 4);
    return e -= 2082844800, this.relativeOffset += 8, e;
  };
  p.prototype.parseVersion = function(e) {
    var t = zr(this.data, this.offset + this.relativeOffset), r = zr(this.data, this.offset + this.relativeOffset + 2);
    return this.relativeOffset += 4, e === void 0 && (e = 4096), t + r / e / 10;
  };
  p.prototype.skip = function(e, t) {
    t === void 0 && (t = 1), this.relativeOffset += ao[e] * t;
  };
  p.prototype.parseULongList = function(e) {
    e === void 0 && (e = this.parseULong());
    for (var t = new Array(e), r = this.data, n = this.offset + this.relativeOffset, a = 0; a < e; a++)
      t[a] = r.getUint32(n), n += 4;
    return this.relativeOffset += e * 4, t;
  };
  p.prototype.parseOffset16List = p.prototype.parseUShortList = function(e) {
    e === void 0 && (e = this.parseUShort());
    for (var t = new Array(e), r = this.data, n = this.offset + this.relativeOffset, a = 0; a < e; a++)
      t[a] = r.getUint16(n), n += 2;
    return this.relativeOffset += e * 2, t;
  };
  p.prototype.parseShortList = function(e) {
    for (var t = new Array(e), r = this.data, n = this.offset + this.relativeOffset, a = 0; a < e; a++)
      t[a] = r.getInt16(n), n += 2;
    return this.relativeOffset += e * 2, t;
  };
  p.prototype.parseByteList = function(e) {
    for (var t = new Array(e), r = this.data, n = this.offset + this.relativeOffset, a = 0; a < e; a++)
      t[a] = r.getUint8(n++);
    return this.relativeOffset += e, t;
  };
  p.prototype.parseList = function(e, t) {
    t || (t = e, e = this.parseUShort());
    for (var r = new Array(e), n = 0; n < e; n++)
      r[n] = t.call(this);
    return r;
  };
  p.prototype.parseList32 = function(e, t) {
    t || (t = e, e = this.parseULong());
    for (var r = new Array(e), n = 0; n < e; n++)
      r[n] = t.call(this);
    return r;
  };
  p.prototype.parseRecordList = function(e, t) {
    t || (t = e, e = this.parseUShort());
    for (var r = new Array(e), n = Object.keys(t), a = 0; a < e; a++) {
      for (var s = {}, i = 0; i < n.length; i++) {
        var o = n[i], u = t[o];
        s[o] = u.call(this);
      }
      r[a] = s;
    }
    return r;
  };
  p.prototype.parseRecordList32 = function(e, t) {
    t || (t = e, e = this.parseULong());
    for (var r = new Array(e), n = Object.keys(t), a = 0; a < e; a++) {
      for (var s = {}, i = 0; i < n.length; i++) {
        var o = n[i], u = t[o];
        s[o] = u.call(this);
      }
      r[a] = s;
    }
    return r;
  };
  p.prototype.parseStruct = function(e) {
    if (typeof e == "function")
      return e.call(this);
    for (var t = Object.keys(e), r = {}, n = 0; n < t.length; n++) {
      var a = t[n], s = e[a];
      r[a] = s.call(this);
    }
    return r;
  };
  p.prototype.parseValueRecord = function(e) {
    if (e === void 0 && (e = this.parseUShort()), e !== 0) {
      var t = {};
      return e & 1 && (t.xPlacement = this.parseShort()), e & 2 && (t.yPlacement = this.parseShort()), e & 4 && (t.xAdvance = this.parseShort()), e & 8 && (t.yAdvance = this.parseShort()), e & 16 && (t.xPlaDevice = void 0, this.parseShort()), e & 32 && (t.yPlaDevice = void 0, this.parseShort()), e & 64 && (t.xAdvDevice = void 0, this.parseShort()), e & 128 && (t.yAdvDevice = void 0, this.parseShort()), t;
    }
  };
  p.prototype.parseValueRecordList = function() {
    for (var e = this.parseUShort(), t = this.parseUShort(), r = new Array(t), n = 0; n < t; n++)
      r[n] = this.parseValueRecord(e);
    return r;
  };
  p.prototype.parsePointer = function(e) {
    var t = this.parseOffset16();
    if (t > 0)
      return new p(this.data, this.offset + t).parseStruct(e);
  };
  p.prototype.parsePointer32 = function(e) {
    var t = this.parseOffset32();
    if (t > 0)
      return new p(this.data, this.offset + t).parseStruct(e);
  };
  p.prototype.parseListOfLists = function(e) {
    for (var t = this.parseOffset16List(), r = t.length, n = this.relativeOffset, a = new Array(r), s = 0; s < r; s++) {
      var i = t[s];
      if (i === 0) {
        a[s] = void 0;
        continue;
      }
      if (this.relativeOffset = i, e) {
        for (var o = this.parseOffset16List(), u = new Array(o.length), l = 0; l < o.length; l++)
          this.relativeOffset = i + o[l], u[l] = e.call(this);
        a[s] = u;
      } else
        a[s] = this.parseUShortList();
    }
    return this.relativeOffset = n, a;
  };
  p.prototype.parseCoverage = function() {
    var e = this.offset + this.relativeOffset, t = this.parseUShort(), r = this.parseUShort();
    if (t === 1)
      return {
        format: 1,
        glyphs: this.parseUShortList(r)
      };
    if (t === 2) {
      for (var n = new Array(r), a = 0; a < r; a++)
        n[a] = {
          start: this.parseUShort(),
          end: this.parseUShort(),
          index: this.parseUShort()
        };
      return {
        format: 2,
        ranges: n
      };
    }
    throw new Error("0x" + e.toString(16) + ": Coverage format must be 1 or 2.");
  };
  p.prototype.parseClassDef = function() {
    var e = this.offset + this.relativeOffset, t = this.parseUShort();
    if (t === 1)
      return {
        format: 1,
        startGlyph: this.parseUShort(),
        classes: this.parseUShortList()
      };
    if (t === 2)
      return {
        format: 2,
        ranges: this.parseRecordList({
          start: p.uShort,
          end: p.uShort,
          classId: p.uShort
        })
      };
    throw new Error("0x" + e.toString(16) + ": ClassDef format must be 1 or 2.");
  };
  p.list = function(e, t) {
    return function() {
      return this.parseList(e, t);
    };
  };
  p.list32 = function(e, t) {
    return function() {
      return this.parseList32(e, t);
    };
  };
  p.recordList = function(e, t) {
    return function() {
      return this.parseRecordList(e, t);
    };
  };
  p.recordList32 = function(e, t) {
    return function() {
      return this.parseRecordList32(e, t);
    };
  };
  p.pointer = function(e) {
    return function() {
      return this.parsePointer(e);
    };
  };
  p.pointer32 = function(e) {
    return function() {
      return this.parsePointer32(e);
    };
  };
  p.tag = p.prototype.parseTag;
  p.byte = p.prototype.parseByte;
  p.uShort = p.offset16 = p.prototype.parseUShort;
  p.uShortList = p.prototype.parseUShortList;
  p.uLong = p.offset32 = p.prototype.parseULong;
  p.uLongList = p.prototype.parseULongList;
  p.struct = p.prototype.parseStruct;
  p.coverage = p.prototype.parseCoverage;
  p.classDef = p.prototype.parseClassDef;
  var ga = {
    reserved: p.uShort,
    reqFeatureIndex: p.uShort,
    featureIndexes: p.uShortList
  };
  p.prototype.parseScriptList = function() {
    return this.parsePointer(p.recordList({
      tag: p.tag,
      script: p.pointer({
        defaultLangSys: p.pointer(ga),
        langSysRecords: p.recordList({
          tag: p.tag,
          langSys: p.pointer(ga)
        })
      })
    })) || [];
  };
  p.prototype.parseFeatureList = function() {
    return this.parsePointer(p.recordList({
      tag: p.tag,
      feature: p.pointer({
        featureParams: p.offset16,
        lookupListIndexes: p.uShortList
      })
    })) || [];
  };
  p.prototype.parseLookupList = function(e) {
    return this.parsePointer(p.list(p.pointer(function() {
      var t = this.parseUShort();
      G.argument(1 <= t && t <= 9, "GPOS/GSUB lookup type " + t + " unknown.");
      var r = this.parseUShort(), n = r & 16;
      return {
        lookupType: t,
        lookupFlag: r,
        subtables: this.parseList(p.pointer(e[t])),
        markFilteringSet: n ? this.parseUShort() : void 0
      };
    }))) || [];
  };
  p.prototype.parseFeatureVariationsList = function() {
    return this.parsePointer32(function() {
      var e = this.parseUShort(), t = this.parseUShort();
      G.argument(e === 1 && t < 1, "GPOS/GSUB feature variations table unknown.");
      var r = this.parseRecordList32({
        conditionSetOffset: p.offset32,
        featureTableSubstitutionOffset: p.offset32
      });
      return r;
    }) || [];
  };
  var A = {
    getByte: va,
    getCard8: va,
    getUShort: zr,
    getCard16: zr,
    getShort: Ki,
    getULong: Zn,
    getFixed: Es,
    getTag: eo,
    getOffset: to,
    getBytes: ro,
    bytesToString: no,
    Parser: p
  };
  function so(e, t) {
    t.parseUShort(), e.length = t.parseULong(), e.language = t.parseULong();
    var r;
    e.groupCount = r = t.parseULong(), e.glyphIndexMap = {};
    for (var n = 0; n < r; n += 1)
      for (var a = t.parseULong(), s = t.parseULong(), i = t.parseULong(), o = a; o <= s; o += 1)
        e.glyphIndexMap[o] = i, i++;
  }
  function io(e, t, r, n, a) {
    e.length = t.parseUShort(), e.language = t.parseUShort();
    var s;
    e.segCount = s = t.parseUShort() >> 1, t.skip("uShort", 3), e.glyphIndexMap = {};
    for (var i = new A.Parser(r, n + a + 14), o = new A.Parser(r, n + a + 16 + s * 2), u = new A.Parser(r, n + a + 16 + s * 4), l = new A.Parser(r, n + a + 16 + s * 6), c = n + a + 16 + s * 8, h = 0; h < s - 1; h += 1)
      for (var f = void 0, d = i.parseUShort(), v = o.parseUShort(), S = u.parseShort(), m = l.parseUShort(), x = v; x <= d; x += 1)
        m !== 0 ? (c = l.offset + l.relativeOffset - 2, c += m, c += (x - v) * 2, f = A.getUShort(r, c), f !== 0 && (f = f + S & 65535)) : f = x + S & 65535, e.glyphIndexMap[x] = f;
  }
  function oo(e, t) {
    var r = {};
    r.version = A.getUShort(e, t), G.argument(r.version === 0, "cmap table version should be 0."), r.numTables = A.getUShort(e, t + 2);
    for (var n = -1, a = r.numTables - 1; a >= 0; a -= 1) {
      var s = A.getUShort(e, t + 4 + a * 8), i = A.getUShort(e, t + 4 + a * 8 + 2);
      if (s === 3 && (i === 0 || i === 1 || i === 10) || s === 0 && (i === 0 || i === 1 || i === 2 || i === 3 || i === 4)) {
        n = A.getULong(e, t + 4 + a * 8 + 4);
        break;
      }
    }
    if (n === -1)
      throw new Error("No valid cmap sub-tables found.");
    var o = new A.Parser(e, t + n);
    if (r.format = o.parseUShort(), r.format === 12)
      so(r, o);
    else if (r.format === 4)
      io(r, o, e, t, n);
    else
      throw new Error("Only format 4 and 12 cmap tables are supported (found format " + r.format + ").");
    return r;
  }
  function uo(e, t, r) {
    e.segments.push({
      end: t,
      start: t,
      delta: -(t - r),
      offset: 0,
      glyphIndex: r
    });
  }
  function lo(e) {
    e.segments.push({
      end: 65535,
      start: 65535,
      delta: 1,
      offset: 0
    });
  }
  function co(e) {
    var t = !0, r;
    for (r = e.length - 1; r > 0; r -= 1) {
      var n = e.get(r);
      if (n.unicode > 65535) {
        console.log("Adding CMAP format 12 (needed!)"), t = !1;
        break;
      }
    }
    var a = [
      { name: "version", type: "USHORT", value: 0 },
      { name: "numTables", type: "USHORT", value: t ? 1 : 2 },
      // CMAP 4 header
      { name: "platformID", type: "USHORT", value: 3 },
      { name: "encodingID", type: "USHORT", value: 1 },
      { name: "offset", type: "ULONG", value: t ? 12 : 20 }
    ];
    t || (a = a.concat([
      // CMAP 12 header
      { name: "cmap12PlatformID", type: "USHORT", value: 3 },
      // We encode only for PlatformID = 3 (Windows) because it is supported everywhere
      { name: "cmap12EncodingID", type: "USHORT", value: 10 },
      { name: "cmap12Offset", type: "ULONG", value: 0 }
    ])), a = a.concat([
      // CMAP 4 Subtable
      { name: "format", type: "USHORT", value: 4 },
      { name: "cmap4Length", type: "USHORT", value: 0 },
      { name: "language", type: "USHORT", value: 0 },
      { name: "segCountX2", type: "USHORT", value: 0 },
      { name: "searchRange", type: "USHORT", value: 0 },
      { name: "entrySelector", type: "USHORT", value: 0 },
      { name: "rangeShift", type: "USHORT", value: 0 }
    ]);
    var s = new O.Table("cmap", a);
    for (s.segments = [], r = 0; r < e.length; r += 1) {
      for (var i = e.get(r), o = 0; o < i.unicodes.length; o += 1)
        uo(s, i.unicodes[o], r);
      s.segments = s.segments.sort(function(E, b) {
        return E.start - b.start;
      });
    }
    lo(s);
    var u = s.segments.length, l = 0, c = [], h = [], f = [], d = [], v = [], S = [];
    for (r = 0; r < u; r += 1) {
      var m = s.segments[r];
      m.end <= 65535 && m.start <= 65535 ? (c = c.concat({ name: "end_" + r, type: "USHORT", value: m.end }), h = h.concat({ name: "start_" + r, type: "USHORT", value: m.start }), f = f.concat({ name: "idDelta_" + r, type: "SHORT", value: m.delta }), d = d.concat({ name: "idRangeOffset_" + r, type: "USHORT", value: m.offset }), m.glyphId !== void 0 && (v = v.concat({ name: "glyph_" + r, type: "USHORT", value: m.glyphId }))) : l += 1, !t && m.glyphIndex !== void 0 && (S = S.concat({ name: "cmap12Start_" + r, type: "ULONG", value: m.start }), S = S.concat({ name: "cmap12End_" + r, type: "ULONG", value: m.end }), S = S.concat({ name: "cmap12Glyph_" + r, type: "ULONG", value: m.glyphIndex }));
    }
    if (s.segCountX2 = (u - l) * 2, s.searchRange = Math.pow(2, Math.floor(Math.log(u - l) / Math.log(2))) * 2, s.entrySelector = Math.log(s.searchRange / 2) / Math.log(2), s.rangeShift = s.segCountX2 - s.searchRange, s.fields = s.fields.concat(c), s.fields.push({ name: "reservedPad", type: "USHORT", value: 0 }), s.fields = s.fields.concat(h), s.fields = s.fields.concat(f), s.fields = s.fields.concat(d), s.fields = s.fields.concat(v), s.cmap4Length = 14 + // Subtable header
    c.length * 2 + 2 + // reservedPad
    h.length * 2 + f.length * 2 + d.length * 2 + v.length * 2, !t) {
      var x = 16 + // Subtable header
      S.length * 4;
      s.cmap12Offset = 20 + s.cmap4Length, s.fields = s.fields.concat([
        { name: "cmap12Format", type: "USHORT", value: 12 },
        { name: "cmap12Reserved", type: "USHORT", value: 0 },
        { name: "cmap12Length", type: "ULONG", value: x },
        { name: "cmap12Language", type: "ULONG", value: 0 },
        { name: "cmap12nGroups", type: "ULONG", value: S.length / 3 }
      ]), s.fields = s.fields.concat(S);
    }
    return s;
  }
  var Fs = { parse: oo, make: co }, Ir = [
    ".notdef",
    "space",
    "exclam",
    "quotedbl",
    "numbersign",
    "dollar",
    "percent",
    "ampersand",
    "quoteright",
    "parenleft",
    "parenright",
    "asterisk",
    "plus",
    "comma",
    "hyphen",
    "period",
    "slash",
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "colon",
    "semicolon",
    "less",
    "equal",
    "greater",
    "question",
    "at",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "bracketleft",
    "backslash",
    "bracketright",
    "asciicircum",
    "underscore",
    "quoteleft",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "braceleft",
    "bar",
    "braceright",
    "asciitilde",
    "exclamdown",
    "cent",
    "sterling",
    "fraction",
    "yen",
    "florin",
    "section",
    "currency",
    "quotesingle",
    "quotedblleft",
    "guillemotleft",
    "guilsinglleft",
    "guilsinglright",
    "fi",
    "fl",
    "endash",
    "dagger",
    "daggerdbl",
    "periodcentered",
    "paragraph",
    "bullet",
    "quotesinglbase",
    "quotedblbase",
    "quotedblright",
    "guillemotright",
    "ellipsis",
    "perthousand",
    "questiondown",
    "grave",
    "acute",
    "circumflex",
    "tilde",
    "macron",
    "breve",
    "dotaccent",
    "dieresis",
    "ring",
    "cedilla",
    "hungarumlaut",
    "ogonek",
    "caron",
    "emdash",
    "AE",
    "ordfeminine",
    "Lslash",
    "Oslash",
    "OE",
    "ordmasculine",
    "ae",
    "dotlessi",
    "lslash",
    "oslash",
    "oe",
    "germandbls",
    "onesuperior",
    "logicalnot",
    "mu",
    "trademark",
    "Eth",
    "onehalf",
    "plusminus",
    "Thorn",
    "onequarter",
    "divide",
    "brokenbar",
    "degree",
    "thorn",
    "threequarters",
    "twosuperior",
    "registered",
    "minus",
    "eth",
    "multiply",
    "threesuperior",
    "copyright",
    "Aacute",
    "Acircumflex",
    "Adieresis",
    "Agrave",
    "Aring",
    "Atilde",
    "Ccedilla",
    "Eacute",
    "Ecircumflex",
    "Edieresis",
    "Egrave",
    "Iacute",
    "Icircumflex",
    "Idieresis",
    "Igrave",
    "Ntilde",
    "Oacute",
    "Ocircumflex",
    "Odieresis",
    "Ograve",
    "Otilde",
    "Scaron",
    "Uacute",
    "Ucircumflex",
    "Udieresis",
    "Ugrave",
    "Yacute",
    "Ydieresis",
    "Zcaron",
    "aacute",
    "acircumflex",
    "adieresis",
    "agrave",
    "aring",
    "atilde",
    "ccedilla",
    "eacute",
    "ecircumflex",
    "edieresis",
    "egrave",
    "iacute",
    "icircumflex",
    "idieresis",
    "igrave",
    "ntilde",
    "oacute",
    "ocircumflex",
    "odieresis",
    "ograve",
    "otilde",
    "scaron",
    "uacute",
    "ucircumflex",
    "udieresis",
    "ugrave",
    "yacute",
    "ydieresis",
    "zcaron",
    "exclamsmall",
    "Hungarumlautsmall",
    "dollaroldstyle",
    "dollarsuperior",
    "ampersandsmall",
    "Acutesmall",
    "parenleftsuperior",
    "parenrightsuperior",
    "266 ff",
    "onedotenleader",
    "zerooldstyle",
    "oneoldstyle",
    "twooldstyle",
    "threeoldstyle",
    "fouroldstyle",
    "fiveoldstyle",
    "sixoldstyle",
    "sevenoldstyle",
    "eightoldstyle",
    "nineoldstyle",
    "commasuperior",
    "threequartersemdash",
    "periodsuperior",
    "questionsmall",
    "asuperior",
    "bsuperior",
    "centsuperior",
    "dsuperior",
    "esuperior",
    "isuperior",
    "lsuperior",
    "msuperior",
    "nsuperior",
    "osuperior",
    "rsuperior",
    "ssuperior",
    "tsuperior",
    "ff",
    "ffi",
    "ffl",
    "parenleftinferior",
    "parenrightinferior",
    "Circumflexsmall",
    "hyphensuperior",
    "Gravesmall",
    "Asmall",
    "Bsmall",
    "Csmall",
    "Dsmall",
    "Esmall",
    "Fsmall",
    "Gsmall",
    "Hsmall",
    "Ismall",
    "Jsmall",
    "Ksmall",
    "Lsmall",
    "Msmall",
    "Nsmall",
    "Osmall",
    "Psmall",
    "Qsmall",
    "Rsmall",
    "Ssmall",
    "Tsmall",
    "Usmall",
    "Vsmall",
    "Wsmall",
    "Xsmall",
    "Ysmall",
    "Zsmall",
    "colonmonetary",
    "onefitted",
    "rupiah",
    "Tildesmall",
    "exclamdownsmall",
    "centoldstyle",
    "Lslashsmall",
    "Scaronsmall",
    "Zcaronsmall",
    "Dieresissmall",
    "Brevesmall",
    "Caronsmall",
    "Dotaccentsmall",
    "Macronsmall",
    "figuredash",
    "hypheninferior",
    "Ogoneksmall",
    "Ringsmall",
    "Cedillasmall",
    "questiondownsmall",
    "oneeighth",
    "threeeighths",
    "fiveeighths",
    "seveneighths",
    "onethird",
    "twothirds",
    "zerosuperior",
    "foursuperior",
    "fivesuperior",
    "sixsuperior",
    "sevensuperior",
    "eightsuperior",
    "ninesuperior",
    "zeroinferior",
    "oneinferior",
    "twoinferior",
    "threeinferior",
    "fourinferior",
    "fiveinferior",
    "sixinferior",
    "seveninferior",
    "eightinferior",
    "nineinferior",
    "centinferior",
    "dollarinferior",
    "periodinferior",
    "commainferior",
    "Agravesmall",
    "Aacutesmall",
    "Acircumflexsmall",
    "Atildesmall",
    "Adieresissmall",
    "Aringsmall",
    "AEsmall",
    "Ccedillasmall",
    "Egravesmall",
    "Eacutesmall",
    "Ecircumflexsmall",
    "Edieresissmall",
    "Igravesmall",
    "Iacutesmall",
    "Icircumflexsmall",
    "Idieresissmall",
    "Ethsmall",
    "Ntildesmall",
    "Ogravesmall",
    "Oacutesmall",
    "Ocircumflexsmall",
    "Otildesmall",
    "Odieresissmall",
    "OEsmall",
    "Oslashsmall",
    "Ugravesmall",
    "Uacutesmall",
    "Ucircumflexsmall",
    "Udieresissmall",
    "Yacutesmall",
    "Thornsmall",
    "Ydieresissmall",
    "001.000",
    "001.001",
    "001.002",
    "001.003",
    "Black",
    "Bold",
    "Book",
    "Light",
    "Medium",
    "Regular",
    "Roman",
    "Semibold"
  ], fo = [
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "space",
    "exclam",
    "quotedbl",
    "numbersign",
    "dollar",
    "percent",
    "ampersand",
    "quoteright",
    "parenleft",
    "parenright",
    "asterisk",
    "plus",
    "comma",
    "hyphen",
    "period",
    "slash",
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "colon",
    "semicolon",
    "less",
    "equal",
    "greater",
    "question",
    "at",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "bracketleft",
    "backslash",
    "bracketright",
    "asciicircum",
    "underscore",
    "quoteleft",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "braceleft",
    "bar",
    "braceright",
    "asciitilde",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "exclamdown",
    "cent",
    "sterling",
    "fraction",
    "yen",
    "florin",
    "section",
    "currency",
    "quotesingle",
    "quotedblleft",
    "guillemotleft",
    "guilsinglleft",
    "guilsinglright",
    "fi",
    "fl",
    "",
    "endash",
    "dagger",
    "daggerdbl",
    "periodcentered",
    "",
    "paragraph",
    "bullet",
    "quotesinglbase",
    "quotedblbase",
    "quotedblright",
    "guillemotright",
    "ellipsis",
    "perthousand",
    "",
    "questiondown",
    "",
    "grave",
    "acute",
    "circumflex",
    "tilde",
    "macron",
    "breve",
    "dotaccent",
    "dieresis",
    "",
    "ring",
    "cedilla",
    "",
    "hungarumlaut",
    "ogonek",
    "caron",
    "emdash",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "AE",
    "",
    "ordfeminine",
    "",
    "",
    "",
    "",
    "Lslash",
    "Oslash",
    "OE",
    "ordmasculine",
    "",
    "",
    "",
    "",
    "",
    "ae",
    "",
    "",
    "",
    "dotlessi",
    "",
    "",
    "lslash",
    "oslash",
    "oe",
    "germandbls"
  ], ho = [
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "space",
    "exclamsmall",
    "Hungarumlautsmall",
    "",
    "dollaroldstyle",
    "dollarsuperior",
    "ampersandsmall",
    "Acutesmall",
    "parenleftsuperior",
    "parenrightsuperior",
    "twodotenleader",
    "onedotenleader",
    "comma",
    "hyphen",
    "period",
    "fraction",
    "zerooldstyle",
    "oneoldstyle",
    "twooldstyle",
    "threeoldstyle",
    "fouroldstyle",
    "fiveoldstyle",
    "sixoldstyle",
    "sevenoldstyle",
    "eightoldstyle",
    "nineoldstyle",
    "colon",
    "semicolon",
    "commasuperior",
    "threequartersemdash",
    "periodsuperior",
    "questionsmall",
    "",
    "asuperior",
    "bsuperior",
    "centsuperior",
    "dsuperior",
    "esuperior",
    "",
    "",
    "isuperior",
    "",
    "",
    "lsuperior",
    "msuperior",
    "nsuperior",
    "osuperior",
    "",
    "",
    "rsuperior",
    "ssuperior",
    "tsuperior",
    "",
    "ff",
    "fi",
    "fl",
    "ffi",
    "ffl",
    "parenleftinferior",
    "",
    "parenrightinferior",
    "Circumflexsmall",
    "hyphensuperior",
    "Gravesmall",
    "Asmall",
    "Bsmall",
    "Csmall",
    "Dsmall",
    "Esmall",
    "Fsmall",
    "Gsmall",
    "Hsmall",
    "Ismall",
    "Jsmall",
    "Ksmall",
    "Lsmall",
    "Msmall",
    "Nsmall",
    "Osmall",
    "Psmall",
    "Qsmall",
    "Rsmall",
    "Ssmall",
    "Tsmall",
    "Usmall",
    "Vsmall",
    "Wsmall",
    "Xsmall",
    "Ysmall",
    "Zsmall",
    "colonmonetary",
    "onefitted",
    "rupiah",
    "Tildesmall",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "exclamdownsmall",
    "centoldstyle",
    "Lslashsmall",
    "",
    "",
    "Scaronsmall",
    "Zcaronsmall",
    "Dieresissmall",
    "Brevesmall",
    "Caronsmall",
    "",
    "Dotaccentsmall",
    "",
    "",
    "Macronsmall",
    "",
    "",
    "figuredash",
    "hypheninferior",
    "",
    "",
    "Ogoneksmall",
    "Ringsmall",
    "Cedillasmall",
    "",
    "",
    "",
    "onequarter",
    "onehalf",
    "threequarters",
    "questiondownsmall",
    "oneeighth",
    "threeeighths",
    "fiveeighths",
    "seveneighths",
    "onethird",
    "twothirds",
    "",
    "",
    "zerosuperior",
    "onesuperior",
    "twosuperior",
    "threesuperior",
    "foursuperior",
    "fivesuperior",
    "sixsuperior",
    "sevensuperior",
    "eightsuperior",
    "ninesuperior",
    "zeroinferior",
    "oneinferior",
    "twoinferior",
    "threeinferior",
    "fourinferior",
    "fiveinferior",
    "sixinferior",
    "seveninferior",
    "eightinferior",
    "nineinferior",
    "centinferior",
    "dollarinferior",
    "periodinferior",
    "commainferior",
    "Agravesmall",
    "Aacutesmall",
    "Acircumflexsmall",
    "Atildesmall",
    "Adieresissmall",
    "Aringsmall",
    "AEsmall",
    "Ccedillasmall",
    "Egravesmall",
    "Eacutesmall",
    "Ecircumflexsmall",
    "Edieresissmall",
    "Igravesmall",
    "Iacutesmall",
    "Icircumflexsmall",
    "Idieresissmall",
    "Ethsmall",
    "Ntildesmall",
    "Ogravesmall",
    "Oacutesmall",
    "Ocircumflexsmall",
    "Otildesmall",
    "Odieresissmall",
    "OEsmall",
    "Oslashsmall",
    "Ugravesmall",
    "Uacutesmall",
    "Ucircumflexsmall",
    "Udieresissmall",
    "Yacutesmall",
    "Thornsmall",
    "Ydieresissmall"
  ], pt = [
    ".notdef",
    ".null",
    "nonmarkingreturn",
    "space",
    "exclam",
    "quotedbl",
    "numbersign",
    "dollar",
    "percent",
    "ampersand",
    "quotesingle",
    "parenleft",
    "parenright",
    "asterisk",
    "plus",
    "comma",
    "hyphen",
    "period",
    "slash",
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "colon",
    "semicolon",
    "less",
    "equal",
    "greater",
    "question",
    "at",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "bracketleft",
    "backslash",
    "bracketright",
    "asciicircum",
    "underscore",
    "grave",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "braceleft",
    "bar",
    "braceright",
    "asciitilde",
    "Adieresis",
    "Aring",
    "Ccedilla",
    "Eacute",
    "Ntilde",
    "Odieresis",
    "Udieresis",
    "aacute",
    "agrave",
    "acircumflex",
    "adieresis",
    "atilde",
    "aring",
    "ccedilla",
    "eacute",
    "egrave",
    "ecircumflex",
    "edieresis",
    "iacute",
    "igrave",
    "icircumflex",
    "idieresis",
    "ntilde",
    "oacute",
    "ograve",
    "ocircumflex",
    "odieresis",
    "otilde",
    "uacute",
    "ugrave",
    "ucircumflex",
    "udieresis",
    "dagger",
    "degree",
    "cent",
    "sterling",
    "section",
    "bullet",
    "paragraph",
    "germandbls",
    "registered",
    "copyright",
    "trademark",
    "acute",
    "dieresis",
    "notequal",
    "AE",
    "Oslash",
    "infinity",
    "plusminus",
    "lessequal",
    "greaterequal",
    "yen",
    "mu",
    "partialdiff",
    "summation",
    "product",
    "pi",
    "integral",
    "ordfeminine",
    "ordmasculine",
    "Omega",
    "ae",
    "oslash",
    "questiondown",
    "exclamdown",
    "logicalnot",
    "radical",
    "florin",
    "approxequal",
    "Delta",
    "guillemotleft",
    "guillemotright",
    "ellipsis",
    "nonbreakingspace",
    "Agrave",
    "Atilde",
    "Otilde",
    "OE",
    "oe",
    "endash",
    "emdash",
    "quotedblleft",
    "quotedblright",
    "quoteleft",
    "quoteright",
    "divide",
    "lozenge",
    "ydieresis",
    "Ydieresis",
    "fraction",
    "currency",
    "guilsinglleft",
    "guilsinglright",
    "fi",
    "fl",
    "daggerdbl",
    "periodcentered",
    "quotesinglbase",
    "quotedblbase",
    "perthousand",
    "Acircumflex",
    "Ecircumflex",
    "Aacute",
    "Edieresis",
    "Egrave",
    "Iacute",
    "Icircumflex",
    "Idieresis",
    "Igrave",
    "Oacute",
    "Ocircumflex",
    "apple",
    "Ograve",
    "Uacute",
    "Ucircumflex",
    "Ugrave",
    "dotlessi",
    "circumflex",
    "tilde",
    "macron",
    "breve",
    "dotaccent",
    "ring",
    "cedilla",
    "hungarumlaut",
    "ogonek",
    "caron",
    "Lslash",
    "lslash",
    "Scaron",
    "scaron",
    "Zcaron",
    "zcaron",
    "brokenbar",
    "Eth",
    "eth",
    "Yacute",
    "yacute",
    "Thorn",
    "thorn",
    "minus",
    "multiply",
    "onesuperior",
    "twosuperior",
    "threesuperior",
    "onehalf",
    "onequarter",
    "threequarters",
    "franc",
    "Gbreve",
    "gbreve",
    "Idotaccent",
    "Scedilla",
    "scedilla",
    "Cacute",
    "cacute",
    "Ccaron",
    "ccaron",
    "dcroat"
  ];
  function Os(e) {
    this.font = e;
  }
  Os.prototype.charToGlyphIndex = function(e) {
    var t = e.codePointAt(0), r = this.font.glyphs;
    if (r) {
      for (var n = 0; n < r.length; n += 1)
        for (var a = r.get(n), s = 0; s < a.unicodes.length; s += 1)
          if (a.unicodes[s] === t)
            return n;
    }
    return null;
  };
  function Rs(e) {
    this.cmap = e;
  }
  Rs.prototype.charToGlyphIndex = function(e) {
    return this.cmap.glyphIndexMap[e.codePointAt(0)] || 0;
  };
  function jr(e, t) {
    this.encoding = e, this.charset = t;
  }
  jr.prototype.charToGlyphIndex = function(e) {
    var t = e.codePointAt(0), r = this.encoding[t];
    return this.charset.indexOf(r);
  };
  function Vn(e) {
    switch (e.version) {
      case 1:
        this.names = pt.slice();
        break;
      case 2:
        this.names = new Array(e.numberOfGlyphs);
        for (var t = 0; t < e.numberOfGlyphs; t++)
          e.glyphNameIndex[t] < pt.length ? this.names[t] = pt[e.glyphNameIndex[t]] : this.names[t] = e.names[e.glyphNameIndex[t] - pt.length];
        break;
      case 2.5:
        this.names = new Array(e.numberOfGlyphs);
        for (var r = 0; r < e.numberOfGlyphs; r++)
          this.names[r] = pt[r + e.glyphNameIndex[r]];
        break;
      case 3:
        this.names = [];
        break;
      default:
        this.names = [];
        break;
    }
  }
  Vn.prototype.nameToGlyphIndex = function(e) {
    return this.names.indexOf(e);
  };
  Vn.prototype.glyphIndexToName = function(e) {
    return this.names[e];
  };
  function po(e) {
    for (var t, r = e.tables.cmap.glyphIndexMap, n = Object.keys(r), a = 0; a < n.length; a += 1) {
      var s = n[a], i = r[s];
      t = e.glyphs.get(i), t.addUnicode(parseInt(s));
    }
    for (var o = 0; o < e.glyphs.length; o += 1)
      t = e.glyphs.get(o), e.cffEncoding ? e.isCIDFont ? t.name = "gid" + o : t.name = e.cffEncoding.charset[o] : e.glyphNames.names && (t.name = e.glyphNames.glyphIndexToName(o));
  }
  function vo(e) {
    e._IndexToUnicodeMap = {};
    for (var t = e.tables.cmap.glyphIndexMap, r = Object.keys(t), n = 0; n < r.length; n += 1) {
      var a = r[n], s = t[a];
      e._IndexToUnicodeMap[s] === void 0 ? e._IndexToUnicodeMap[s] = {
        unicodes: [parseInt(a)]
      } : e._IndexToUnicodeMap[s].unicodes.push(parseInt(a));
    }
  }
  function go(e, t) {
    t.lowMemory ? vo(e) : po(e);
  }
  function mo(e, t, r, n, a) {
    e.beginPath(), e.moveTo(t, r), e.lineTo(n, a), e.stroke();
  }
  var dt = { line: mo };
  function yo(e, t) {
    var r = t || new ge();
    return {
      configurable: !0,
      get: function() {
        return typeof r == "function" && (r = r()), r;
      },
      set: function(n) {
        r = n;
      }
    };
  }
  function Ee(e) {
    this.bindConstructorValues(e);
  }
  Ee.prototype.bindConstructorValues = function(e) {
    this.index = e.index || 0, this.name = e.name || null, this.unicode = e.unicode || void 0, this.unicodes = e.unicodes || e.unicode !== void 0 ? [e.unicode] : [], "xMin" in e && (this.xMin = e.xMin), "yMin" in e && (this.yMin = e.yMin), "xMax" in e && (this.xMax = e.xMax), "yMax" in e && (this.yMax = e.yMax), "advanceWidth" in e && (this.advanceWidth = e.advanceWidth), Object.defineProperty(this, "path", yo(this, e.path));
  };
  Ee.prototype.addUnicode = function(e) {
    this.unicodes.length === 0 && (this.unicode = e), this.unicodes.push(e);
  };
  Ee.prototype.getBoundingBox = function() {
    return this.path.getBoundingBox();
  };
  Ee.prototype.getPath = function(e, t, r, n, a) {
    e = e !== void 0 ? e : 0, t = t !== void 0 ? t : 0, r = r !== void 0 ? r : 72;
    var s, i;
    n || (n = {});
    var o = n.xScale, u = n.yScale;
    if (n.hinting && a && a.hinting && (i = this.path && a.hinting.exec(this, r)), i)
      s = a.hinting.getCommands(i), e = Math.round(e), t = Math.round(t), o = u = 1;
    else {
      s = this.path.commands;
      var l = 1 / (this.path.unitsPerEm || 1e3) * r;
      o === void 0 && (o = l), u === void 0 && (u = l);
    }
    for (var c = new ge(), h = 0; h < s.length; h += 1) {
      var f = s[h];
      f.type === "M" ? c.moveTo(e + f.x * o, t + -f.y * u) : f.type === "L" ? c.lineTo(e + f.x * o, t + -f.y * u) : f.type === "Q" ? c.quadraticCurveTo(
        e + f.x1 * o,
        t + -f.y1 * u,
        e + f.x * o,
        t + -f.y * u
      ) : f.type === "C" ? c.curveTo(
        e + f.x1 * o,
        t + -f.y1 * u,
        e + f.x2 * o,
        t + -f.y2 * u,
        e + f.x * o,
        t + -f.y * u
      ) : f.type === "Z" && c.closePath();
    }
    return c;
  };
  Ee.prototype.getContours = function() {
    if (this.points === void 0)
      return [];
    for (var e = [], t = [], r = 0; r < this.points.length; r += 1) {
      var n = this.points[r];
      t.push(n), n.lastPointOfContour && (e.push(t), t = []);
    }
    return G.argument(t.length === 0, "There are still points left in the current contour."), e;
  };
  Ee.prototype.getMetrics = function() {
    for (var e = this.path.commands, t = [], r = [], n = 0; n < e.length; n += 1) {
      var a = e[n];
      a.type !== "Z" && (t.push(a.x), r.push(a.y)), (a.type === "Q" || a.type === "C") && (t.push(a.x1), r.push(a.y1)), a.type === "C" && (t.push(a.x2), r.push(a.y2));
    }
    var s = {
      xMin: Math.min.apply(null, t),
      yMin: Math.min.apply(null, r),
      xMax: Math.max.apply(null, t),
      yMax: Math.max.apply(null, r),
      leftSideBearing: this.leftSideBearing
    };
    return isFinite(s.xMin) || (s.xMin = 0), isFinite(s.xMax) || (s.xMax = this.advanceWidth), isFinite(s.yMin) || (s.yMin = 0), isFinite(s.yMax) || (s.yMax = 0), s.rightSideBearing = this.advanceWidth - s.leftSideBearing - (s.xMax - s.xMin), s;
  };
  Ee.prototype.draw = function(e, t, r, n, a) {
    this.getPath(t, r, n, a).draw(e);
  };
  Ee.prototype.drawPoints = function(e, t, r, n) {
    function a(h, f, d, v) {
      e.beginPath();
      for (var S = 0; S < h.length; S += 1)
        e.moveTo(f + h[S].x * v, d + h[S].y * v), e.arc(f + h[S].x * v, d + h[S].y * v, 2, 0, Math.PI * 2, !1);
      e.closePath(), e.fill();
    }
    t = t !== void 0 ? t : 0, r = r !== void 0 ? r : 0, n = n !== void 0 ? n : 24;
    for (var s = 1 / this.path.unitsPerEm * n, i = [], o = [], u = this.path, l = 0; l < u.commands.length; l += 1) {
      var c = u.commands[l];
      c.x !== void 0 && i.push({ x: c.x, y: -c.y }), c.x1 !== void 0 && o.push({ x: c.x1, y: -c.y1 }), c.x2 !== void 0 && o.push({ x: c.x2, y: -c.y2 });
    }
    e.fillStyle = "blue", a(i, t, r, s), e.fillStyle = "red", a(o, t, r, s);
  };
  Ee.prototype.drawMetrics = function(e, t, r, n) {
    var a;
    t = t !== void 0 ? t : 0, r = r !== void 0 ? r : 0, n = n !== void 0 ? n : 24, a = 1 / this.path.unitsPerEm * n, e.lineWidth = 1, e.strokeStyle = "black", dt.line(e, t, -1e4, t, 1e4), dt.line(e, -1e4, r, 1e4, r);
    var s = this.xMin || 0, i = this.yMin || 0, o = this.xMax || 0, u = this.yMax || 0, l = this.advanceWidth || 0;
    e.strokeStyle = "blue", dt.line(e, t + s * a, -1e4, t + s * a, 1e4), dt.line(e, t + o * a, -1e4, t + o * a, 1e4), dt.line(e, -1e4, r + -i * a, 1e4, r + -i * a), dt.line(e, -1e4, r + -u * a, 1e4, r + -u * a), e.strokeStyle = "green", dt.line(e, t + l * a, -1e4, t + l * a, 1e4);
  };
  function _r(e, t, r) {
    Object.defineProperty(e, t, {
      get: function() {
        return e.path, e[r];
      },
      set: function(n) {
        e[r] = n;
      },
      enumerable: !0,
      configurable: !0
    });
  }
  function qn(e, t) {
    if (this.font = e, this.glyphs = {}, Array.isArray(t))
      for (var r = 0; r < t.length; r++) {
        var n = t[r];
        n.path.unitsPerEm = e.unitsPerEm, this.glyphs[r] = n;
      }
    this.length = t && t.length || 0;
  }
  qn.prototype.get = function(e) {
    if (this.glyphs[e] === void 0) {
      this.font._push(e), typeof this.glyphs[e] == "function" && (this.glyphs[e] = this.glyphs[e]());
      var t = this.glyphs[e], r = this.font._IndexToUnicodeMap[e];
      if (r)
        for (var n = 0; n < r.unicodes.length; n++)
          t.addUnicode(r.unicodes[n]);
      this.font.cffEncoding ? this.font.isCIDFont ? t.name = "gid" + e : t.name = this.font.cffEncoding.charset[e] : this.font.glyphNames.names && (t.name = this.font.glyphNames.glyphIndexToName(e)), this.glyphs[e].advanceWidth = this.font._hmtxTableData[e].advanceWidth, this.glyphs[e].leftSideBearing = this.font._hmtxTableData[e].leftSideBearing;
    } else
      typeof this.glyphs[e] == "function" && (this.glyphs[e] = this.glyphs[e]());
    return this.glyphs[e];
  };
  qn.prototype.push = function(e, t) {
    this.glyphs[e] = t, this.length++;
  };
  function xo(e, t) {
    return new Ee({ index: t, font: e });
  }
  function bo(e, t, r, n, a, s) {
    return function() {
      var i = new Ee({ index: t, font: e });
      return i.path = function() {
        r(i, n, a);
        var o = s(e.glyphs, i);
        return o.unitsPerEm = e.unitsPerEm, o;
      }, _r(i, "xMin", "_xMin"), _r(i, "xMax", "_xMax"), _r(i, "yMin", "_yMin"), _r(i, "yMax", "_yMax"), i;
    };
  }
  function So(e, t, r, n) {
    return function() {
      var a = new Ee({ index: t, font: e });
      return a.path = function() {
        var s = r(e, a, n);
        return s.unitsPerEm = e.unitsPerEm, s;
      }, a;
    };
  }
  var We = { GlyphSet: qn, glyphLoader: xo, ttfGlyphLoader: bo, cffGlyphLoader: So };
  function _s(e, t) {
    if (e === t)
      return !0;
    if (Array.isArray(e) && Array.isArray(t)) {
      if (e.length !== t.length)
        return !1;
      for (var r = 0; r < e.length; r += 1)
        if (!_s(e[r], t[r]))
          return !1;
      return !0;
    } else
      return !1;
  }
  function bn(e) {
    var t;
    return e.length < 1240 ? t = 107 : e.length < 33900 ? t = 1131 : t = 32768, t;
  }
  function Qe(e, t, r) {
    var n = [], a = [], s = A.getCard16(e, t), i, o;
    if (s !== 0) {
      var u = A.getByte(e, t + 2);
      i = t + (s + 1) * u + 2;
      for (var l = t + 3, c = 0; c < s + 1; c += 1)
        n.push(A.getOffset(e, l, u)), l += u;
      o = i + n[s];
    } else
      o = t + 2;
    for (var h = 0; h < n.length - 1; h += 1) {
      var f = A.getBytes(e, i + n[h], i + n[h + 1]);
      r && (f = r(f)), a.push(f);
    }
    return { objects: a, startOffset: t, endOffset: o };
  }
  function ko(e, t) {
    var r = [], n = A.getCard16(e, t), a, s;
    if (n !== 0) {
      var i = A.getByte(e, t + 2);
      a = t + (n + 1) * i + 2;
      for (var o = t + 3, u = 0; u < n + 1; u += 1)
        r.push(A.getOffset(e, o, i)), o += i;
      s = a + r[n];
    } else
      s = t + 2;
    return { offsets: r, startOffset: t, endOffset: s };
  }
  function To(e, t, r, n, a) {
    var s = A.getCard16(r, n), i = 0;
    if (s !== 0) {
      var o = A.getByte(r, n + 2);
      i = n + (s + 1) * o + 2;
    }
    var u = A.getBytes(r, i + t[e], i + t[e + 1]);
    return u;
  }
  function wo(e) {
    for (var t = "", r = 15, n = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", ".", "E", "E-", null, "-"]; ; ) {
      var a = e.parseByte(), s = a >> 4, i = a & 15;
      if (s === r || (t += n[s], i === r))
        break;
      t += n[i];
    }
    return parseFloat(t);
  }
  function Co(e, t) {
    var r, n, a, s;
    if (t === 28)
      return r = e.parseByte(), n = e.parseByte(), r << 8 | n;
    if (t === 29)
      return r = e.parseByte(), n = e.parseByte(), a = e.parseByte(), s = e.parseByte(), r << 24 | n << 16 | a << 8 | s;
    if (t === 30)
      return wo(e);
    if (t >= 32 && t <= 246)
      return t - 139;
    if (t >= 247 && t <= 250)
      return r = e.parseByte(), (t - 247) * 256 + r + 108;
    if (t >= 251 && t <= 254)
      return r = e.parseByte(), -(t - 251) * 256 - r - 108;
    throw new Error("Invalid b0 " + t);
  }
  function Eo(e) {
    for (var t = {}, r = 0; r < e.length; r += 1) {
      var n = e[r][0], a = e[r][1], s = void 0;
      if (a.length === 1 ? s = a[0] : s = a, t.hasOwnProperty(n) && !isNaN(t[n]))
        throw new Error("Object " + t + " already has key " + n);
      t[n] = s;
    }
    return t;
  }
  function Us(e, t, r) {
    t = t !== void 0 ? t : 0;
    var n = new A.Parser(e, t), a = [], s = [];
    for (r = r !== void 0 ? r : e.length; n.relativeOffset < r; ) {
      var i = n.parseByte();
      i <= 21 ? (i === 12 && (i = 1200 + n.parseByte()), a.push([i, s]), s = []) : s.push(Co(n, i));
    }
    return Eo(a);
  }
  function Yt(e, t) {
    return t <= 390 ? t = Ir[t] : t = e[t - 391], t;
  }
  function Ls(e, t, r) {
    for (var n = {}, a, s = 0; s < t.length; s += 1) {
      var i = t[s];
      if (Array.isArray(i.type)) {
        var o = [];
        o.length = i.type.length;
        for (var u = 0; u < i.type.length; u++)
          a = e[i.op] !== void 0 ? e[i.op][u] : void 0, a === void 0 && (a = i.value !== void 0 && i.value[u] !== void 0 ? i.value[u] : null), i.type[u] === "SID" && (a = Yt(r, a)), o[u] = a;
        n[i.name] = o;
      } else
        a = e[i.op], a === void 0 && (a = i.value !== void 0 ? i.value : null), i.type === "SID" && (a = Yt(r, a)), n[i.name] = a;
    }
    return n;
  }
  function Fo(e, t) {
    var r = {};
    return r.formatMajor = A.getCard8(e, t), r.formatMinor = A.getCard8(e, t + 1), r.size = A.getCard8(e, t + 2), r.offsetSize = A.getCard8(e, t + 3), r.startOffset = t, r.endOffset = t + 4, r;
  }
  var As = [
    { name: "version", op: 0, type: "SID" },
    { name: "notice", op: 1, type: "SID" },
    { name: "copyright", op: 1200, type: "SID" },
    { name: "fullName", op: 2, type: "SID" },
    { name: "familyName", op: 3, type: "SID" },
    { name: "weight", op: 4, type: "SID" },
    { name: "isFixedPitch", op: 1201, type: "number", value: 0 },
    { name: "italicAngle", op: 1202, type: "number", value: 0 },
    { name: "underlinePosition", op: 1203, type: "number", value: -100 },
    { name: "underlineThickness", op: 1204, type: "number", value: 50 },
    { name: "paintType", op: 1205, type: "number", value: 0 },
    { name: "charstringType", op: 1206, type: "number", value: 2 },
    {
      name: "fontMatrix",
      op: 1207,
      type: ["real", "real", "real", "real", "real", "real"],
      value: [1e-3, 0, 0, 1e-3, 0, 0]
    },
    { name: "uniqueId", op: 13, type: "number" },
    { name: "fontBBox", op: 5, type: ["number", "number", "number", "number"], value: [0, 0, 0, 0] },
    { name: "strokeWidth", op: 1208, type: "number", value: 0 },
    { name: "xuid", op: 14, type: [], value: null },
    { name: "charset", op: 15, type: "offset", value: 0 },
    { name: "encoding", op: 16, type: "offset", value: 0 },
    { name: "charStrings", op: 17, type: "offset", value: 0 },
    { name: "private", op: 18, type: ["number", "offset"], value: [0, 0] },
    { name: "ros", op: 1230, type: ["SID", "SID", "number"] },
    { name: "cidFontVersion", op: 1231, type: "number", value: 0 },
    { name: "cidFontRevision", op: 1232, type: "number", value: 0 },
    { name: "cidFontType", op: 1233, type: "number", value: 0 },
    { name: "cidCount", op: 1234, type: "number", value: 8720 },
    { name: "uidBase", op: 1235, type: "number" },
    { name: "fdArray", op: 1236, type: "offset" },
    { name: "fdSelect", op: 1237, type: "offset" },
    { name: "fontName", op: 1238, type: "SID" }
  ], Is = [
    { name: "subrs", op: 19, type: "offset", value: 0 },
    { name: "defaultWidthX", op: 20, type: "number", value: 0 },
    { name: "nominalWidthX", op: 21, type: "number", value: 0 }
  ];
  function Oo(e, t) {
    var r = Us(e, 0, e.byteLength);
    return Ls(r, As, t);
  }
  function Ds(e, t, r, n) {
    var a = Us(e, t, r);
    return Ls(a, Is, n);
  }
  function ma(e, t, r, n) {
    for (var a = [], s = 0; s < r.length; s += 1) {
      var i = new DataView(new Uint8Array(r[s]).buffer), o = Oo(i, n);
      o._subrs = [], o._subrsBias = 0, o._defaultWidthX = 0, o._nominalWidthX = 0;
      var u = o.private[0], l = o.private[1];
      if (u !== 0 && l !== 0) {
        var c = Ds(e, l + t, u, n);
        if (o._defaultWidthX = c.defaultWidthX, o._nominalWidthX = c.nominalWidthX, c.subrs !== 0) {
          var h = l + c.subrs, f = Qe(e, h + t);
          o._subrs = f.objects, o._subrsBias = bn(o._subrs);
        }
        o._privateDict = c;
      }
      a.push(o);
    }
    return a;
  }
  function Ro(e, t, r, n) {
    var a, s, i = new A.Parser(e, t);
    r -= 1;
    var o = [".notdef"], u = i.parseCard8();
    if (u === 0)
      for (var l = 0; l < r; l += 1)
        a = i.parseSID(), o.push(Yt(n, a));
    else if (u === 1)
      for (; o.length <= r; ) {
        a = i.parseSID(), s = i.parseCard8();
        for (var c = 0; c <= s; c += 1)
          o.push(Yt(n, a)), a += 1;
      }
    else if (u === 2)
      for (; o.length <= r; ) {
        a = i.parseSID(), s = i.parseCard16();
        for (var h = 0; h <= s; h += 1)
          o.push(Yt(n, a)), a += 1;
      }
    else
      throw new Error("Unknown charset format " + u);
    return o;
  }
  function _o(e, t, r) {
    var n, a = {}, s = new A.Parser(e, t), i = s.parseCard8();
    if (i === 0)
      for (var o = s.parseCard8(), u = 0; u < o; u += 1)
        n = s.parseCard8(), a[n] = u;
    else if (i === 1) {
      var l = s.parseCard8();
      n = 1;
      for (var c = 0; c < l; c += 1)
        for (var h = s.parseCard8(), f = s.parseCard8(), d = h; d <= h + f; d += 1)
          a[d] = n, n += 1;
    } else
      throw new Error("Unknown encoding format " + i);
    return new jr(a, r);
  }
  function ya(e, t, r) {
    var n, a, s, i, o = new ge(), u = [], l = 0, c = !1, h = !1, f = 0, d = 0, v, S, m, x;
    if (e.isCIDFont) {
      var E = e.tables.cff.topDict._fdSelect[t.index], b = e.tables.cff.topDict._fdArray[E];
      v = b._subrs, S = b._subrsBias, m = b._defaultWidthX, x = b._nominalWidthX;
    } else
      v = e.tables.cff.topDict._subrs, S = e.tables.cff.topDict._subrsBias, m = e.tables.cff.topDict._defaultWidthX, x = e.tables.cff.topDict._nominalWidthX;
    var M = m;
    function I(w, Q) {
      h && o.closePath(), o.moveTo(w, Q), h = !0;
    }
    function W() {
      var w;
      w = u.length % 2 !== 0, w && !c && (M = u.shift() + x), l += u.length >> 1, u.length = 0, c = !0;
    }
    function U(w) {
      for (var Q, q, ae, ne, K, te, ee, ie, le, ce, se, y, L = 0; L < w.length; ) {
        var Z = w[L];
        switch (L += 1, Z) {
          case 1:
            W();
            break;
          case 3:
            W();
            break;
          case 4:
            u.length > 1 && !c && (M = u.shift() + x, c = !0), d += u.pop(), I(f, d);
            break;
          case 5:
            for (; u.length > 0; )
              f += u.shift(), d += u.shift(), o.lineTo(f, d);
            break;
          case 6:
            for (; u.length > 0 && (f += u.shift(), o.lineTo(f, d), u.length !== 0); )
              d += u.shift(), o.lineTo(f, d);
            break;
          case 7:
            for (; u.length > 0 && (d += u.shift(), o.lineTo(f, d), u.length !== 0); )
              f += u.shift(), o.lineTo(f, d);
            break;
          case 8:
            for (; u.length > 0; )
              n = f + u.shift(), a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), f = s + u.shift(), d = i + u.shift(), o.curveTo(n, a, s, i, f, d);
            break;
          case 10:
            K = u.pop() + S, te = v[K], te && U(te);
            break;
          case 11:
            return;
          case 12:
            switch (Z = w[L], L += 1, Z) {
              case 35:
                n = f + u.shift(), a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), ee = s + u.shift(), ie = i + u.shift(), le = ee + u.shift(), ce = ie + u.shift(), se = le + u.shift(), y = ce + u.shift(), f = se + u.shift(), d = y + u.shift(), u.shift(), o.curveTo(n, a, s, i, ee, ie), o.curveTo(le, ce, se, y, f, d);
                break;
              case 34:
                n = f + u.shift(), a = d, s = n + u.shift(), i = a + u.shift(), ee = s + u.shift(), ie = i, le = ee + u.shift(), ce = i, se = le + u.shift(), y = d, f = se + u.shift(), o.curveTo(n, a, s, i, ee, ie), o.curveTo(le, ce, se, y, f, d);
                break;
              case 36:
                n = f + u.shift(), a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), ee = s + u.shift(), ie = i, le = ee + u.shift(), ce = i, se = le + u.shift(), y = ce + u.shift(), f = se + u.shift(), o.curveTo(n, a, s, i, ee, ie), o.curveTo(le, ce, se, y, f, d);
                break;
              case 37:
                n = f + u.shift(), a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), ee = s + u.shift(), ie = i + u.shift(), le = ee + u.shift(), ce = ie + u.shift(), se = le + u.shift(), y = ce + u.shift(), Math.abs(se - f) > Math.abs(y - d) ? f = se + u.shift() : d = y + u.shift(), o.curveTo(n, a, s, i, ee, ie), o.curveTo(le, ce, se, y, f, d);
                break;
              default:
                console.log("Glyph " + t.index + ": unknown operator 1200" + Z), u.length = 0;
            }
            break;
          case 14:
            u.length > 0 && !c && (M = u.shift() + x, c = !0), h && (o.closePath(), h = !1);
            break;
          case 18:
            W();
            break;
          case 19:
          // hintmask
          case 20:
            W(), L += l + 7 >> 3;
            break;
          case 21:
            u.length > 2 && !c && (M = u.shift() + x, c = !0), d += u.pop(), f += u.pop(), I(f, d);
            break;
          case 22:
            u.length > 1 && !c && (M = u.shift() + x, c = !0), f += u.pop(), I(f, d);
            break;
          case 23:
            W();
            break;
          case 24:
            for (; u.length > 2; )
              n = f + u.shift(), a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), f = s + u.shift(), d = i + u.shift(), o.curveTo(n, a, s, i, f, d);
            f += u.shift(), d += u.shift(), o.lineTo(f, d);
            break;
          case 25:
            for (; u.length > 6; )
              f += u.shift(), d += u.shift(), o.lineTo(f, d);
            n = f + u.shift(), a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), f = s + u.shift(), d = i + u.shift(), o.curveTo(n, a, s, i, f, d);
            break;
          case 26:
            for (u.length % 2 && (f += u.shift()); u.length > 0; )
              n = f, a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), f = s, d = i + u.shift(), o.curveTo(n, a, s, i, f, d);
            break;
          case 27:
            for (u.length % 2 && (d += u.shift()); u.length > 0; )
              n = f + u.shift(), a = d, s = n + u.shift(), i = a + u.shift(), f = s + u.shift(), d = i, o.curveTo(n, a, s, i, f, d);
            break;
          case 28:
            Q = w[L], q = w[L + 1], u.push((Q << 24 | q << 16) >> 16), L += 2;
            break;
          case 29:
            K = u.pop() + e.gsubrsBias, te = e.gsubrs[K], te && U(te);
            break;
          case 30:
            for (; u.length > 0 && (n = f, a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), f = s + u.shift(), d = i + (u.length === 1 ? u.shift() : 0), o.curveTo(n, a, s, i, f, d), u.length !== 0); )
              n = f + u.shift(), a = d, s = n + u.shift(), i = a + u.shift(), d = i + u.shift(), f = s + (u.length === 1 ? u.shift() : 0), o.curveTo(n, a, s, i, f, d);
            break;
          case 31:
            for (; u.length > 0 && (n = f + u.shift(), a = d, s = n + u.shift(), i = a + u.shift(), d = i + u.shift(), f = s + (u.length === 1 ? u.shift() : 0), o.curveTo(n, a, s, i, f, d), u.length !== 0); )
              n = f, a = d + u.shift(), s = n + u.shift(), i = a + u.shift(), f = s + u.shift(), d = i + (u.length === 1 ? u.shift() : 0), o.curveTo(n, a, s, i, f, d);
            break;
          default:
            Z < 32 ? console.log("Glyph " + t.index + ": unknown operator " + Z) : Z < 247 ? u.push(Z - 139) : Z < 251 ? (Q = w[L], L += 1, u.push((Z - 247) * 256 + Q + 108)) : Z < 255 ? (Q = w[L], L += 1, u.push(-(Z - 251) * 256 - Q - 108)) : (Q = w[L], q = w[L + 1], ae = w[L + 2], ne = w[L + 3], L += 4, u.push((Q << 24 | q << 16 | ae << 8 | ne) / 65536));
        }
      }
    }
    return U(r), t.advanceWidth = M, o;
  }
  function Uo(e, t, r, n) {
    var a = [], s, i = new A.Parser(e, t), o = i.parseCard8();
    if (o === 0)
      for (var u = 0; u < r; u++) {
        if (s = i.parseCard8(), s >= n)
          throw new Error("CFF table CID Font FDSelect has bad FD index value " + s + " (FD count " + n + ")");
        a.push(s);
      }
    else if (o === 3) {
      var l = i.parseCard16(), c = i.parseCard16();
      if (c !== 0)
        throw new Error("CFF Table CID Font FDSelect format 3 range has bad initial GID " + c);
      for (var h, f = 0; f < l; f++) {
        if (s = i.parseCard8(), h = i.parseCard16(), s >= n)
          throw new Error("CFF table CID Font FDSelect has bad FD index value " + s + " (FD count " + n + ")");
        if (h > r)
          throw new Error("CFF Table CID Font FDSelect format 3 range has bad GID " + h);
        for (; c < h; c++)
          a.push(s);
        c = h;
      }
      if (h !== r)
        throw new Error("CFF Table CID Font FDSelect format 3 range has bad final GID " + h);
    } else
      throw new Error("CFF Table CID Font FDSelect table has unsupported format " + o);
    return a;
  }
  function Lo(e, t, r, n) {
    r.tables.cff = {};
    var a = Fo(e, t), s = Qe(e, a.endOffset, A.bytesToString), i = Qe(e, s.endOffset), o = Qe(e, i.endOffset, A.bytesToString), u = Qe(e, o.endOffset);
    r.gsubrs = u.objects, r.gsubrsBias = bn(r.gsubrs);
    var l = ma(e, t, i.objects, o.objects);
    if (l.length !== 1)
      throw new Error("CFF table has too many fonts in 'FontSet' - count of fonts NameIndex.length = " + l.length);
    var c = l[0];
    if (r.tables.cff.topDict = c, c._privateDict && (r.defaultWidthX = c._privateDict.defaultWidthX, r.nominalWidthX = c._privateDict.nominalWidthX), c.ros[0] !== void 0 && c.ros[1] !== void 0 && (r.isCIDFont = !0), r.isCIDFont) {
      var h = c.fdArray, f = c.fdSelect;
      if (h === 0 || f === 0)
        throw new Error("Font is marked as a CID font, but FDArray and/or FDSelect information is missing");
      h += t;
      var d = Qe(e, h), v = ma(e, t, d.objects, o.objects);
      c._fdArray = v, f += t, c._fdSelect = Uo(e, f, r.numGlyphs, v.length);
    }
    var S = t + c.private[1], m = Ds(e, S, c.private[0], o.objects);
    if (r.defaultWidthX = m.defaultWidthX, r.nominalWidthX = m.nominalWidthX, m.subrs !== 0) {
      var x = S + m.subrs, E = Qe(e, x);
      r.subrs = E.objects, r.subrsBias = bn(r.subrs);
    } else
      r.subrs = [], r.subrsBias = 0;
    var b;
    n.lowMemory ? (b = ko(e, t + c.charStrings), r.nGlyphs = b.offsets.length) : (b = Qe(e, t + c.charStrings), r.nGlyphs = b.objects.length);
    var M = Ro(e, t + c.charset, r.nGlyphs, o.objects);
    if (c.encoding === 0 ? r.cffEncoding = new jr(fo, M) : c.encoding === 1 ? r.cffEncoding = new jr(ho, M) : r.cffEncoding = _o(e, t + c.encoding, M), r.encoding = r.encoding || r.cffEncoding, r.glyphs = new We.GlyphSet(r), n.lowMemory)
      r._push = function(U) {
        var w = To(U, b.offsets, e, t + c.charStrings);
        r.glyphs.push(U, We.cffGlyphLoader(r, U, ya, w));
      };
    else
      for (var I = 0; I < r.nGlyphs; I += 1) {
        var W = b.objects[I];
        r.glyphs.push(I, We.cffGlyphLoader(r, I, ya, W));
      }
  }
  function Ps(e, t) {
    var r, n = Ir.indexOf(e);
    return n >= 0 && (r = n), n = t.indexOf(e), n >= 0 ? r = n + Ir.length : (r = Ir.length + t.length, t.push(e)), r;
  }
  function Ao() {
    return new O.Record("Header", [
      { name: "major", type: "Card8", value: 1 },
      { name: "minor", type: "Card8", value: 0 },
      { name: "hdrSize", type: "Card8", value: 4 },
      { name: "major", type: "Card8", value: 1 }
    ]);
  }
  function Io(e) {
    var t = new O.Record("Name INDEX", [
      { name: "names", type: "INDEX", value: [] }
    ]);
    t.names = [];
    for (var r = 0; r < e.length; r += 1)
      t.names.push({ name: "name_" + r, type: "NAME", value: e[r] });
    return t;
  }
  function Ms(e, t, r) {
    for (var n = {}, a = 0; a < e.length; a += 1) {
      var s = e[a], i = t[s.name];
      i !== void 0 && !_s(i, s.value) && (s.type === "SID" && (i = Ps(i, r)), n[s.op] = { name: s.name, type: s.type, value: i });
    }
    return n;
  }
  function xa(e, t) {
    var r = new O.Record("Top DICT", [
      { name: "dict", type: "DICT", value: {} }
    ]);
    return r.dict = Ms(As, e, t), r;
  }
  function ba(e) {
    var t = new O.Record("Top DICT INDEX", [
      { name: "topDicts", type: "INDEX", value: [] }
    ]);
    return t.topDicts = [{ name: "topDict_0", type: "TABLE", value: e }], t;
  }
  function Do(e) {
    var t = new O.Record("String INDEX", [
      { name: "strings", type: "INDEX", value: [] }
    ]);
    t.strings = [];
    for (var r = 0; r < e.length; r += 1)
      t.strings.push({ name: "string_" + r, type: "STRING", value: e[r] });
    return t;
  }
  function Po() {
    return new O.Record("Global Subr INDEX", [
      { name: "subrs", type: "INDEX", value: [] }
    ]);
  }
  function Mo(e, t) {
    for (var r = new O.Record("Charsets", [
      { name: "format", type: "Card8", value: 0 }
    ]), n = 0; n < e.length; n += 1) {
      var a = e[n], s = Ps(a, t);
      r.fields.push({ name: "glyph_" + n, type: "SID", value: s });
    }
    return r;
  }
  function No(e) {
    var t = [], r = e.path;
    t.push({ name: "width", type: "NUMBER", value: e.advanceWidth });
    for (var n = 0, a = 0, s = 0; s < r.commands.length; s += 1) {
      var i = void 0, o = void 0, u = r.commands[s];
      if (u.type === "Q") {
        var l = 0.3333333333333333, c = 2 / 3;
        u = {
          type: "C",
          x: u.x,
          y: u.y,
          x1: Math.round(l * n + c * u.x1),
          y1: Math.round(l * a + c * u.y1),
          x2: Math.round(l * u.x + c * u.x1),
          y2: Math.round(l * u.y + c * u.y1)
        };
      }
      if (u.type === "M")
        i = Math.round(u.x - n), o = Math.round(u.y - a), t.push({ name: "dx", type: "NUMBER", value: i }), t.push({ name: "dy", type: "NUMBER", value: o }), t.push({ name: "rmoveto", type: "OP", value: 21 }), n = Math.round(u.x), a = Math.round(u.y);
      else if (u.type === "L")
        i = Math.round(u.x - n), o = Math.round(u.y - a), t.push({ name: "dx", type: "NUMBER", value: i }), t.push({ name: "dy", type: "NUMBER", value: o }), t.push({ name: "rlineto", type: "OP", value: 5 }), n = Math.round(u.x), a = Math.round(u.y);
      else if (u.type === "C") {
        var h = Math.round(u.x1 - n), f = Math.round(u.y1 - a), d = Math.round(u.x2 - u.x1), v = Math.round(u.y2 - u.y1);
        i = Math.round(u.x - u.x2), o = Math.round(u.y - u.y2), t.push({ name: "dx1", type: "NUMBER", value: h }), t.push({ name: "dy1", type: "NUMBER", value: f }), t.push({ name: "dx2", type: "NUMBER", value: d }), t.push({ name: "dy2", type: "NUMBER", value: v }), t.push({ name: "dx", type: "NUMBER", value: i }), t.push({ name: "dy", type: "NUMBER", value: o }), t.push({ name: "rrcurveto", type: "OP", value: 8 }), n = Math.round(u.x), a = Math.round(u.y);
      }
    }
    return t.push({ name: "endchar", type: "OP", value: 14 }), t;
  }
  function Bo(e) {
    for (var t = new O.Record("CharStrings INDEX", [
      { name: "charStrings", type: "INDEX", value: [] }
    ]), r = 0; r < e.length; r += 1) {
      var n = e.get(r), a = No(n);
      t.charStrings.push({ name: n.name, type: "CHARSTRING", value: a });
    }
    return t;
  }
  function Go(e, t) {
    var r = new O.Record("Private DICT", [
      { name: "dict", type: "DICT", value: {} }
    ]);
    return r.dict = Ms(Is, e, t), r;
  }
  function Ho(e, t) {
    for (var r = new O.Table("CFF ", [
      { name: "header", type: "RECORD" },
      { name: "nameIndex", type: "RECORD" },
      { name: "topDictIndex", type: "RECORD" },
      { name: "stringIndex", type: "RECORD" },
      { name: "globalSubrIndex", type: "RECORD" },
      { name: "charsets", type: "RECORD" },
      { name: "charStringsIndex", type: "RECORD" },
      { name: "privateDict", type: "RECORD" }
    ]), n = 1 / t.unitsPerEm, a = {
      version: t.version,
      fullName: t.fullName,
      familyName: t.familyName,
      weight: t.weightName,
      fontBBox: t.fontBBox || [0, 0, 0, 0],
      fontMatrix: [n, 0, 0, n, 0, 0],
      charset: 999,
      encoding: 0,
      charStrings: 999,
      private: [0, 999]
    }, s = {}, i = [], o, u = 1; u < e.length; u += 1)
      o = e.get(u), i.push(o.name);
    var l = [];
    r.header = Ao(), r.nameIndex = Io([t.postScriptName]);
    var c = xa(a, l);
    r.topDictIndex = ba(c), r.globalSubrIndex = Po(), r.charsets = Mo(i, l), r.charStringsIndex = Bo(e), r.privateDict = Go(s, l), r.stringIndex = Do(l);
    var h = r.header.sizeOf() + r.nameIndex.sizeOf() + r.topDictIndex.sizeOf() + r.stringIndex.sizeOf() + r.globalSubrIndex.sizeOf();
    return a.charset = h, a.encoding = 0, a.charStrings = a.charset + r.charsets.sizeOf(), a.private[1] = a.charStrings + r.charStringsIndex.sizeOf(), c = xa(a, l), r.topDictIndex = ba(c), r;
  }
  var Ns = { parse: Lo, make: Ho };
  function Wo(e, t) {
    var r = {}, n = new A.Parser(e, t);
    return r.version = n.parseVersion(), r.fontRevision = Math.round(n.parseFixed() * 1e3) / 1e3, r.checkSumAdjustment = n.parseULong(), r.magicNumber = n.parseULong(), G.argument(r.magicNumber === 1594834165, "Font header has wrong magic number."), r.flags = n.parseUShort(), r.unitsPerEm = n.parseUShort(), r.created = n.parseLongDateTime(), r.modified = n.parseLongDateTime(), r.xMin = n.parseShort(), r.yMin = n.parseShort(), r.xMax = n.parseShort(), r.yMax = n.parseShort(), r.macStyle = n.parseUShort(), r.lowestRecPPEM = n.parseUShort(), r.fontDirectionHint = n.parseShort(), r.indexToLocFormat = n.parseShort(), r.glyphDataFormat = n.parseShort(), r;
  }
  function zo(e) {
    var t = Math.round((/* @__PURE__ */ new Date()).getTime() / 1e3) + 2082844800, r = t;
    return e.createdTimestamp && (r = e.createdTimestamp + 2082844800), new O.Table("head", [
      { name: "version", type: "FIXED", value: 65536 },
      { name: "fontRevision", type: "FIXED", value: 65536 },
      { name: "checkSumAdjustment", type: "ULONG", value: 0 },
      { name: "magicNumber", type: "ULONG", value: 1594834165 },
      { name: "flags", type: "USHORT", value: 0 },
      { name: "unitsPerEm", type: "USHORT", value: 1e3 },
      { name: "created", type: "LONGDATETIME", value: r },
      { name: "modified", type: "LONGDATETIME", value: t },
      { name: "xMin", type: "SHORT", value: 0 },
      { name: "yMin", type: "SHORT", value: 0 },
      { name: "xMax", type: "SHORT", value: 0 },
      { name: "yMax", type: "SHORT", value: 0 },
      { name: "macStyle", type: "USHORT", value: 0 },
      { name: "lowestRecPPEM", type: "USHORT", value: 0 },
      { name: "fontDirectionHint", type: "SHORT", value: 2 },
      { name: "indexToLocFormat", type: "SHORT", value: 0 },
      { name: "glyphDataFormat", type: "SHORT", value: 0 }
    ], e);
  }
  var Bs = { parse: Wo, make: zo };
  function jo(e, t) {
    var r = {}, n = new A.Parser(e, t);
    return r.version = n.parseVersion(), r.ascender = n.parseShort(), r.descender = n.parseShort(), r.lineGap = n.parseShort(), r.advanceWidthMax = n.parseUShort(), r.minLeftSideBearing = n.parseShort(), r.minRightSideBearing = n.parseShort(), r.xMaxExtent = n.parseShort(), r.caretSlopeRise = n.parseShort(), r.caretSlopeRun = n.parseShort(), r.caretOffset = n.parseShort(), n.relativeOffset += 8, r.metricDataFormat = n.parseShort(), r.numberOfHMetrics = n.parseUShort(), r;
  }
  function Zo(e) {
    return new O.Table("hhea", [
      { name: "version", type: "FIXED", value: 65536 },
      { name: "ascender", type: "FWORD", value: 0 },
      { name: "descender", type: "FWORD", value: 0 },
      { name: "lineGap", type: "FWORD", value: 0 },
      { name: "advanceWidthMax", type: "UFWORD", value: 0 },
      { name: "minLeftSideBearing", type: "FWORD", value: 0 },
      { name: "minRightSideBearing", type: "FWORD", value: 0 },
      { name: "xMaxExtent", type: "FWORD", value: 0 },
      { name: "caretSlopeRise", type: "SHORT", value: 1 },
      { name: "caretSlopeRun", type: "SHORT", value: 0 },
      { name: "caretOffset", type: "SHORT", value: 0 },
      { name: "reserved1", type: "SHORT", value: 0 },
      { name: "reserved2", type: "SHORT", value: 0 },
      { name: "reserved3", type: "SHORT", value: 0 },
      { name: "reserved4", type: "SHORT", value: 0 },
      { name: "metricDataFormat", type: "SHORT", value: 0 },
      { name: "numberOfHMetrics", type: "USHORT", value: 0 }
    ], e);
  }
  var Gs = { parse: jo, make: Zo };
  function Vo(e, t, r, n, a) {
    for (var s, i, o = new A.Parser(e, t), u = 0; u < n; u += 1) {
      u < r && (s = o.parseUShort(), i = o.parseShort());
      var l = a.get(u);
      l.advanceWidth = s, l.leftSideBearing = i;
    }
  }
  function qo(e, t, r, n, a) {
    e._hmtxTableData = {};
    for (var s, i, o = new A.Parser(t, r), u = 0; u < a; u += 1)
      u < n && (s = o.parseUShort(), i = o.parseShort()), e._hmtxTableData[u] = {
        advanceWidth: s,
        leftSideBearing: i
      };
  }
  function $o(e, t, r, n, a, s, i) {
    i.lowMemory ? qo(e, t, r, n, a) : Vo(t, r, n, a, s);
  }
  function Xo(e) {
    for (var t = new O.Table("hmtx", []), r = 0; r < e.length; r += 1) {
      var n = e.get(r), a = n.advanceWidth || 0, s = n.leftSideBearing || 0;
      t.fields.push({ name: "advanceWidth_" + r, type: "USHORT", value: a }), t.fields.push({ name: "leftSideBearing_" + r, type: "SHORT", value: s });
    }
    return t;
  }
  var Hs = { parse: $o, make: Xo };
  function Yo(e) {
    for (var t = new O.Table("ltag", [
      { name: "version", type: "ULONG", value: 1 },
      { name: "flags", type: "ULONG", value: 0 },
      { name: "numTags", type: "ULONG", value: e.length }
    ]), r = "", n = 12 + e.length * 4, a = 0; a < e.length; ++a) {
      var s = r.indexOf(e[a]);
      s < 0 && (s = r.length, r += e[a]), t.fields.push({ name: "offset " + a, type: "USHORT", value: n + s }), t.fields.push({ name: "length " + a, type: "USHORT", value: e[a].length });
    }
    return t.fields.push({ name: "stringPool", type: "CHARARRAY", value: r }), t;
  }
  function Qo(e, t) {
    var r = new A.Parser(e, t), n = r.parseULong();
    G.argument(n === 1, "Unsupported ltag table version."), r.skip("uLong", 1);
    for (var a = r.parseULong(), s = [], i = 0; i < a; i++) {
      for (var o = "", u = t + r.parseUShort(), l = r.parseUShort(), c = u; c < u + l; ++c)
        o += String.fromCharCode(e.getInt8(c));
      s.push(o);
    }
    return s;
  }
  var Ws = { make: Yo, parse: Qo };
  function Jo(e, t) {
    var r = {}, n = new A.Parser(e, t);
    return r.version = n.parseVersion(), r.numGlyphs = n.parseUShort(), r.version === 1 && (r.maxPoints = n.parseUShort(), r.maxContours = n.parseUShort(), r.maxCompositePoints = n.parseUShort(), r.maxCompositeContours = n.parseUShort(), r.maxZones = n.parseUShort(), r.maxTwilightPoints = n.parseUShort(), r.maxStorage = n.parseUShort(), r.maxFunctionDefs = n.parseUShort(), r.maxInstructionDefs = n.parseUShort(), r.maxStackElements = n.parseUShort(), r.maxSizeOfInstructions = n.parseUShort(), r.maxComponentElements = n.parseUShort(), r.maxComponentDepth = n.parseUShort()), r;
  }
  function Ko(e) {
    return new O.Table("maxp", [
      { name: "version", type: "FIXED", value: 20480 },
      { name: "numGlyphs", type: "USHORT", value: e }
    ]);
  }
  var zs = { parse: Jo, make: Ko }, js = [
    "copyright",
    // 0
    "fontFamily",
    // 1
    "fontSubfamily",
    // 2
    "uniqueID",
    // 3
    "fullName",
    // 4
    "version",
    // 5
    "postScriptName",
    // 6
    "trademark",
    // 7
    "manufacturer",
    // 8
    "designer",
    // 9
    "description",
    // 10
    "manufacturerURL",
    // 11
    "designerURL",
    // 12
    "license",
    // 13
    "licenseURL",
    // 14
    "reserved",
    // 15
    "preferredFamily",
    // 16
    "preferredSubfamily",
    // 17
    "compatibleFullName",
    // 18
    "sampleText",
    // 19
    "postScriptFindFontName",
    // 20
    "wwsFamily",
    // 21
    "wwsSubfamily"
    // 22
  ], Zs = {
    0: "en",
    1: "fr",
    2: "de",
    3: "it",
    4: "nl",
    5: "sv",
    6: "es",
    7: "da",
    8: "pt",
    9: "no",
    10: "he",
    11: "ja",
    12: "ar",
    13: "fi",
    14: "el",
    15: "is",
    16: "mt",
    17: "tr",
    18: "hr",
    19: "zh-Hant",
    20: "ur",
    21: "hi",
    22: "th",
    23: "ko",
    24: "lt",
    25: "pl",
    26: "hu",
    27: "es",
    28: "lv",
    29: "se",
    30: "fo",
    31: "fa",
    32: "ru",
    33: "zh",
    34: "nl-BE",
    35: "ga",
    36: "sq",
    37: "ro",
    38: "cz",
    39: "sk",
    40: "si",
    41: "yi",
    42: "sr",
    43: "mk",
    44: "bg",
    45: "uk",
    46: "be",
    47: "uz",
    48: "kk",
    49: "az-Cyrl",
    50: "az-Arab",
    51: "hy",
    52: "ka",
    53: "mo",
    54: "ky",
    55: "tg",
    56: "tk",
    57: "mn-CN",
    58: "mn",
    59: "ps",
    60: "ks",
    61: "ku",
    62: "sd",
    63: "bo",
    64: "ne",
    65: "sa",
    66: "mr",
    67: "bn",
    68: "as",
    69: "gu",
    70: "pa",
    71: "or",
    72: "ml",
    73: "kn",
    74: "ta",
    75: "te",
    76: "si",
    77: "my",
    78: "km",
    79: "lo",
    80: "vi",
    81: "id",
    82: "tl",
    83: "ms",
    84: "ms-Arab",
    85: "am",
    86: "ti",
    87: "om",
    88: "so",
    89: "sw",
    90: "rw",
    91: "rn",
    92: "ny",
    93: "mg",
    94: "eo",
    128: "cy",
    129: "eu",
    130: "ca",
    131: "la",
    132: "qu",
    133: "gn",
    134: "ay",
    135: "tt",
    136: "ug",
    137: "dz",
    138: "jv",
    139: "su",
    140: "gl",
    141: "af",
    142: "br",
    143: "iu",
    144: "gd",
    145: "gv",
    146: "ga",
    147: "to",
    148: "el-polyton",
    149: "kl",
    150: "az",
    151: "nn"
  }, eu = {
    0: 0,
    // langEnglish → smRoman
    1: 0,
    // langFrench → smRoman
    2: 0,
    // langGerman → smRoman
    3: 0,
    // langItalian → smRoman
    4: 0,
    // langDutch → smRoman
    5: 0,
    // langSwedish → smRoman
    6: 0,
    // langSpanish → smRoman
    7: 0,
    // langDanish → smRoman
    8: 0,
    // langPortuguese → smRoman
    9: 0,
    // langNorwegian → smRoman
    10: 5,
    // langHebrew → smHebrew
    11: 1,
    // langJapanese → smJapanese
    12: 4,
    // langArabic → smArabic
    13: 0,
    // langFinnish → smRoman
    14: 6,
    // langGreek → smGreek
    15: 0,
    // langIcelandic → smRoman (modified)
    16: 0,
    // langMaltese → smRoman
    17: 0,
    // langTurkish → smRoman (modified)
    18: 0,
    // langCroatian → smRoman (modified)
    19: 2,
    // langTradChinese → smTradChinese
    20: 4,
    // langUrdu → smArabic
    21: 9,
    // langHindi → smDevanagari
    22: 21,
    // langThai → smThai
    23: 3,
    // langKorean → smKorean
    24: 29,
    // langLithuanian → smCentralEuroRoman
    25: 29,
    // langPolish → smCentralEuroRoman
    26: 29,
    // langHungarian → smCentralEuroRoman
    27: 29,
    // langEstonian → smCentralEuroRoman
    28: 29,
    // langLatvian → smCentralEuroRoman
    29: 0,
    // langSami → smRoman
    30: 0,
    // langFaroese → smRoman (modified)
    31: 4,
    // langFarsi → smArabic (modified)
    32: 7,
    // langRussian → smCyrillic
    33: 25,
    // langSimpChinese → smSimpChinese
    34: 0,
    // langFlemish → smRoman
    35: 0,
    // langIrishGaelic → smRoman (modified)
    36: 0,
    // langAlbanian → smRoman
    37: 0,
    // langRomanian → smRoman (modified)
    38: 29,
    // langCzech → smCentralEuroRoman
    39: 29,
    // langSlovak → smCentralEuroRoman
    40: 0,
    // langSlovenian → smRoman (modified)
    41: 5,
    // langYiddish → smHebrew
    42: 7,
    // langSerbian → smCyrillic
    43: 7,
    // langMacedonian → smCyrillic
    44: 7,
    // langBulgarian → smCyrillic
    45: 7,
    // langUkrainian → smCyrillic (modified)
    46: 7,
    // langByelorussian → smCyrillic
    47: 7,
    // langUzbek → smCyrillic
    48: 7,
    // langKazakh → smCyrillic
    49: 7,
    // langAzerbaijani → smCyrillic
    50: 4,
    // langAzerbaijanAr → smArabic
    51: 24,
    // langArmenian → smArmenian
    52: 23,
    // langGeorgian → smGeorgian
    53: 7,
    // langMoldavian → smCyrillic
    54: 7,
    // langKirghiz → smCyrillic
    55: 7,
    // langTajiki → smCyrillic
    56: 7,
    // langTurkmen → smCyrillic
    57: 27,
    // langMongolian → smMongolian
    58: 7,
    // langMongolianCyr → smCyrillic
    59: 4,
    // langPashto → smArabic
    60: 4,
    // langKurdish → smArabic
    61: 4,
    // langKashmiri → smArabic
    62: 4,
    // langSindhi → smArabic
    63: 26,
    // langTibetan → smTibetan
    64: 9,
    // langNepali → smDevanagari
    65: 9,
    // langSanskrit → smDevanagari
    66: 9,
    // langMarathi → smDevanagari
    67: 13,
    // langBengali → smBengali
    68: 13,
    // langAssamese → smBengali
    69: 11,
    // langGujarati → smGujarati
    70: 10,
    // langPunjabi → smGurmukhi
    71: 12,
    // langOriya → smOriya
    72: 17,
    // langMalayalam → smMalayalam
    73: 16,
    // langKannada → smKannada
    74: 14,
    // langTamil → smTamil
    75: 15,
    // langTelugu → smTelugu
    76: 18,
    // langSinhalese → smSinhalese
    77: 19,
    // langBurmese → smBurmese
    78: 20,
    // langKhmer → smKhmer
    79: 22,
    // langLao → smLao
    80: 30,
    // langVietnamese → smVietnamese
    81: 0,
    // langIndonesian → smRoman
    82: 0,
    // langTagalog → smRoman
    83: 0,
    // langMalayRoman → smRoman
    84: 4,
    // langMalayArabic → smArabic
    85: 28,
    // langAmharic → smEthiopic
    86: 28,
    // langTigrinya → smEthiopic
    87: 28,
    // langOromo → smEthiopic
    88: 0,
    // langSomali → smRoman
    89: 0,
    // langSwahili → smRoman
    90: 0,
    // langKinyarwanda → smRoman
    91: 0,
    // langRundi → smRoman
    92: 0,
    // langNyanja → smRoman
    93: 0,
    // langMalagasy → smRoman
    94: 0,
    // langEsperanto → smRoman
    128: 0,
    // langWelsh → smRoman (modified)
    129: 0,
    // langBasque → smRoman
    130: 0,
    // langCatalan → smRoman
    131: 0,
    // langLatin → smRoman
    132: 0,
    // langQuechua → smRoman
    133: 0,
    // langGuarani → smRoman
    134: 0,
    // langAymara → smRoman
    135: 7,
    // langTatar → smCyrillic
    136: 4,
    // langUighur → smArabic
    137: 26,
    // langDzongkha → smTibetan
    138: 0,
    // langJavaneseRom → smRoman
    139: 0,
    // langSundaneseRom → smRoman
    140: 0,
    // langGalician → smRoman
    141: 0,
    // langAfrikaans → smRoman
    142: 0,
    // langBreton → smRoman (modified)
    143: 28,
    // langInuktitut → smEthiopic (modified)
    144: 0,
    // langScottishGaelic → smRoman (modified)
    145: 0,
    // langManxGaelic → smRoman (modified)
    146: 0,
    // langIrishGaelicScript → smRoman (modified)
    147: 0,
    // langTongan → smRoman
    148: 6,
    // langGreekAncient → smRoman
    149: 0,
    // langGreenlandic → smRoman
    150: 0,
    // langAzerbaijanRoman → smRoman
    151: 0
    // langNynorsk → smRoman
  }, Vs = {
    1078: "af",
    1052: "sq",
    1156: "gsw",
    1118: "am",
    5121: "ar-DZ",
    15361: "ar-BH",
    3073: "ar",
    2049: "ar-IQ",
    11265: "ar-JO",
    13313: "ar-KW",
    12289: "ar-LB",
    4097: "ar-LY",
    6145: "ary",
    8193: "ar-OM",
    16385: "ar-QA",
    1025: "ar-SA",
    10241: "ar-SY",
    7169: "aeb",
    14337: "ar-AE",
    9217: "ar-YE",
    1067: "hy",
    1101: "as",
    2092: "az-Cyrl",
    1068: "az",
    1133: "ba",
    1069: "eu",
    1059: "be",
    2117: "bn",
    1093: "bn-IN",
    8218: "bs-Cyrl",
    5146: "bs",
    1150: "br",
    1026: "bg",
    1027: "ca",
    3076: "zh-HK",
    5124: "zh-MO",
    2052: "zh",
    4100: "zh-SG",
    1028: "zh-TW",
    1155: "co",
    1050: "hr",
    4122: "hr-BA",
    1029: "cs",
    1030: "da",
    1164: "prs",
    1125: "dv",
    2067: "nl-BE",
    1043: "nl",
    3081: "en-AU",
    10249: "en-BZ",
    4105: "en-CA",
    9225: "en-029",
    16393: "en-IN",
    6153: "en-IE",
    8201: "en-JM",
    17417: "en-MY",
    5129: "en-NZ",
    13321: "en-PH",
    18441: "en-SG",
    7177: "en-ZA",
    11273: "en-TT",
    2057: "en-GB",
    1033: "en",
    12297: "en-ZW",
    1061: "et",
    1080: "fo",
    1124: "fil",
    1035: "fi",
    2060: "fr-BE",
    3084: "fr-CA",
    1036: "fr",
    5132: "fr-LU",
    6156: "fr-MC",
    4108: "fr-CH",
    1122: "fy",
    1110: "gl",
    1079: "ka",
    3079: "de-AT",
    1031: "de",
    5127: "de-LI",
    4103: "de-LU",
    2055: "de-CH",
    1032: "el",
    1135: "kl",
    1095: "gu",
    1128: "ha",
    1037: "he",
    1081: "hi",
    1038: "hu",
    1039: "is",
    1136: "ig",
    1057: "id",
    1117: "iu",
    2141: "iu-Latn",
    2108: "ga",
    1076: "xh",
    1077: "zu",
    1040: "it",
    2064: "it-CH",
    1041: "ja",
    1099: "kn",
    1087: "kk",
    1107: "km",
    1158: "quc",
    1159: "rw",
    1089: "sw",
    1111: "kok",
    1042: "ko",
    1088: "ky",
    1108: "lo",
    1062: "lv",
    1063: "lt",
    2094: "dsb",
    1134: "lb",
    1071: "mk",
    2110: "ms-BN",
    1086: "ms",
    1100: "ml",
    1082: "mt",
    1153: "mi",
    1146: "arn",
    1102: "mr",
    1148: "moh",
    1104: "mn",
    2128: "mn-CN",
    1121: "ne",
    1044: "nb",
    2068: "nn",
    1154: "oc",
    1096: "or",
    1123: "ps",
    1045: "pl",
    1046: "pt",
    2070: "pt-PT",
    1094: "pa",
    1131: "qu-BO",
    2155: "qu-EC",
    3179: "qu",
    1048: "ro",
    1047: "rm",
    1049: "ru",
    9275: "smn",
    4155: "smj-NO",
    5179: "smj",
    3131: "se-FI",
    1083: "se",
    2107: "se-SE",
    8251: "sms",
    6203: "sma-NO",
    7227: "sms",
    1103: "sa",
    7194: "sr-Cyrl-BA",
    3098: "sr",
    6170: "sr-Latn-BA",
    2074: "sr-Latn",
    1132: "nso",
    1074: "tn",
    1115: "si",
    1051: "sk",
    1060: "sl",
    11274: "es-AR",
    16394: "es-BO",
    13322: "es-CL",
    9226: "es-CO",
    5130: "es-CR",
    7178: "es-DO",
    12298: "es-EC",
    17418: "es-SV",
    4106: "es-GT",
    18442: "es-HN",
    2058: "es-MX",
    19466: "es-NI",
    6154: "es-PA",
    15370: "es-PY",
    10250: "es-PE",
    20490: "es-PR",
    // Microsoft has defined two different language codes for
    // “Spanish with modern sorting” and “Spanish with traditional
    // sorting”. This makes sense for collation APIs, and it would be
    // possible to express this in BCP 47 language tags via Unicode
    // extensions (eg., es-u-co-trad is Spanish with traditional
    // sorting). However, for storing names in fonts, the distinction
    // does not make sense, so we give “es” in both cases.
    3082: "es",
    1034: "es",
    21514: "es-US",
    14346: "es-UY",
    8202: "es-VE",
    2077: "sv-FI",
    1053: "sv",
    1114: "syr",
    1064: "tg",
    2143: "tzm",
    1097: "ta",
    1092: "tt",
    1098: "te",
    1054: "th",
    1105: "bo",
    1055: "tr",
    1090: "tk",
    1152: "ug",
    1058: "uk",
    1070: "hsb",
    1056: "ur",
    2115: "uz-Cyrl",
    1091: "uz",
    1066: "vi",
    1106: "cy",
    1160: "wo",
    1157: "sah",
    1144: "ii",
    1130: "yo"
  };
  function tu(e, t, r) {
    switch (e) {
      case 0:
        if (t === 65535)
          return "und";
        if (r)
          return r[t];
        break;
      case 1:
        return Zs[t];
      case 3:
        return Vs[t];
    }
  }
  var Sn = "utf-16", ru = {
    0: "macintosh",
    // smRoman
    1: "x-mac-japanese",
    // smJapanese
    2: "x-mac-chinesetrad",
    // smTradChinese
    3: "x-mac-korean",
    // smKorean
    6: "x-mac-greek",
    // smGreek
    7: "x-mac-cyrillic",
    // smCyrillic
    9: "x-mac-devanagai",
    // smDevanagari
    10: "x-mac-gurmukhi",
    // smGurmukhi
    11: "x-mac-gujarati",
    // smGujarati
    12: "x-mac-oriya",
    // smOriya
    13: "x-mac-bengali",
    // smBengali
    14: "x-mac-tamil",
    // smTamil
    15: "x-mac-telugu",
    // smTelugu
    16: "x-mac-kannada",
    // smKannada
    17: "x-mac-malayalam",
    // smMalayalam
    18: "x-mac-sinhalese",
    // smSinhalese
    19: "x-mac-burmese",
    // smBurmese
    20: "x-mac-khmer",
    // smKhmer
    21: "x-mac-thai",
    // smThai
    22: "x-mac-lao",
    // smLao
    23: "x-mac-georgian",
    // smGeorgian
    24: "x-mac-armenian",
    // smArmenian
    25: "x-mac-chinesesimp",
    // smSimpChinese
    26: "x-mac-tibetan",
    // smTibetan
    27: "x-mac-mongolian",
    // smMongolian
    28: "x-mac-ethiopic",
    // smEthiopic
    29: "x-mac-ce",
    // smCentralEuroRoman
    30: "x-mac-vietnamese",
    // smVietnamese
    31: "x-mac-extarabic"
    // smExtArabic
  }, nu = {
    15: "x-mac-icelandic",
    // langIcelandic
    17: "x-mac-turkish",
    // langTurkish
    18: "x-mac-croatian",
    // langCroatian
    24: "x-mac-ce",
    // langLithuanian
    25: "x-mac-ce",
    // langPolish
    26: "x-mac-ce",
    // langHungarian
    27: "x-mac-ce",
    // langEstonian
    28: "x-mac-ce",
    // langLatvian
    30: "x-mac-icelandic",
    // langFaroese
    37: "x-mac-romanian",
    // langRomanian
    38: "x-mac-ce",
    // langCzech
    39: "x-mac-ce",
    // langSlovak
    40: "x-mac-ce",
    // langSlovenian
    143: "x-mac-inuit",
    // langInuktitut
    146: "x-mac-gaelic"
    // langIrishGaelicScript
  };
  function qs(e, t, r) {
    switch (e) {
      case 0:
        return Sn;
      case 1:
        return nu[r] || ru[t];
      case 3:
        if (t === 1 || t === 10)
          return Sn;
        break;
    }
  }
  function au(e, t, r) {
    for (var n = {}, a = new A.Parser(e, t), s = a.parseUShort(), i = a.parseUShort(), o = a.offset + a.parseUShort(), u = 0; u < i; u++) {
      var l = a.parseUShort(), c = a.parseUShort(), h = a.parseUShort(), f = a.parseUShort(), d = js[f] || f, v = a.parseUShort(), S = a.parseUShort(), m = tu(l, h, r), x = qs(l, c, h);
      if (x !== void 0 && m !== void 0) {
        var E = void 0;
        if (x === Sn ? E = Ht.UTF16(e, o + S, v) : E = Ht.MACSTRING(e, o + S, v, x), E) {
          var b = n[d];
          b === void 0 && (b = n[d] = {}), b[m] = E;
        }
      }
    }
    return s === 1 && a.parseUShort(), n;
  }
  function on(e) {
    var t = {};
    for (var r in e)
      t[e[r]] = parseInt(r);
    return t;
  }
  function Sa(e, t, r, n, a, s) {
    return new O.Record("NameRecord", [
      { name: "platformID", type: "USHORT", value: e },
      { name: "encodingID", type: "USHORT", value: t },
      { name: "languageID", type: "USHORT", value: r },
      { name: "nameID", type: "USHORT", value: n },
      { name: "length", type: "USHORT", value: a },
      { name: "offset", type: "USHORT", value: s }
    ]);
  }
  function su(e, t) {
    var r = e.length, n = t.length - r + 1;
    e:
      for (var a = 0; a < n; a++)
        for (; a < n; a++) {
          for (var s = 0; s < r; s++)
            if (t[a + s] !== e[s])
              continue e;
          return a;
        }
    return -1;
  }
  function ka(e, t) {
    var r = su(e, t);
    if (r < 0) {
      r = t.length;
      for (var n = 0, a = e.length; n < a; ++n)
        t.push(e[n]);
    }
    return r;
  }
  function iu(e, t) {
    var r, n = [], a = {}, s = on(js);
    for (var i in e) {
      var o = s[i];
      if (o === void 0 && (o = i), r = parseInt(o), isNaN(r))
        throw new Error('Name table entry "' + i + '" does not exist, see nameTableNames for complete list.');
      a[r] = e[i], n.push(r);
    }
    for (var u = on(Zs), l = on(Vs), c = [], h = [], f = 0; f < n.length; f++) {
      r = n[f];
      var d = a[r];
      for (var v in d) {
        var S = d[v], m = 1, x = u[v], E = eu[x], b = qs(m, E, x), M = C.MACSTRING(S, b);
        M === void 0 && (m = 0, x = t.indexOf(v), x < 0 && (x = t.length, t.push(v)), E = 4, M = C.UTF16(S));
        var I = ka(M, h);
        c.push(Sa(
          m,
          E,
          x,
          r,
          M.length,
          I
        ));
        var W = l[v];
        if (W !== void 0) {
          var U = C.UTF16(S), w = ka(U, h);
          c.push(Sa(
            3,
            1,
            W,
            r,
            U.length,
            w
          ));
        }
      }
    }
    c.sort(function(ae, ne) {
      return ae.platformID - ne.platformID || ae.encodingID - ne.encodingID || ae.languageID - ne.languageID || ae.nameID - ne.nameID;
    });
    for (var Q = new O.Table("name", [
      { name: "format", type: "USHORT", value: 0 },
      { name: "count", type: "USHORT", value: c.length },
      { name: "stringOffset", type: "USHORT", value: 6 + c.length * 12 }
    ]), q = 0; q < c.length; q++)
      Q.fields.push({ name: "record_" + q, type: "RECORD", value: c[q] });
    return Q.fields.push({ name: "strings", type: "LITERAL", value: h }), Q;
  }
  var $s = { parse: au, make: iu }, kn = [
    { begin: 0, end: 127 },
    // Basic Latin
    { begin: 128, end: 255 },
    // Latin-1 Supplement
    { begin: 256, end: 383 },
    // Latin Extended-A
    { begin: 384, end: 591 },
    // Latin Extended-B
    { begin: 592, end: 687 },
    // IPA Extensions
    { begin: 688, end: 767 },
    // Spacing Modifier Letters
    { begin: 768, end: 879 },
    // Combining Diacritical Marks
    { begin: 880, end: 1023 },
    // Greek and Coptic
    { begin: 11392, end: 11519 },
    // Coptic
    { begin: 1024, end: 1279 },
    // Cyrillic
    { begin: 1328, end: 1423 },
    // Armenian
    { begin: 1424, end: 1535 },
    // Hebrew
    { begin: 42240, end: 42559 },
    // Vai
    { begin: 1536, end: 1791 },
    // Arabic
    { begin: 1984, end: 2047 },
    // NKo
    { begin: 2304, end: 2431 },
    // Devanagari
    { begin: 2432, end: 2559 },
    // Bengali
    { begin: 2560, end: 2687 },
    // Gurmukhi
    { begin: 2688, end: 2815 },
    // Gujarati
    { begin: 2816, end: 2943 },
    // Oriya
    { begin: 2944, end: 3071 },
    // Tamil
    { begin: 3072, end: 3199 },
    // Telugu
    { begin: 3200, end: 3327 },
    // Kannada
    { begin: 3328, end: 3455 },
    // Malayalam
    { begin: 3584, end: 3711 },
    // Thai
    { begin: 3712, end: 3839 },
    // Lao
    { begin: 4256, end: 4351 },
    // Georgian
    { begin: 6912, end: 7039 },
    // Balinese
    { begin: 4352, end: 4607 },
    // Hangul Jamo
    { begin: 7680, end: 7935 },
    // Latin Extended Additional
    { begin: 7936, end: 8191 },
    // Greek Extended
    { begin: 8192, end: 8303 },
    // General Punctuation
    { begin: 8304, end: 8351 },
    // Superscripts And Subscripts
    { begin: 8352, end: 8399 },
    // Currency Symbol
    { begin: 8400, end: 8447 },
    // Combining Diacritical Marks For Symbols
    { begin: 8448, end: 8527 },
    // Letterlike Symbols
    { begin: 8528, end: 8591 },
    // Number Forms
    { begin: 8592, end: 8703 },
    // Arrows
    { begin: 8704, end: 8959 },
    // Mathematical Operators
    { begin: 8960, end: 9215 },
    // Miscellaneous Technical
    { begin: 9216, end: 9279 },
    // Control Pictures
    { begin: 9280, end: 9311 },
    // Optical Character Recognition
    { begin: 9312, end: 9471 },
    // Enclosed Alphanumerics
    { begin: 9472, end: 9599 },
    // Box Drawing
    { begin: 9600, end: 9631 },
    // Block Elements
    { begin: 9632, end: 9727 },
    // Geometric Shapes
    { begin: 9728, end: 9983 },
    // Miscellaneous Symbols
    { begin: 9984, end: 10175 },
    // Dingbats
    { begin: 12288, end: 12351 },
    // CJK Symbols And Punctuation
    { begin: 12352, end: 12447 },
    // Hiragana
    { begin: 12448, end: 12543 },
    // Katakana
    { begin: 12544, end: 12591 },
    // Bopomofo
    { begin: 12592, end: 12687 },
    // Hangul Compatibility Jamo
    { begin: 43072, end: 43135 },
    // Phags-pa
    { begin: 12800, end: 13055 },
    // Enclosed CJK Letters And Months
    { begin: 13056, end: 13311 },
    // CJK Compatibility
    { begin: 44032, end: 55215 },
    // Hangul Syllables
    { begin: 55296, end: 57343 },
    // Non-Plane 0 *
    { begin: 67840, end: 67871 },
    // Phoenicia
    { begin: 19968, end: 40959 },
    // CJK Unified Ideographs
    { begin: 57344, end: 63743 },
    // Private Use Area (plane 0)
    { begin: 12736, end: 12783 },
    // CJK Strokes
    { begin: 64256, end: 64335 },
    // Alphabetic Presentation Forms
    { begin: 64336, end: 65023 },
    // Arabic Presentation Forms-A
    { begin: 65056, end: 65071 },
    // Combining Half Marks
    { begin: 65040, end: 65055 },
    // Vertical Forms
    { begin: 65104, end: 65135 },
    // Small Form Variants
    { begin: 65136, end: 65279 },
    // Arabic Presentation Forms-B
    { begin: 65280, end: 65519 },
    // Halfwidth And Fullwidth Forms
    { begin: 65520, end: 65535 },
    // Specials
    { begin: 3840, end: 4095 },
    // Tibetan
    { begin: 1792, end: 1871 },
    // Syriac
    { begin: 1920, end: 1983 },
    // Thaana
    { begin: 3456, end: 3583 },
    // Sinhala
    { begin: 4096, end: 4255 },
    // Myanmar
    { begin: 4608, end: 4991 },
    // Ethiopic
    { begin: 5024, end: 5119 },
    // Cherokee
    { begin: 5120, end: 5759 },
    // Unified Canadian Aboriginal Syllabics
    { begin: 5760, end: 5791 },
    // Ogham
    { begin: 5792, end: 5887 },
    // Runic
    { begin: 6016, end: 6143 },
    // Khmer
    { begin: 6144, end: 6319 },
    // Mongolian
    { begin: 10240, end: 10495 },
    // Braille Patterns
    { begin: 40960, end: 42127 },
    // Yi Syllables
    { begin: 5888, end: 5919 },
    // Tagalog
    { begin: 66304, end: 66351 },
    // Old Italic
    { begin: 66352, end: 66383 },
    // Gothic
    { begin: 66560, end: 66639 },
    // Deseret
    { begin: 118784, end: 119039 },
    // Byzantine Musical Symbols
    { begin: 119808, end: 120831 },
    // Mathematical Alphanumeric Symbols
    { begin: 1044480, end: 1048573 },
    // Private Use (plane 15)
    { begin: 65024, end: 65039 },
    // Variation Selectors
    { begin: 917504, end: 917631 },
    // Tags
    { begin: 6400, end: 6479 },
    // Limbu
    { begin: 6480, end: 6527 },
    // Tai Le
    { begin: 6528, end: 6623 },
    // New Tai Lue
    { begin: 6656, end: 6687 },
    // Buginese
    { begin: 11264, end: 11359 },
    // Glagolitic
    { begin: 11568, end: 11647 },
    // Tifinagh
    { begin: 19904, end: 19967 },
    // Yijing Hexagram Symbols
    { begin: 43008, end: 43055 },
    // Syloti Nagri
    { begin: 65536, end: 65663 },
    // Linear B Syllabary
    { begin: 65856, end: 65935 },
    // Ancient Greek Numbers
    { begin: 66432, end: 66463 },
    // Ugaritic
    { begin: 66464, end: 66527 },
    // Old Persian
    { begin: 66640, end: 66687 },
    // Shavian
    { begin: 66688, end: 66735 },
    // Osmanya
    { begin: 67584, end: 67647 },
    // Cypriot Syllabary
    { begin: 68096, end: 68191 },
    // Kharoshthi
    { begin: 119552, end: 119647 },
    // Tai Xuan Jing Symbols
    { begin: 73728, end: 74751 },
    // Cuneiform
    { begin: 119648, end: 119679 },
    // Counting Rod Numerals
    { begin: 7040, end: 7103 },
    // Sundanese
    { begin: 7168, end: 7247 },
    // Lepcha
    { begin: 7248, end: 7295 },
    // Ol Chiki
    { begin: 43136, end: 43231 },
    // Saurashtra
    { begin: 43264, end: 43311 },
    // Kayah Li
    { begin: 43312, end: 43359 },
    // Rejang
    { begin: 43520, end: 43615 },
    // Cham
    { begin: 65936, end: 65999 },
    // Ancient Symbols
    { begin: 66e3, end: 66047 },
    // Phaistos Disc
    { begin: 66208, end: 66271 },
    // Carian
    { begin: 127024, end: 127135 }
    // Domino Tiles
  ];
  function ou(e) {
    for (var t = 0; t < kn.length; t += 1) {
      var r = kn[t];
      if (e >= r.begin && e < r.end)
        return t;
    }
    return -1;
  }
  function uu(e, t) {
    var r = {}, n = new A.Parser(e, t);
    r.version = n.parseUShort(), r.xAvgCharWidth = n.parseShort(), r.usWeightClass = n.parseUShort(), r.usWidthClass = n.parseUShort(), r.fsType = n.parseUShort(), r.ySubscriptXSize = n.parseShort(), r.ySubscriptYSize = n.parseShort(), r.ySubscriptXOffset = n.parseShort(), r.ySubscriptYOffset = n.parseShort(), r.ySuperscriptXSize = n.parseShort(), r.ySuperscriptYSize = n.parseShort(), r.ySuperscriptXOffset = n.parseShort(), r.ySuperscriptYOffset = n.parseShort(), r.yStrikeoutSize = n.parseShort(), r.yStrikeoutPosition = n.parseShort(), r.sFamilyClass = n.parseShort(), r.panose = [];
    for (var a = 0; a < 10; a++)
      r.panose[a] = n.parseByte();
    return r.ulUnicodeRange1 = n.parseULong(), r.ulUnicodeRange2 = n.parseULong(), r.ulUnicodeRange3 = n.parseULong(), r.ulUnicodeRange4 = n.parseULong(), r.achVendID = String.fromCharCode(n.parseByte(), n.parseByte(), n.parseByte(), n.parseByte()), r.fsSelection = n.parseUShort(), r.usFirstCharIndex = n.parseUShort(), r.usLastCharIndex = n.parseUShort(), r.sTypoAscender = n.parseShort(), r.sTypoDescender = n.parseShort(), r.sTypoLineGap = n.parseShort(), r.usWinAscent = n.parseUShort(), r.usWinDescent = n.parseUShort(), r.version >= 1 && (r.ulCodePageRange1 = n.parseULong(), r.ulCodePageRange2 = n.parseULong()), r.version >= 2 && (r.sxHeight = n.parseShort(), r.sCapHeight = n.parseShort(), r.usDefaultChar = n.parseUShort(), r.usBreakChar = n.parseUShort(), r.usMaxContent = n.parseUShort()), r;
  }
  function lu(e) {
    return new O.Table("OS/2", [
      { name: "version", type: "USHORT", value: 3 },
      { name: "xAvgCharWidth", type: "SHORT", value: 0 },
      { name: "usWeightClass", type: "USHORT", value: 0 },
      { name: "usWidthClass", type: "USHORT", value: 0 },
      { name: "fsType", type: "USHORT", value: 0 },
      { name: "ySubscriptXSize", type: "SHORT", value: 650 },
      { name: "ySubscriptYSize", type: "SHORT", value: 699 },
      { name: "ySubscriptXOffset", type: "SHORT", value: 0 },
      { name: "ySubscriptYOffset", type: "SHORT", value: 140 },
      { name: "ySuperscriptXSize", type: "SHORT", value: 650 },
      { name: "ySuperscriptYSize", type: "SHORT", value: 699 },
      { name: "ySuperscriptXOffset", type: "SHORT", value: 0 },
      { name: "ySuperscriptYOffset", type: "SHORT", value: 479 },
      { name: "yStrikeoutSize", type: "SHORT", value: 49 },
      { name: "yStrikeoutPosition", type: "SHORT", value: 258 },
      { name: "sFamilyClass", type: "SHORT", value: 0 },
      { name: "bFamilyType", type: "BYTE", value: 0 },
      { name: "bSerifStyle", type: "BYTE", value: 0 },
      { name: "bWeight", type: "BYTE", value: 0 },
      { name: "bProportion", type: "BYTE", value: 0 },
      { name: "bContrast", type: "BYTE", value: 0 },
      { name: "bStrokeVariation", type: "BYTE", value: 0 },
      { name: "bArmStyle", type: "BYTE", value: 0 },
      { name: "bLetterform", type: "BYTE", value: 0 },
      { name: "bMidline", type: "BYTE", value: 0 },
      { name: "bXHeight", type: "BYTE", value: 0 },
      { name: "ulUnicodeRange1", type: "ULONG", value: 0 },
      { name: "ulUnicodeRange2", type: "ULONG", value: 0 },
      { name: "ulUnicodeRange3", type: "ULONG", value: 0 },
      { name: "ulUnicodeRange4", type: "ULONG", value: 0 },
      { name: "achVendID", type: "CHARARRAY", value: "XXXX" },
      { name: "fsSelection", type: "USHORT", value: 0 },
      { name: "usFirstCharIndex", type: "USHORT", value: 0 },
      { name: "usLastCharIndex", type: "USHORT", value: 0 },
      { name: "sTypoAscender", type: "SHORT", value: 0 },
      { name: "sTypoDescender", type: "SHORT", value: 0 },
      { name: "sTypoLineGap", type: "SHORT", value: 0 },
      { name: "usWinAscent", type: "USHORT", value: 0 },
      { name: "usWinDescent", type: "USHORT", value: 0 },
      { name: "ulCodePageRange1", type: "ULONG", value: 0 },
      { name: "ulCodePageRange2", type: "ULONG", value: 0 },
      { name: "sxHeight", type: "SHORT", value: 0 },
      { name: "sCapHeight", type: "SHORT", value: 0 },
      { name: "usDefaultChar", type: "USHORT", value: 0 },
      { name: "usBreakChar", type: "USHORT", value: 0 },
      { name: "usMaxContext", type: "USHORT", value: 0 }
    ], e);
  }
  var Tn = { parse: uu, make: lu, unicodeRanges: kn, getUnicodeRange: ou };
  function cu(e, t) {
    var r = {}, n = new A.Parser(e, t);
    switch (r.version = n.parseVersion(), r.italicAngle = n.parseFixed(), r.underlinePosition = n.parseShort(), r.underlineThickness = n.parseShort(), r.isFixedPitch = n.parseULong(), r.minMemType42 = n.parseULong(), r.maxMemType42 = n.parseULong(), r.minMemType1 = n.parseULong(), r.maxMemType1 = n.parseULong(), r.version) {
      case 1:
        r.names = pt.slice();
        break;
      case 2:
        r.numberOfGlyphs = n.parseUShort(), r.glyphNameIndex = new Array(r.numberOfGlyphs);
        for (var a = 0; a < r.numberOfGlyphs; a++)
          r.glyphNameIndex[a] = n.parseUShort();
        r.names = [];
        for (var s = 0; s < r.numberOfGlyphs; s++)
          if (r.glyphNameIndex[s] >= pt.length) {
            var i = n.parseChar();
            r.names.push(n.parseString(i));
          }
        break;
      case 2.5:
        r.numberOfGlyphs = n.parseUShort(), r.offset = new Array(r.numberOfGlyphs);
        for (var o = 0; o < r.numberOfGlyphs; o++)
          r.offset[o] = n.parseChar();
        break;
    }
    return r;
  }
  function fu() {
    return new O.Table("post", [
      { name: "version", type: "FIXED", value: 196608 },
      { name: "italicAngle", type: "FIXED", value: 0 },
      { name: "underlinePosition", type: "FWORD", value: 0 },
      { name: "underlineThickness", type: "FWORD", value: 0 },
      { name: "isFixedPitch", type: "ULONG", value: 0 },
      { name: "minMemType42", type: "ULONG", value: 0 },
      { name: "maxMemType42", type: "ULONG", value: 0 },
      { name: "minMemType1", type: "ULONG", value: 0 },
      { name: "maxMemType1", type: "ULONG", value: 0 }
    ]);
  }
  var Xs = { parse: cu, make: fu }, Ue = new Array(9);
  Ue[1] = function() {
    var t = this.offset + this.relativeOffset, r = this.parseUShort();
    if (r === 1)
      return {
        substFormat: 1,
        coverage: this.parsePointer(p.coverage),
        deltaGlyphId: this.parseUShort()
      };
    if (r === 2)
      return {
        substFormat: 2,
        coverage: this.parsePointer(p.coverage),
        substitute: this.parseOffset16List()
      };
    G.assert(!1, "0x" + t.toString(16) + ": lookup type 1 format must be 1 or 2.");
  };
  Ue[2] = function() {
    var t = this.parseUShort();
    return G.argument(t === 1, "GSUB Multiple Substitution Subtable identifier-format must be 1"), {
      substFormat: t,
      coverage: this.parsePointer(p.coverage),
      sequences: this.parseListOfLists()
    };
  };
  Ue[3] = function() {
    var t = this.parseUShort();
    return G.argument(t === 1, "GSUB Alternate Substitution Subtable identifier-format must be 1"), {
      substFormat: t,
      coverage: this.parsePointer(p.coverage),
      alternateSets: this.parseListOfLists()
    };
  };
  Ue[4] = function() {
    var t = this.parseUShort();
    return G.argument(t === 1, "GSUB ligature table identifier-format must be 1"), {
      substFormat: t,
      coverage: this.parsePointer(p.coverage),
      ligatureSets: this.parseListOfLists(function() {
        return {
          ligGlyph: this.parseUShort(),
          components: this.parseUShortList(this.parseUShort() - 1)
        };
      })
    };
  };
  var Ut = {
    sequenceIndex: p.uShort,
    lookupListIndex: p.uShort
  };
  Ue[5] = function() {
    var t = this.offset + this.relativeOffset, r = this.parseUShort();
    if (r === 1)
      return {
        substFormat: r,
        coverage: this.parsePointer(p.coverage),
        ruleSets: this.parseListOfLists(function() {
          var s = this.parseUShort(), i = this.parseUShort();
          return {
            input: this.parseUShortList(s - 1),
            lookupRecords: this.parseRecordList(i, Ut)
          };
        })
      };
    if (r === 2)
      return {
        substFormat: r,
        coverage: this.parsePointer(p.coverage),
        classDef: this.parsePointer(p.classDef),
        classSets: this.parseListOfLists(function() {
          var s = this.parseUShort(), i = this.parseUShort();
          return {
            classes: this.parseUShortList(s - 1),
            lookupRecords: this.parseRecordList(i, Ut)
          };
        })
      };
    if (r === 3) {
      var n = this.parseUShort(), a = this.parseUShort();
      return {
        substFormat: r,
        coverages: this.parseList(n, p.pointer(p.coverage)),
        lookupRecords: this.parseRecordList(a, Ut)
      };
    }
    G.assert(!1, "0x" + t.toString(16) + ": lookup type 5 format must be 1, 2 or 3.");
  };
  Ue[6] = function() {
    var t = this.offset + this.relativeOffset, r = this.parseUShort();
    if (r === 1)
      return {
        substFormat: 1,
        coverage: this.parsePointer(p.coverage),
        chainRuleSets: this.parseListOfLists(function() {
          return {
            backtrack: this.parseUShortList(),
            input: this.parseUShortList(this.parseShort() - 1),
            lookahead: this.parseUShortList(),
            lookupRecords: this.parseRecordList(Ut)
          };
        })
      };
    if (r === 2)
      return {
        substFormat: 2,
        coverage: this.parsePointer(p.coverage),
        backtrackClassDef: this.parsePointer(p.classDef),
        inputClassDef: this.parsePointer(p.classDef),
        lookaheadClassDef: this.parsePointer(p.classDef),
        chainClassSet: this.parseListOfLists(function() {
          return {
            backtrack: this.parseUShortList(),
            input: this.parseUShortList(this.parseShort() - 1),
            lookahead: this.parseUShortList(),
            lookupRecords: this.parseRecordList(Ut)
          };
        })
      };
    if (r === 3)
      return {
        substFormat: 3,
        backtrackCoverage: this.parseList(p.pointer(p.coverage)),
        inputCoverage: this.parseList(p.pointer(p.coverage)),
        lookaheadCoverage: this.parseList(p.pointer(p.coverage)),
        lookupRecords: this.parseRecordList(Ut)
      };
    G.assert(!1, "0x" + t.toString(16) + ": lookup type 6 format must be 1, 2 or 3.");
  };
  Ue[7] = function() {
    var t = this.parseUShort();
    G.argument(t === 1, "GSUB Extension Substitution subtable identifier-format must be 1");
    var r = this.parseUShort(), n = new p(this.data, this.offset + this.parseULong());
    return {
      substFormat: 1,
      lookupType: r,
      extension: Ue[r].call(n)
    };
  };
  Ue[8] = function() {
    var t = this.parseUShort();
    return G.argument(t === 1, "GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1"), {
      substFormat: t,
      coverage: this.parsePointer(p.coverage),
      backtrackCoverage: this.parseList(p.pointer(p.coverage)),
      lookaheadCoverage: this.parseList(p.pointer(p.coverage)),
      substitutes: this.parseUShortList()
    };
  };
  function hu(e, t) {
    t = t || 0;
    var r = new p(e, t), n = r.parseVersion(1);
    return G.argument(n === 1 || n === 1.1, "Unsupported GSUB table version."), n === 1 ? {
      version: n,
      scripts: r.parseScriptList(),
      features: r.parseFeatureList(),
      lookups: r.parseLookupList(Ue)
    } : {
      version: n,
      scripts: r.parseScriptList(),
      features: r.parseFeatureList(),
      lookups: r.parseLookupList(Ue),
      variations: r.parseFeatureVariationsList()
    };
  }
  var jt = new Array(9);
  jt[1] = function(t) {
    return t.substFormat === 1 ? new O.Table("substitutionTable", [
      { name: "substFormat", type: "USHORT", value: 1 },
      { name: "coverage", type: "TABLE", value: new O.Coverage(t.coverage) },
      { name: "deltaGlyphID", type: "USHORT", value: t.deltaGlyphId }
    ]) : new O.Table("substitutionTable", [
      { name: "substFormat", type: "USHORT", value: 2 },
      { name: "coverage", type: "TABLE", value: new O.Coverage(t.coverage) }
    ].concat(O.ushortList("substitute", t.substitute)));
  };
  jt[2] = function(t) {
    return G.assert(t.substFormat === 1, "Lookup type 2 substFormat must be 1."), new O.Table("substitutionTable", [
      { name: "substFormat", type: "USHORT", value: 1 },
      { name: "coverage", type: "TABLE", value: new O.Coverage(t.coverage) }
    ].concat(O.tableList("seqSet", t.sequences, function(r) {
      return new O.Table("sequenceSetTable", O.ushortList("sequence", r));
    })));
  };
  jt[3] = function(t) {
    return G.assert(t.substFormat === 1, "Lookup type 3 substFormat must be 1."), new O.Table("substitutionTable", [
      { name: "substFormat", type: "USHORT", value: 1 },
      { name: "coverage", type: "TABLE", value: new O.Coverage(t.coverage) }
    ].concat(O.tableList("altSet", t.alternateSets, function(r) {
      return new O.Table("alternateSetTable", O.ushortList("alternate", r));
    })));
  };
  jt[4] = function(t) {
    return G.assert(t.substFormat === 1, "Lookup type 4 substFormat must be 1."), new O.Table("substitutionTable", [
      { name: "substFormat", type: "USHORT", value: 1 },
      { name: "coverage", type: "TABLE", value: new O.Coverage(t.coverage) }
    ].concat(O.tableList("ligSet", t.ligatureSets, function(r) {
      return new O.Table("ligatureSetTable", O.tableList("ligature", r, function(n) {
        return new O.Table(
          "ligatureTable",
          [{ name: "ligGlyph", type: "USHORT", value: n.ligGlyph }].concat(O.ushortList("component", n.components, n.components.length + 1))
        );
      }));
    })));
  };
  jt[6] = function(t) {
    if (t.substFormat === 1) {
      var r = new O.Table("chainContextTable", [
        { name: "substFormat", type: "USHORT", value: t.substFormat },
        { name: "coverage", type: "TABLE", value: new O.Coverage(t.coverage) }
      ].concat(O.tableList("chainRuleSet", t.chainRuleSets, function(s) {
        return new O.Table("chainRuleSetTable", O.tableList("chainRule", s, function(i) {
          var o = O.ushortList("backtrackGlyph", i.backtrack, i.backtrack.length).concat(O.ushortList("inputGlyph", i.input, i.input.length + 1)).concat(O.ushortList("lookaheadGlyph", i.lookahead, i.lookahead.length)).concat(O.ushortList("substitution", [], i.lookupRecords.length));
          return i.lookupRecords.forEach(function(u, l) {
            o = o.concat({ name: "sequenceIndex" + l, type: "USHORT", value: u.sequenceIndex }).concat({ name: "lookupListIndex" + l, type: "USHORT", value: u.lookupListIndex });
          }), new O.Table("chainRuleTable", o);
        }));
      })));
      return r;
    } else if (t.substFormat === 2)
      G.assert(!1, "lookup type 6 format 2 is not yet supported.");
    else if (t.substFormat === 3) {
      var n = [
        { name: "substFormat", type: "USHORT", value: t.substFormat }
      ];
      n.push({ name: "backtrackGlyphCount", type: "USHORT", value: t.backtrackCoverage.length }), t.backtrackCoverage.forEach(function(s, i) {
        n.push({ name: "backtrackCoverage" + i, type: "TABLE", value: new O.Coverage(s) });
      }), n.push({ name: "inputGlyphCount", type: "USHORT", value: t.inputCoverage.length }), t.inputCoverage.forEach(function(s, i) {
        n.push({ name: "inputCoverage" + i, type: "TABLE", value: new O.Coverage(s) });
      }), n.push({ name: "lookaheadGlyphCount", type: "USHORT", value: t.lookaheadCoverage.length }), t.lookaheadCoverage.forEach(function(s, i) {
        n.push({ name: "lookaheadCoverage" + i, type: "TABLE", value: new O.Coverage(s) });
      }), n.push({ name: "substitutionCount", type: "USHORT", value: t.lookupRecords.length }), t.lookupRecords.forEach(function(s, i) {
        n = n.concat({ name: "sequenceIndex" + i, type: "USHORT", value: s.sequenceIndex }).concat({ name: "lookupListIndex" + i, type: "USHORT", value: s.lookupListIndex });
      });
      var a = new O.Table("chainContextTable", n);
      return a;
    }
    G.assert(!1, "lookup type 6 format must be 1, 2 or 3.");
  };
  function du(e) {
    return new O.Table("GSUB", [
      { name: "version", type: "ULONG", value: 65536 },
      { name: "scripts", type: "TABLE", value: new O.ScriptList(e.scripts) },
      { name: "features", type: "TABLE", value: new O.FeatureList(e.features) },
      { name: "lookups", type: "TABLE", value: new O.LookupList(e.lookups, jt) }
    ]);
  }
  var Ys = { parse: hu, make: du };
  function pu(e, t) {
    var r = new A.Parser(e, t), n = r.parseULong();
    G.argument(n === 1, "Unsupported META table version."), r.parseULong(), r.parseULong();
    for (var a = r.parseULong(), s = {}, i = 0; i < a; i++) {
      var o = r.parseTag(), u = r.parseULong(), l = r.parseULong(), c = Ht.UTF8(e, t + u, l);
      s[o] = c;
    }
    return s;
  }
  function vu(e) {
    var t = Object.keys(e).length, r = "", n = 16 + t * 12, a = new O.Table("meta", [
      { name: "version", type: "ULONG", value: 1 },
      { name: "flags", type: "ULONG", value: 0 },
      { name: "offset", type: "ULONG", value: n },
      { name: "numTags", type: "ULONG", value: t }
    ]);
    for (var s in e) {
      var i = r.length;
      r += e[s], a.fields.push({ name: "tag " + s, type: "TAG", value: s }), a.fields.push({ name: "offset " + s, type: "ULONG", value: n + i }), a.fields.push({ name: "length " + s, type: "ULONG", value: e[s].length });
    }
    return a.fields.push({ name: "stringPool", type: "CHARARRAY", value: r }), a;
  }
  var Qs = { parse: pu, make: vu };
  function Ta(e) {
    return Math.log(e) / Math.log(2) | 0;
  }
  function $n(e) {
    for (; e.length % 4 !== 0; )
      e.push(0);
    for (var t = 0, r = 0; r < e.length; r += 4)
      t += (e[r] << 24) + (e[r + 1] << 16) + (e[r + 2] << 8) + e[r + 3];
    return t %= Math.pow(2, 32), t;
  }
  function wa(e, t, r, n) {
    return new O.Record("Table Record", [
      { name: "tag", type: "TAG", value: e !== void 0 ? e : "" },
      { name: "checkSum", type: "ULONG", value: t !== void 0 ? t : 0 },
      { name: "offset", type: "ULONG", value: r !== void 0 ? r : 0 },
      { name: "length", type: "ULONG", value: n !== void 0 ? n : 0 }
    ]);
  }
  function Js(e) {
    var t = new O.Table("sfnt", [
      { name: "version", type: "TAG", value: "OTTO" },
      { name: "numTables", type: "USHORT", value: 0 },
      { name: "searchRange", type: "USHORT", value: 0 },
      { name: "entrySelector", type: "USHORT", value: 0 },
      { name: "rangeShift", type: "USHORT", value: 0 }
    ]);
    t.tables = e, t.numTables = e.length;
    var r = Math.pow(2, Ta(t.numTables));
    t.searchRange = 16 * r, t.entrySelector = Ta(r), t.rangeShift = t.numTables * 16 - t.searchRange;
    for (var n = [], a = [], s = t.sizeOf() + wa().sizeOf() * t.numTables; s % 4 !== 0; )
      s += 1, a.push({ name: "padding", type: "BYTE", value: 0 });
    for (var i = 0; i < e.length; i += 1) {
      var o = e[i];
      G.argument(o.tableName.length === 4, "Table name" + o.tableName + " is invalid.");
      var u = o.sizeOf(), l = wa(o.tableName, $n(o.encode()), s, u);
      for (n.push({ name: l.tag + " Table Record", type: "RECORD", value: l }), a.push({ name: o.tableName + " table", type: "RECORD", value: o }), s += u, G.argument(!isNaN(s), "Something went wrong calculating the offset."); s % 4 !== 0; )
        s += 1, a.push({ name: "padding", type: "BYTE", value: 0 });
    }
    return n.sort(function(c, h) {
      return c.value.tag > h.value.tag ? 1 : -1;
    }), t.fields = t.fields.concat(n), t.fields = t.fields.concat(a), t;
  }
  function Ca(e, t, r) {
    for (var n = 0; n < t.length; n += 1) {
      var a = e.charToGlyphIndex(t[n]);
      if (a > 0) {
        var s = e.glyphs.get(a);
        return s.getMetrics();
      }
    }
    return r;
  }
  function gu(e) {
    for (var t = 0, r = 0; r < e.length; r += 1)
      t += e[r];
    return t / e.length;
  }
  function mu(e) {
    for (var t = [], r = [], n = [], a = [], s = [], i = [], o = [], u, l = 0, c = 0, h = 0, f = 0, d = 0, v = 0; v < e.glyphs.length; v += 1) {
      var S = e.glyphs.get(v), m = S.unicode | 0;
      if (isNaN(S.advanceWidth))
        throw new Error("Glyph " + S.name + " (" + v + "): advanceWidth is not a number.");
      (u > m || u === void 0) && m > 0 && (u = m), l < m && (l = m);
      var x = Tn.getUnicodeRange(m);
      if (x < 32)
        c |= 1 << x;
      else if (x < 64)
        h |= 1 << x - 32;
      else if (x < 96)
        f |= 1 << x - 64;
      else if (x < 123)
        d |= 1 << x - 96;
      else
        throw new Error("Unicode ranges bits > 123 are reserved for internal usage");
      if (S.name !== ".notdef") {
        var E = S.getMetrics();
        t.push(E.xMin), r.push(E.yMin), n.push(E.xMax), a.push(E.yMax), i.push(E.leftSideBearing), o.push(E.rightSideBearing), s.push(S.advanceWidth);
      }
    }
    var b = {
      xMin: Math.min.apply(null, t),
      yMin: Math.min.apply(null, r),
      xMax: Math.max.apply(null, n),
      yMax: Math.max.apply(null, a),
      advanceWidthMax: Math.max.apply(null, s),
      advanceWidthAvg: gu(s),
      minLeftSideBearing: Math.min.apply(null, i),
      maxLeftSideBearing: Math.max.apply(null, i),
      minRightSideBearing: Math.min.apply(null, o)
    };
    b.ascender = e.ascender, b.descender = e.descender;
    var M = Bs.make({
      flags: 3,
      // 00000011 (baseline for font at y=0; left sidebearing point at x=0)
      unitsPerEm: e.unitsPerEm,
      xMin: b.xMin,
      yMin: b.yMin,
      xMax: b.xMax,
      yMax: b.yMax,
      lowestRecPPEM: 3,
      createdTimestamp: e.createdTimestamp
    }), I = Gs.make({
      ascender: b.ascender,
      descender: b.descender,
      advanceWidthMax: b.advanceWidthMax,
      minLeftSideBearing: b.minLeftSideBearing,
      minRightSideBearing: b.minRightSideBearing,
      xMaxExtent: b.maxLeftSideBearing + (b.xMax - b.xMin),
      numberOfHMetrics: e.glyphs.length
    }), W = zs.make(e.glyphs.length), U = Tn.make(Object.assign({
      xAvgCharWidth: Math.round(b.advanceWidthAvg),
      usFirstCharIndex: u,
      usLastCharIndex: l,
      ulUnicodeRange1: c,
      ulUnicodeRange2: h,
      ulUnicodeRange3: f,
      ulUnicodeRange4: d,
      // See http://typophile.com/node/13081 for more info on vertical metrics.
      // We get metrics for typical characters (such as "x" for xHeight).
      // We provide some fallback characters if characters are unavailable: their
      // ordering was chosen experimentally.
      sTypoAscender: b.ascender,
      sTypoDescender: b.descender,
      sTypoLineGap: 0,
      usWinAscent: b.yMax,
      usWinDescent: Math.abs(b.yMin),
      ulCodePageRange1: 1,
      // FIXME: hard-code Latin 1 support for now
      sxHeight: Ca(e, "xyvw", { yMax: Math.round(b.ascender / 2) }).yMax,
      sCapHeight: Ca(e, "HIKLEFJMNTZBDPRAGOQSUVWXY", b).yMax,
      usDefaultChar: e.hasChar(" ") ? 32 : 0,
      // Use space as the default character, if available.
      usBreakChar: e.hasChar(" ") ? 32 : 0
      // Use space as the break character, if available.
    }, e.tables.os2)), w = Hs.make(e.glyphs), Q = Fs.make(e.glyphs), q = e.getEnglishName("fontFamily"), ae = e.getEnglishName("fontSubfamily"), ne = q + " " + ae, K = e.getEnglishName("postScriptName");
    K || (K = q.replace(/\s/g, "") + "-" + ae);
    var te = {};
    for (var ee in e.names)
      te[ee] = e.names[ee];
    te.uniqueID || (te.uniqueID = { en: e.getEnglishName("manufacturer") + ":" + ne }), te.postScriptName || (te.postScriptName = { en: K }), te.preferredFamily || (te.preferredFamily = e.names.fontFamily), te.preferredSubfamily || (te.preferredSubfamily = e.names.fontSubfamily);
    var ie = [], le = $s.make(te, ie), ce = ie.length > 0 ? Ws.make(ie) : void 0, se = Xs.make(), y = Ns.make(e.glyphs, {
      version: e.getEnglishName("version"),
      fullName: ne,
      familyName: q,
      weightName: ae,
      postScriptName: K,
      unitsPerEm: e.unitsPerEm,
      fontBBox: [0, b.yMin, b.ascender, b.advanceWidthMax]
    }), L = e.metas && Object.keys(e.metas).length > 0 ? Qs.make(e.metas) : void 0, Z = [M, I, W, U, le, Q, se, y, w];
    ce && Z.push(ce), e.tables.gsub && Z.push(Ys.make(e.tables.gsub)), L && Z.push(L);
    for (var pe = Js(Z), Me = pe.encode(), Ce = $n(Me), ft = pe.fields, Ft = !1, he = 0; he < ft.length; he += 1)
      if (ft[he].name === "head table") {
        ft[he].value.checkSumAdjustment = 2981146554 - Ce, Ft = !0;
        break;
      }
    if (!Ft)
      throw new Error("Could not find head table with checkSum to adjust.");
    return pe;
  }
  var yu = { make: Js, fontToTable: mu, computeCheckSum: $n };
  function un(e, t) {
    for (var r = 0, n = e.length - 1; r <= n; ) {
      var a = r + n >>> 1, s = e[a].tag;
      if (s === t)
        return a;
      s < t ? r = a + 1 : n = a - 1;
    }
    return -r - 1;
  }
  function Ea(e, t) {
    for (var r = 0, n = e.length - 1; r <= n; ) {
      var a = r + n >>> 1, s = e[a];
      if (s === t)
        return a;
      s < t ? r = a + 1 : n = a - 1;
    }
    return -r - 1;
  }
  function Fa(e, t) {
    for (var r, n = 0, a = e.length - 1; n <= a; ) {
      var s = n + a >>> 1;
      r = e[s];
      var i = r.start;
      if (i === t)
        return r;
      i < t ? n = s + 1 : a = s - 1;
    }
    if (n > 0)
      return r = e[n - 1], t > r.end ? 0 : r;
  }
  function xr(e, t) {
    this.font = e, this.tableName = t;
  }
  xr.prototype = {
    /**
     * Binary search an object by "tag" property
     * @instance
     * @function searchTag
     * @memberof opentype.Layout
     * @param  {Array} arr
     * @param  {string} tag
     * @return {number}
     */
    searchTag: un,
    /**
     * Binary search in a list of numbers
     * @instance
     * @function binSearch
     * @memberof opentype.Layout
     * @param  {Array} arr
     * @param  {number} value
     * @return {number}
     */
    binSearch: Ea,
    /**
     * Get or create the Layout table (GSUB, GPOS etc).
     * @param  {boolean} create - Whether to create a new one.
     * @return {Object} The GSUB or GPOS table.
     */
    getTable: function(e) {
      var t = this.font.tables[this.tableName];
      return !t && e && (t = this.font.tables[this.tableName] = this.createDefaultTable()), t;
    },
    /**
     * Returns all scripts in the substitution table.
     * @instance
     * @return {Array}
     */
    getScriptNames: function() {
      var e = this.getTable();
      return e ? e.scripts.map(function(t) {
        return t.tag;
      }) : [];
    },
    /**
     * Returns the best bet for a script name.
     * Returns 'DFLT' if it exists.
     * If not, returns 'latn' if it exists.
     * If neither exist, returns undefined.
     */
    getDefaultScriptName: function() {
      var e = this.getTable();
      if (e) {
        for (var t = !1, r = 0; r < e.scripts.length; r++) {
          var n = e.scripts[r].tag;
          if (n === "DFLT")
            return n;
          n === "latn" && (t = !0);
        }
        if (t)
          return "latn";
      }
    },
    /**
     * Returns all LangSysRecords in the given script.
     * @instance
     * @param {string} [script='DFLT']
     * @param {boolean} create - forces the creation of this script table if it doesn't exist.
     * @return {Object} An object with tag and script properties.
     */
    getScriptTable: function(e, t) {
      var r = this.getTable(t);
      if (r) {
        e = e || "DFLT";
        var n = r.scripts, a = un(r.scripts, e);
        if (a >= 0)
          return n[a].script;
        if (t) {
          var s = {
            tag: e,
            script: {
              defaultLangSys: { reserved: 0, reqFeatureIndex: 65535, featureIndexes: [] },
              langSysRecords: []
            }
          };
          return n.splice(-1 - a, 0, s), s.script;
        }
      }
    },
    /**
     * Returns a language system table
     * @instance
     * @param {string} [script='DFLT']
     * @param {string} [language='dlft']
     * @param {boolean} create - forces the creation of this langSysTable if it doesn't exist.
     * @return {Object}
     */
    getLangSysTable: function(e, t, r) {
      var n = this.getScriptTable(e, r);
      if (n) {
        if (!t || t === "dflt" || t === "DFLT")
          return n.defaultLangSys;
        var a = un(n.langSysRecords, t);
        if (a >= 0)
          return n.langSysRecords[a].langSys;
        if (r) {
          var s = {
            tag: t,
            langSys: { reserved: 0, reqFeatureIndex: 65535, featureIndexes: [] }
          };
          return n.langSysRecords.splice(-1 - a, 0, s), s.langSys;
        }
      }
    },
    /**
     * Get a specific feature table.
     * @instance
     * @param {string} [script='DFLT']
     * @param {string} [language='dlft']
     * @param {string} feature - One of the codes listed at https://www.microsoft.com/typography/OTSPEC/featurelist.htm
     * @param {boolean} create - forces the creation of the feature table if it doesn't exist.
     * @return {Object}
     */
    getFeatureTable: function(e, t, r, n) {
      var a = this.getLangSysTable(e, t, n);
      if (a) {
        for (var s, i = a.featureIndexes, o = this.font.tables[this.tableName].features, u = 0; u < i.length; u++)
          if (s = o[i[u]], s.tag === r)
            return s.feature;
        if (n) {
          var l = o.length;
          return G.assert(l === 0 || r >= o[l - 1].tag, "Features must be added in alphabetical order."), s = {
            tag: r,
            feature: { params: 0, lookupListIndexes: [] }
          }, o.push(s), i.push(l), s.feature;
        }
      }
    },
    /**
     * Get the lookup tables of a given type for a script/language/feature.
     * @instance
     * @param {string} [script='DFLT']
     * @param {string} [language='dlft']
     * @param {string} feature - 4-letter feature code
     * @param {number} lookupType - 1 to 9
     * @param {boolean} create - forces the creation of the lookup table if it doesn't exist, with no subtables.
     * @return {Object[]}
     */
    getLookupTables: function(e, t, r, n, a) {
      var s = this.getFeatureTable(e, t, r, a), i = [];
      if (s) {
        for (var o, u = s.lookupListIndexes, l = this.font.tables[this.tableName].lookups, c = 0; c < u.length; c++)
          o = l[u[c]], o.lookupType === n && i.push(o);
        if (i.length === 0 && a) {
          o = {
            lookupType: n,
            lookupFlag: 0,
            subtables: [],
            markFilteringSet: void 0
          };
          var h = l.length;
          return l.push(o), u.push(h), [o];
        }
      }
      return i;
    },
    /**
     * Find a glyph in a class definition table
     * https://docs.microsoft.com/en-us/typography/opentype/spec/chapter2#class-definition-table
     * @param {object} classDefTable - an OpenType Layout class definition table
     * @param {number} glyphIndex - the index of the glyph to find
     * @returns {number} -1 if not found
     */
    getGlyphClass: function(e, t) {
      switch (e.format) {
        case 1:
          return e.startGlyph <= t && t < e.startGlyph + e.classes.length ? e.classes[t - e.startGlyph] : 0;
        case 2:
          var r = Fa(e.ranges, t);
          return r ? r.classId : 0;
      }
    },
    /**
     * Find a glyph in a coverage table
     * https://docs.microsoft.com/en-us/typography/opentype/spec/chapter2#coverage-table
     * @param {object} coverageTable - an OpenType Layout coverage table
     * @param {number} glyphIndex - the index of the glyph to find
     * @returns {number} -1 if not found
     */
    getCoverageIndex: function(e, t) {
      switch (e.format) {
        case 1:
          var r = Ea(e.glyphs, t);
          return r >= 0 ? r : -1;
        case 2:
          var n = Fa(e.ranges, t);
          return n ? n.index + t - n.start : -1;
      }
    },
    /**
     * Returns the list of glyph indexes of a coverage table.
     * Format 1: the list is stored raw
     * Format 2: compact list as range records.
     * @instance
     * @param  {Object} coverageTable
     * @return {Array}
     */
    expandCoverage: function(e) {
      if (e.format === 1)
        return e.glyphs;
      for (var t = [], r = e.ranges, n = 0; n < r.length; n++)
        for (var a = r[n], s = a.start, i = a.end, o = s; o <= i; o++)
          t.push(o);
      return t;
    }
  };
  function br(e) {
    xr.call(this, e, "gpos");
  }
  br.prototype = xr.prototype;
  br.prototype.init = function() {
    var e = this.getDefaultScriptName();
    this.defaultKerningTables = this.getKerningTables(e);
  };
  br.prototype.getKerningValue = function(e, t, r) {
    for (var n = 0; n < e.length; n++)
      for (var a = e[n].subtables, s = 0; s < a.length; s++) {
        var i = a[s], o = this.getCoverageIndex(i.coverage, t);
        if (!(o < 0))
          switch (i.posFormat) {
            case 1:
              for (var u = i.pairSets[o], l = 0; l < u.length; l++) {
                var c = u[l];
                if (c.secondGlyph === r)
                  return c.value1 && c.value1.xAdvance || 0;
              }
              break;
            // left glyph found, not right glyph - try next subtable
            case 2:
              var h = this.getGlyphClass(i.classDef1, t), f = this.getGlyphClass(i.classDef2, r), d = i.classRecords[h][f];
              return d.value1 && d.value1.xAdvance || 0;
          }
      }
    return 0;
  };
  br.prototype.getKerningTables = function(e, t) {
    if (this.font.tables.gpos)
      return this.getLookupTables(e, t, "kern", 2);
  };
  function Te(e) {
    xr.call(this, e, "gsub");
  }
  function xu(e, t) {
    var r = e.length;
    if (r !== t.length)
      return !1;
    for (var n = 0; n < r; n++)
      if (e[n] !== t[n])
        return !1;
    return !0;
  }
  function Xn(e, t, r) {
    for (var n = e.subtables, a = 0; a < n.length; a++) {
      var s = n[a];
      if (s.substFormat === t)
        return s;
    }
    if (r)
      return n.push(r), r;
  }
  Te.prototype = xr.prototype;
  Te.prototype.createDefaultTable = function() {
    return {
      version: 1,
      scripts: [{
        tag: "DFLT",
        script: {
          defaultLangSys: { reserved: 0, reqFeatureIndex: 65535, featureIndexes: [] },
          langSysRecords: []
        }
      }],
      features: [],
      lookups: []
    };
  };
  Te.prototype.getSingle = function(e, t, r) {
    for (var n = [], a = this.getLookupTables(t, r, e, 1), s = 0; s < a.length; s++)
      for (var i = a[s].subtables, o = 0; o < i.length; o++) {
        var u = i[o], l = this.expandCoverage(u.coverage), c = void 0;
        if (u.substFormat === 1) {
          var h = u.deltaGlyphId;
          for (c = 0; c < l.length; c++) {
            var f = l[c];
            n.push({ sub: f, by: f + h });
          }
        } else {
          var d = u.substitute;
          for (c = 0; c < l.length; c++)
            n.push({ sub: l[c], by: d[c] });
        }
      }
    return n;
  };
  Te.prototype.getMultiple = function(e, t, r) {
    for (var n = [], a = this.getLookupTables(t, r, e, 2), s = 0; s < a.length; s++)
      for (var i = a[s].subtables, o = 0; o < i.length; o++) {
        var u = i[o], l = this.expandCoverage(u.coverage), c = void 0;
        for (c = 0; c < l.length; c++) {
          var h = l[c], f = u.sequences[c];
          n.push({ sub: h, by: f });
        }
      }
    return n;
  };
  Te.prototype.getAlternates = function(e, t, r) {
    for (var n = [], a = this.getLookupTables(t, r, e, 3), s = 0; s < a.length; s++)
      for (var i = a[s].subtables, o = 0; o < i.length; o++)
        for (var u = i[o], l = this.expandCoverage(u.coverage), c = u.alternateSets, h = 0; h < l.length; h++)
          n.push({ sub: l[h], by: c[h] });
    return n;
  };
  Te.prototype.getLigatures = function(e, t, r) {
    for (var n = [], a = this.getLookupTables(t, r, e, 4), s = 0; s < a.length; s++)
      for (var i = a[s].subtables, o = 0; o < i.length; o++)
        for (var u = i[o], l = this.expandCoverage(u.coverage), c = u.ligatureSets, h = 0; h < l.length; h++)
          for (var f = l[h], d = c[h], v = 0; v < d.length; v++) {
            var S = d[v];
            n.push({
              sub: [f].concat(S.components),
              by: S.ligGlyph
            });
          }
    return n;
  };
  Te.prototype.addSingle = function(e, t, r, n) {
    var a = this.getLookupTables(r, n, e, 1, !0)[0], s = Xn(a, 2, {
      // lookup type 1 subtable, format 2, coverage format 1
      substFormat: 2,
      coverage: { format: 1, glyphs: [] },
      substitute: []
    });
    G.assert(s.coverage.format === 1, "Single: unable to modify coverage table format " + s.coverage.format);
    var i = t.sub, o = this.binSearch(s.coverage.glyphs, i);
    o < 0 && (o = -1 - o, s.coverage.glyphs.splice(o, 0, i), s.substitute.splice(o, 0, 0)), s.substitute[o] = t.by;
  };
  Te.prototype.addMultiple = function(e, t, r, n) {
    G.assert(t.by instanceof Array && t.by.length > 1, 'Multiple: "by" must be an array of two or more ids');
    var a = this.getLookupTables(r, n, e, 2, !0)[0], s = Xn(a, 1, {
      // lookup type 2 subtable, format 1, coverage format 1
      substFormat: 1,
      coverage: { format: 1, glyphs: [] },
      sequences: []
    });
    G.assert(s.coverage.format === 1, "Multiple: unable to modify coverage table format " + s.coverage.format);
    var i = t.sub, o = this.binSearch(s.coverage.glyphs, i);
    o < 0 && (o = -1 - o, s.coverage.glyphs.splice(o, 0, i), s.sequences.splice(o, 0, 0)), s.sequences[o] = t.by;
  };
  Te.prototype.addAlternate = function(e, t, r, n) {
    var a = this.getLookupTables(r, n, e, 3, !0)[0], s = Xn(a, 1, {
      // lookup type 3 subtable, format 1, coverage format 1
      substFormat: 1,
      coverage: { format: 1, glyphs: [] },
      alternateSets: []
    });
    G.assert(s.coverage.format === 1, "Alternate: unable to modify coverage table format " + s.coverage.format);
    var i = t.sub, o = this.binSearch(s.coverage.glyphs, i);
    o < 0 && (o = -1 - o, s.coverage.glyphs.splice(o, 0, i), s.alternateSets.splice(o, 0, 0)), s.alternateSets[o] = t.by;
  };
  Te.prototype.addLigature = function(e, t, r, n) {
    var a = this.getLookupTables(r, n, e, 4, !0)[0], s = a.subtables[0];
    s || (s = {
      // lookup type 4 subtable, format 1, coverage format 1
      substFormat: 1,
      coverage: { format: 1, glyphs: [] },
      ligatureSets: []
    }, a.subtables[0] = s), G.assert(s.coverage.format === 1, "Ligature: unable to modify coverage table format " + s.coverage.format);
    var i = t.sub[0], o = t.sub.slice(1), u = {
      ligGlyph: t.by,
      components: o
    }, l = this.binSearch(s.coverage.glyphs, i);
    if (l >= 0) {
      for (var c = s.ligatureSets[l], h = 0; h < c.length; h++)
        if (xu(c[h].components, o))
          return;
      c.push(u);
    } else
      l = -1 - l, s.coverage.glyphs.splice(l, 0, i), s.ligatureSets.splice(l, 0, [u]);
  };
  Te.prototype.getFeature = function(e, t, r) {
    if (/ss\d\d/.test(e))
      return this.getSingle(e, t, r);
    switch (e) {
      case "aalt":
      case "salt":
        return this.getSingle(e, t, r).concat(this.getAlternates(e, t, r));
      case "dlig":
      case "liga":
      case "rlig":
        return this.getLigatures(e, t, r);
      case "ccmp":
        return this.getMultiple(e, t, r).concat(this.getLigatures(e, t, r));
      case "stch":
        return this.getMultiple(e, t, r);
    }
  };
  Te.prototype.add = function(e, t, r, n) {
    if (/ss\d\d/.test(e))
      return this.addSingle(e, t, r, n);
    switch (e) {
      case "aalt":
      case "salt":
        return typeof t.by == "number" ? this.addSingle(e, t, r, n) : this.addAlternate(e, t, r, n);
      case "dlig":
      case "liga":
      case "rlig":
        return this.addLigature(e, t, r, n);
      case "ccmp":
        return t.by instanceof Array ? this.addMultiple(e, t, r, n) : this.addLigature(e, t, r, n);
    }
  };
  function bu() {
    return typeof window < "u";
  }
  function Su(e) {
    for (var t = new ArrayBuffer(e.length), r = new Uint8Array(t), n = 0; n < e.length; ++n)
      r[n] = e[n];
    return t;
  }
  function ku(e) {
    for (var t = new Buffer(e.byteLength), r = new Uint8Array(e), n = 0; n < t.length; ++n)
      t[n] = r[n];
    return t;
  }
  function $t(e, t) {
    if (!e)
      throw t;
  }
  function Oa(e, t, r, n, a) {
    var s;
    return (t & n) > 0 ? (s = e.parseByte(), (t & a) === 0 && (s = -s), s = r + s) : (t & a) > 0 ? s = r : s = r + e.parseShort(), s;
  }
  function Ks(e, t, r) {
    var n = new A.Parser(t, r);
    e.numberOfContours = n.parseShort(), e._xMin = n.parseShort(), e._yMin = n.parseShort(), e._xMax = n.parseShort(), e._yMax = n.parseShort();
    var a, s;
    if (e.numberOfContours > 0) {
      for (var i = e.endPointIndices = [], o = 0; o < e.numberOfContours; o += 1)
        i.push(n.parseUShort());
      e.instructionLength = n.parseUShort(), e.instructions = [];
      for (var u = 0; u < e.instructionLength; u += 1)
        e.instructions.push(n.parseByte());
      var l = i[i.length - 1] + 1;
      a = [];
      for (var c = 0; c < l; c += 1)
        if (s = n.parseByte(), a.push(s), (s & 8) > 0)
          for (var h = n.parseByte(), f = 0; f < h; f += 1)
            a.push(s), c += 1;
      if (G.argument(a.length === l, "Bad flags."), i.length > 0) {
        var d = [], v;
        if (l > 0) {
          for (var S = 0; S < l; S += 1)
            s = a[S], v = {}, v.onCurve = !!(s & 1), v.lastPointOfContour = i.indexOf(S) >= 0, d.push(v);
          for (var m = 0, x = 0; x < l; x += 1)
            s = a[x], v = d[x], v.x = Oa(n, s, m, 2, 16), m = v.x;
          for (var E = 0, b = 0; b < l; b += 1)
            s = a[b], v = d[b], v.y = Oa(n, s, E, 4, 32), E = v.y;
        }
        e.points = d;
      } else
        e.points = [];
    } else if (e.numberOfContours === 0)
      e.points = [];
    else {
      e.isComposite = !0, e.points = [], e.components = [];
      for (var M = !0; M; ) {
        a = n.parseUShort();
        var I = {
          glyphIndex: n.parseUShort(),
          xScale: 1,
          scale01: 0,
          scale10: 0,
          yScale: 1,
          dx: 0,
          dy: 0
        };
        (a & 1) > 0 ? (a & 2) > 0 ? (I.dx = n.parseShort(), I.dy = n.parseShort()) : I.matchedPoints = [n.parseUShort(), n.parseUShort()] : (a & 2) > 0 ? (I.dx = n.parseChar(), I.dy = n.parseChar()) : I.matchedPoints = [n.parseByte(), n.parseByte()], (a & 8) > 0 ? I.xScale = I.yScale = n.parseF2Dot14() : (a & 64) > 0 ? (I.xScale = n.parseF2Dot14(), I.yScale = n.parseF2Dot14()) : (a & 128) > 0 && (I.xScale = n.parseF2Dot14(), I.scale01 = n.parseF2Dot14(), I.scale10 = n.parseF2Dot14(), I.yScale = n.parseF2Dot14()), e.components.push(I), M = !!(a & 32);
      }
      if (a & 256) {
        e.instructionLength = n.parseUShort(), e.instructions = [];
        for (var W = 0; W < e.instructionLength; W += 1)
          e.instructions.push(n.parseByte());
      }
    }
  }
  function ln(e, t) {
    for (var r = [], n = 0; n < e.length; n += 1) {
      var a = e[n], s = {
        x: t.xScale * a.x + t.scale01 * a.y + t.dx,
        y: t.scale10 * a.x + t.yScale * a.y + t.dy,
        onCurve: a.onCurve,
        lastPointOfContour: a.lastPointOfContour
      };
      r.push(s);
    }
    return r;
  }
  function Tu(e) {
    for (var t = [], r = [], n = 0; n < e.length; n += 1) {
      var a = e[n];
      r.push(a), a.lastPointOfContour && (t.push(r), r = []);
    }
    return G.argument(r.length === 0, "There are still points left in the current contour."), t;
  }
  function ei(e) {
    var t = new ge();
    if (!e)
      return t;
    for (var r = Tu(e), n = 0; n < r.length; ++n) {
      var a = r[n], s = null, i = a[a.length - 1], o = a[0];
      if (i.onCurve)
        t.moveTo(i.x, i.y);
      else if (o.onCurve)
        t.moveTo(o.x, o.y);
      else {
        var u = { x: (i.x + o.x) * 0.5, y: (i.y + o.y) * 0.5 };
        t.moveTo(u.x, u.y);
      }
      for (var l = 0; l < a.length; ++l)
        if (s = i, i = o, o = a[(l + 1) % a.length], i.onCurve)
          t.lineTo(i.x, i.y);
        else {
          var c = o;
          s.onCurve || ((i.x + s.x) * 0.5, (i.y + s.y) * 0.5), o.onCurve || (c = { x: (i.x + o.x) * 0.5, y: (i.y + o.y) * 0.5 }), t.quadraticCurveTo(i.x, i.y, c.x, c.y);
        }
      t.closePath();
    }
    return t;
  }
  function ti(e, t) {
    if (t.isComposite)
      for (var r = 0; r < t.components.length; r += 1) {
        var n = t.components[r], a = e.get(n.glyphIndex);
        if (a.getPath(), a.points) {
          var s = void 0;
          if (n.matchedPoints === void 0)
            s = ln(a.points, n);
          else {
            if (n.matchedPoints[0] > t.points.length - 1 || n.matchedPoints[1] > a.points.length - 1)
              throw Error("Matched points out of range in " + t.name);
            var i = t.points[n.matchedPoints[0]], o = a.points[n.matchedPoints[1]], u = {
              xScale: n.xScale,
              scale01: n.scale01,
              scale10: n.scale10,
              yScale: n.yScale,
              dx: 0,
              dy: 0
            };
            o = ln([o], u)[0], u.dx = i.x - o.x, u.dy = i.y - o.y, s = ln(a.points, u);
          }
          t.points = t.points.concat(s);
        }
      }
    return ei(t.points);
  }
  function wu(e, t, r, n) {
    for (var a = new We.GlyphSet(n), s = 0; s < r.length - 1; s += 1) {
      var i = r[s], o = r[s + 1];
      i !== o ? a.push(s, We.ttfGlyphLoader(n, s, Ks, e, t + i, ti)) : a.push(s, We.glyphLoader(n, s));
    }
    return a;
  }
  function Cu(e, t, r, n) {
    var a = new We.GlyphSet(n);
    return n._push = function(s) {
      var i = r[s], o = r[s + 1];
      i !== o ? a.push(s, We.ttfGlyphLoader(n, s, Ks, e, t + i, ti)) : a.push(s, We.glyphLoader(n, s));
    }, a;
  }
  function Eu(e, t, r, n, a) {
    return a.lowMemory ? Cu(e, t, r, n) : wu(e, t, r, n);
  }
  var ri = { getPath: ei, parse: Eu }, ni, Tt, ai, wn;
  function si(e) {
    this.font = e, this.getCommands = function(t) {
      return ri.getPath(t).commands;
    }, this._fpgmState = this._prepState = void 0, this._errorState = 0;
  }
  function Fu(e) {
    return e;
  }
  function ii(e) {
    return Math.sign(e) * Math.round(Math.abs(e));
  }
  function Ou(e) {
    return Math.sign(e) * Math.round(Math.abs(e * 2)) / 2;
  }
  function Ru(e) {
    return Math.sign(e) * (Math.round(Math.abs(e) + 0.5) - 0.5);
  }
  function _u(e) {
    return Math.sign(e) * Math.ceil(Math.abs(e));
  }
  function Uu(e) {
    return Math.sign(e) * Math.floor(Math.abs(e));
  }
  var oi = function(e) {
    var t = this.srPeriod, r = this.srPhase, n = this.srThreshold, a = 1;
    return e < 0 && (e = -e, a = -1), e += n - r, e = Math.trunc(e / t) * t, e += r, e < 0 ? r * a : e * a;
  }, He = {
    x: 1,
    y: 0,
    axis: "x",
    // Gets the projected distance between two points.
    // o1/o2 ... if true, respective original position is used.
    distance: function(e, t, r, n) {
      return (r ? e.xo : e.x) - (n ? t.xo : t.x);
    },
    // Moves point p so the moved position has the same relative
    // position to the moved positions of rp1 and rp2 than the
    // original positions had.
    //
    // See APPENDIX on INTERPOLATE at the bottom of this file.
    interpolate: function(e, t, r, n) {
      var a, s, i, o, u, l, c;
      if (!n || n === this) {
        if (a = e.xo - t.xo, s = e.xo - r.xo, u = t.x - t.xo, l = r.x - r.xo, i = Math.abs(a), o = Math.abs(s), c = i + o, c === 0) {
          e.x = e.xo + (u + l) / 2;
          return;
        }
        e.x = e.xo + (u * o + l * i) / c;
        return;
      }
      if (a = n.distance(e, t, !0, !0), s = n.distance(e, r, !0, !0), u = n.distance(t, t, !1, !0), l = n.distance(r, r, !1, !0), i = Math.abs(a), o = Math.abs(s), c = i + o, c === 0) {
        He.setRelative(e, e, (u + l) / 2, n, !0);
        return;
      }
      He.setRelative(e, e, (u * o + l * i) / c, n, !0);
    },
    // Slope of line normal to this
    normalSlope: Number.NEGATIVE_INFINITY,
    // Sets the point 'p' relative to point 'rp'
    // by the distance 'd'.
    //
    // See APPENDIX on SETRELATIVE at the bottom of this file.
    //
    // p   ... point to set
    // rp  ... reference point
    // d   ... distance on projection vector
    // pv  ... projection vector (undefined = this)
    // org ... if true, uses the original position of rp as reference.
    setRelative: function(e, t, r, n, a) {
      if (!n || n === this) {
        e.x = (a ? t.xo : t.x) + r;
        return;
      }
      var s = a ? t.xo : t.x, i = a ? t.yo : t.y, o = s + r * n.x, u = i + r * n.y;
      e.x = o + (e.y - u) / n.normalSlope;
    },
    // Slope of vector line.
    slope: 0,
    // Touches the point p.
    touch: function(e) {
      e.xTouched = !0;
    },
    // Tests if a point p is touched.
    touched: function(e) {
      return e.xTouched;
    },
    // Untouches the point p.
    untouch: function(e) {
      e.xTouched = !1;
    }
  }, Ze = {
    x: 0,
    y: 1,
    axis: "y",
    // Gets the projected distance between two points.
    // o1/o2 ... if true, respective original position is used.
    distance: function(e, t, r, n) {
      return (r ? e.yo : e.y) - (n ? t.yo : t.y);
    },
    // Moves point p so the moved position has the same relative
    // position to the moved positions of rp1 and rp2 than the
    // original positions had.
    //
    // See APPENDIX on INTERPOLATE at the bottom of this file.
    interpolate: function(e, t, r, n) {
      var a, s, i, o, u, l, c;
      if (!n || n === this) {
        if (a = e.yo - t.yo, s = e.yo - r.yo, u = t.y - t.yo, l = r.y - r.yo, i = Math.abs(a), o = Math.abs(s), c = i + o, c === 0) {
          e.y = e.yo + (u + l) / 2;
          return;
        }
        e.y = e.yo + (u * o + l * i) / c;
        return;
      }
      if (a = n.distance(e, t, !0, !0), s = n.distance(e, r, !0, !0), u = n.distance(t, t, !1, !0), l = n.distance(r, r, !1, !0), i = Math.abs(a), o = Math.abs(s), c = i + o, c === 0) {
        Ze.setRelative(e, e, (u + l) / 2, n, !0);
        return;
      }
      Ze.setRelative(e, e, (u * o + l * i) / c, n, !0);
    },
    // Slope of line normal to this.
    normalSlope: 0,
    // Sets the point 'p' relative to point 'rp'
    // by the distance 'd'
    //
    // See APPENDIX on SETRELATIVE at the bottom of this file.
    //
    // p   ... point to set
    // rp  ... reference point
    // d   ... distance on projection vector
    // pv  ... projection vector (undefined = this)
    // org ... if true, uses the original position of rp as reference.
    setRelative: function(e, t, r, n, a) {
      if (!n || n === this) {
        e.y = (a ? t.yo : t.y) + r;
        return;
      }
      var s = a ? t.xo : t.x, i = a ? t.yo : t.y, o = s + r * n.x, u = i + r * n.y;
      e.y = u + n.normalSlope * (e.x - o);
    },
    // Slope of vector line.
    slope: Number.POSITIVE_INFINITY,
    // Touches the point p.
    touch: function(e) {
      e.yTouched = !0;
    },
    // Tests if a point p is touched.
    touched: function(e) {
      return e.yTouched;
    },
    // Untouches the point p.
    untouch: function(e) {
      e.yTouched = !1;
    }
  };
  Object.freeze(He);
  Object.freeze(Ze);
  function Sr(e, t) {
    this.x = e, this.y = t, this.axis = void 0, this.slope = t / e, this.normalSlope = -e / t, Object.freeze(this);
  }
  Sr.prototype.distance = function(e, t, r, n) {
    return this.x * He.distance(e, t, r, n) + this.y * Ze.distance(e, t, r, n);
  };
  Sr.prototype.interpolate = function(e, t, r, n) {
    var a, s, i, o, u, l, c;
    if (i = n.distance(e, t, !0, !0), o = n.distance(e, r, !0, !0), a = n.distance(t, t, !1, !0), s = n.distance(r, r, !1, !0), u = Math.abs(i), l = Math.abs(o), c = u + l, c === 0) {
      this.setRelative(e, e, (a + s) / 2, n, !0);
      return;
    }
    this.setRelative(e, e, (a * l + s * u) / c, n, !0);
  };
  Sr.prototype.setRelative = function(e, t, r, n, a) {
    n = n || this;
    var s = a ? t.xo : t.x, i = a ? t.yo : t.y, o = s + r * n.x, u = i + r * n.y, l = n.normalSlope, c = this.slope, h = e.x, f = e.y;
    e.x = (c * h - l * o + u - f) / (c - l), e.y = c * (e.x - h) + f;
  };
  Sr.prototype.touch = function(e) {
    e.xTouched = !0, e.yTouched = !0;
  };
  function kr(e, t) {
    var r = Math.sqrt(e * e + t * t);
    return e /= r, t /= r, e === 1 && t === 0 ? He : e === 0 && t === 1 ? Ze : new Sr(e, t);
  }
  function Ve(e, t, r, n) {
    this.x = this.xo = Math.round(e * 64) / 64, this.y = this.yo = Math.round(t * 64) / 64, this.lastPointOfContour = r, this.onCurve = n, this.prevPointOnContour = void 0, this.nextPointOnContour = void 0, this.xTouched = !1, this.yTouched = !1, Object.preventExtensions(this);
  }
  Ve.prototype.nextTouched = function(e) {
    for (var t = this.nextPointOnContour; !e.touched(t) && t !== this; )
      t = t.nextPointOnContour;
    return t;
  };
  Ve.prototype.prevTouched = function(e) {
    for (var t = this.prevPointOnContour; !e.touched(t) && t !== this; )
      t = t.prevPointOnContour;
    return t;
  };
  var tr = Object.freeze(new Ve(0, 0)), Lu = {
    cvCutIn: 17 / 16,
    // control value cut in
    deltaBase: 9,
    deltaShift: 0.125,
    loop: 1,
    // loops some instructions
    minDis: 1,
    // minimum distance
    autoFlip: !0
  };
  function at(e, t) {
    switch (this.env = e, this.stack = [], this.prog = t, e) {
      case "glyf":
        this.zp0 = this.zp1 = this.zp2 = 1, this.rp0 = this.rp1 = this.rp2 = 0;
      /* fall through */
      case "prep":
        this.fv = this.pv = this.dpv = He, this.round = ii;
    }
  }
  si.prototype.exec = function(e, t) {
    if (typeof t != "number")
      throw new Error("Point size is not a number!");
    if (!(this._errorState > 2)) {
      var r = this.font, n = this._prepState;
      if (!n || n.ppem !== t) {
        var a = this._fpgmState;
        if (!a) {
          at.prototype = Lu, a = this._fpgmState = new at("fpgm", r.tables.fpgm), a.funcs = [], a.font = r, g.DEBUG && (console.log("---EXEC FPGM---"), a.step = -1);
          try {
            Tt(a);
          } catch (l) {
            console.log("Hinting error in FPGM:" + l), this._errorState = 3;
            return;
          }
        }
        at.prototype = a, n = this._prepState = new at("prep", r.tables.prep), n.ppem = t;
        var s = r.tables.cvt;
        if (s)
          for (var i = n.cvt = new Array(s.length), o = t / r.unitsPerEm, u = 0; u < s.length; u++)
            i[u] = s[u] * o;
        else
          n.cvt = [];
        g.DEBUG && (console.log("---EXEC PREP---"), n.step = -1);
        try {
          Tt(n);
        } catch (l) {
          this._errorState < 2 && console.log("Hinting error in PREP:" + l), this._errorState = 2;
        }
      }
      if (!(this._errorState > 1))
        try {
          return ai(e, n);
        } catch (l) {
          this._errorState < 1 && (console.log("Hinting error:" + l), console.log("Note: further hinting errors are silenced")), this._errorState = 1;
          return;
        }
    }
  };
  ai = function(e, t) {
    var r = t.ppem / t.font.unitsPerEm, n = r, a = e.components, s, i, o;
    if (at.prototype = t, !a)
      o = new at("glyf", e.instructions), g.DEBUG && (console.log("---EXEC GLYPH---"), o.step = -1), wn(e, o, r, n), i = o.gZone;
    else {
      var u = t.font;
      i = [], s = [];
      for (var l = 0; l < a.length; l++) {
        var c = a[l], h = u.glyphs.get(c.glyphIndex);
        o = new at("glyf", h.instructions), g.DEBUG && (console.log("---EXEC COMP " + l + "---"), o.step = -1), wn(h, o, r, n);
        for (var f = Math.round(c.dx * r), d = Math.round(c.dy * n), v = o.gZone, S = o.contours, m = 0; m < v.length; m++) {
          var x = v[m];
          x.xTouched = x.yTouched = !1, x.xo = x.x = x.x + f, x.yo = x.y = x.y + d;
        }
        var E = i.length;
        i.push.apply(i, v);
        for (var b = 0; b < S.length; b++)
          s.push(S[b] + E);
      }
      e.instructions && !o.inhibitGridFit && (o = new at("glyf", e.instructions), o.gZone = o.z0 = o.z1 = o.z2 = i, o.contours = s, i.push(
        new Ve(0, 0),
        new Ve(Math.round(e.advanceWidth * r), 0)
      ), g.DEBUG && (console.log("---EXEC COMPOSITE---"), o.step = -1), Tt(o), i.length -= 2);
    }
    return i;
  };
  wn = function(e, t, r, n) {
    for (var a = e.points || [], s = a.length, i = t.gZone = t.z0 = t.z1 = t.z2 = [], o = t.contours = [], u, l = 0; l < s; l++)
      u = a[l], i[l] = new Ve(
        u.x * r,
        u.y * n,
        u.lastPointOfContour,
        u.onCurve
      );
    for (var c, h, f = 0; f < s; f++)
      u = i[f], c || (c = u, o.push(f)), u.lastPointOfContour ? (u.nextPointOnContour = c, c.prevPointOnContour = u, c = void 0) : (h = i[f + 1], u.nextPointOnContour = h, h.prevPointOnContour = u);
    if (!t.inhibitGridFit) {
      if (g.DEBUG) {
        console.log("PROCESSING GLYPH", t.stack);
        for (var d = 0; d < s; d++)
          console.log(d, i[d].x, i[d].y);
      }
      if (i.push(
        new Ve(0, 0),
        new Ve(Math.round(e.advanceWidth * r), 0)
      ), Tt(t), i.length -= 2, g.DEBUG) {
        console.log("FINISHED GLYPH", t.stack);
        for (var v = 0; v < s; v++)
          console.log(v, i[v].x, i[v].y);
      }
    }
  };
  Tt = function(e) {
    var t = e.prog;
    if (t) {
      var r = t.length, n;
      for (e.ip = 0; e.ip < r; e.ip++) {
        if (g.DEBUG && e.step++, n = ni[t[e.ip]], !n)
          throw new Error(
            "unknown instruction: 0x" + Number(t[e.ip]).toString(16)
          );
        n(e);
      }
    }
  };
  function en(e) {
    for (var t = e.tZone = new Array(e.gZone.length), r = 0; r < t.length; r++)
      t[r] = new Ve(0, 0);
  }
  function ui(e, t) {
    var r = e.prog, n = e.ip, a = 1, s;
    do
      if (s = r[++n], s === 88)
        a++;
      else if (s === 89)
        a--;
      else if (s === 64)
        n += r[n + 1] + 1;
      else if (s === 65)
        n += 2 * r[n + 1] + 1;
      else if (s >= 176 && s <= 183)
        n += s - 176 + 1;
      else if (s >= 184 && s <= 191)
        n += (s - 184 + 1) * 2;
      else if (t && a === 1 && s === 27)
        break;
    while (a > 0);
    e.ip = n;
  }
  function Ra(e, t) {
    g.DEBUG && console.log(t.step, "SVTCA[" + e.axis + "]"), t.fv = t.pv = t.dpv = e;
  }
  function _a(e, t) {
    g.DEBUG && console.log(t.step, "SPVTCA[" + e.axis + "]"), t.pv = t.dpv = e;
  }
  function Ua(e, t) {
    g.DEBUG && console.log(t.step, "SFVTCA[" + e.axis + "]"), t.fv = e;
  }
  function La(e, t) {
    var r = t.stack, n = r.pop(), a = r.pop(), s = t.z2[n], i = t.z1[a];
    g.DEBUG && console.log("SPVTL[" + e + "]", n, a);
    var o, u;
    e ? (o = s.y - i.y, u = i.x - s.x) : (o = i.x - s.x, u = i.y - s.y), t.pv = t.dpv = kr(o, u);
  }
  function Aa(e, t) {
    var r = t.stack, n = r.pop(), a = r.pop(), s = t.z2[n], i = t.z1[a];
    g.DEBUG && console.log("SFVTL[" + e + "]", n, a);
    var o, u;
    e ? (o = s.y - i.y, u = i.x - s.x) : (o = i.x - s.x, u = i.y - s.y), t.fv = kr(o, u);
  }
  function Au(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "SPVFS[]", r, n), e.pv = e.dpv = kr(n, r);
  }
  function Iu(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "SPVFS[]", r, n), e.fv = kr(n, r);
  }
  function Du(e) {
    var t = e.stack, r = e.pv;
    g.DEBUG && console.log(e.step, "GPV[]"), t.push(r.x * 16384), t.push(r.y * 16384);
  }
  function Pu(e) {
    var t = e.stack, r = e.fv;
    g.DEBUG && console.log(e.step, "GFV[]"), t.push(r.x * 16384), t.push(r.y * 16384);
  }
  function Mu(e) {
    e.fv = e.pv, g.DEBUG && console.log(e.step, "SFVTPV[]");
  }
  function Nu(e) {
    var t = e.stack, r = t.pop(), n = t.pop(), a = t.pop(), s = t.pop(), i = t.pop(), o = e.z0, u = e.z1, l = o[r], c = o[n], h = u[a], f = u[s], d = e.z2[i];
    g.DEBUG && console.log("ISECT[], ", r, n, a, s, i);
    var v = l.x, S = l.y, m = c.x, x = c.y, E = h.x, b = h.y, M = f.x, I = f.y, W = (v - m) * (b - I) - (S - x) * (E - M), U = v * x - S * m, w = E * I - b * M;
    d.x = (U * (E - M) - w * (v - m)) / W, d.y = (U * (b - I) - w * (S - x)) / W;
  }
  function Bu(e) {
    e.rp0 = e.stack.pop(), g.DEBUG && console.log(e.step, "SRP0[]", e.rp0);
  }
  function Gu(e) {
    e.rp1 = e.stack.pop(), g.DEBUG && console.log(e.step, "SRP1[]", e.rp1);
  }
  function Hu(e) {
    e.rp2 = e.stack.pop(), g.DEBUG && console.log(e.step, "SRP2[]", e.rp2);
  }
  function Wu(e) {
    var t = e.stack.pop();
    switch (g.DEBUG && console.log(e.step, "SZP0[]", t), e.zp0 = t, t) {
      case 0:
        e.tZone || en(e), e.z0 = e.tZone;
        break;
      case 1:
        e.z0 = e.gZone;
        break;
      default:
        throw new Error("Invalid zone pointer");
    }
  }
  function zu(e) {
    var t = e.stack.pop();
    switch (g.DEBUG && console.log(e.step, "SZP1[]", t), e.zp1 = t, t) {
      case 0:
        e.tZone || en(e), e.z1 = e.tZone;
        break;
      case 1:
        e.z1 = e.gZone;
        break;
      default:
        throw new Error("Invalid zone pointer");
    }
  }
  function ju(e) {
    var t = e.stack.pop();
    switch (g.DEBUG && console.log(e.step, "SZP2[]", t), e.zp2 = t, t) {
      case 0:
        e.tZone || en(e), e.z2 = e.tZone;
        break;
      case 1:
        e.z2 = e.gZone;
        break;
      default:
        throw new Error("Invalid zone pointer");
    }
  }
  function Zu(e) {
    var t = e.stack.pop();
    switch (g.DEBUG && console.log(e.step, "SZPS[]", t), e.zp0 = e.zp1 = e.zp2 = t, t) {
      case 0:
        e.tZone || en(e), e.z0 = e.z1 = e.z2 = e.tZone;
        break;
      case 1:
        e.z0 = e.z1 = e.z2 = e.gZone;
        break;
      default:
        throw new Error("Invalid zone pointer");
    }
  }
  function Vu(e) {
    e.loop = e.stack.pop(), g.DEBUG && console.log(e.step, "SLOOP[]", e.loop);
  }
  function qu(e) {
    g.DEBUG && console.log(e.step, "RTG[]"), e.round = ii;
  }
  function $u(e) {
    g.DEBUG && console.log(e.step, "RTHG[]"), e.round = Ru;
  }
  function Xu(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "SMD[]", t), e.minDis = t / 64;
  }
  function Yu(e) {
    g.DEBUG && console.log(e.step, "ELSE[]"), ui(e, !1);
  }
  function Qu(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "JMPR[]", t), e.ip += t - 1;
  }
  function Ju(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "SCVTCI[]", t), e.cvCutIn = t / 64;
  }
  function Ku(e) {
    var t = e.stack;
    g.DEBUG && console.log(e.step, "DUP[]"), t.push(t[t.length - 1]);
  }
  function cn(e) {
    g.DEBUG && console.log(e.step, "POP[]"), e.stack.pop();
  }
  function el(e) {
    g.DEBUG && console.log(e.step, "CLEAR[]"), e.stack.length = 0;
  }
  function tl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "SWAP[]"), t.push(r), t.push(n);
  }
  function rl(e) {
    var t = e.stack;
    g.DEBUG && console.log(e.step, "DEPTH[]"), t.push(t.length);
  }
  function nl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "LOOPCALL[]", r, n);
    var a = e.ip, s = e.prog;
    e.prog = e.funcs[r];
    for (var i = 0; i < n; i++)
      Tt(e), g.DEBUG && console.log(
        ++e.step,
        i + 1 < n ? "next loopcall" : "done loopcall",
        i
      );
    e.ip = a, e.prog = s;
  }
  function al(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "CALL[]", t);
    var r = e.ip, n = e.prog;
    e.prog = e.funcs[t], Tt(e), e.ip = r, e.prog = n, g.DEBUG && console.log(++e.step, "returning from", t);
  }
  function sl(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "CINDEX[]", r), t.push(t[t.length - r]);
  }
  function il(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "MINDEX[]", r), t.push(t.splice(t.length - r, 1)[0]);
  }
  function ol(e) {
    if (e.env !== "fpgm")
      throw new Error("FDEF not allowed here");
    var t = e.stack, r = e.prog, n = e.ip, a = t.pop(), s = n;
    for (g.DEBUG && console.log(e.step, "FDEF[]", a); r[++n] !== 45; )
      ;
    e.ip = n, e.funcs[a] = r.slice(s + 1, n);
  }
  function Ia(e, t) {
    var r = t.stack.pop(), n = t.z0[r], a = t.fv, s = t.pv;
    g.DEBUG && console.log(t.step, "MDAP[" + e + "]", r);
    var i = s.distance(n, tr);
    e && (i = t.round(i)), a.setRelative(n, tr, i, s), a.touch(n), t.rp0 = t.rp1 = r;
  }
  function Da(e, t) {
    var r = t.z2, n = r.length - 2, a, s, i;
    g.DEBUG && console.log(t.step, "IUP[" + e.axis + "]");
    for (var o = 0; o < n; o++)
      a = r[o], !e.touched(a) && (s = a.prevTouched(e), s !== a && (i = a.nextTouched(e), s === i && e.setRelative(a, a, e.distance(s, s, !1, !0), e, !0), e.interpolate(a, s, i, e)));
  }
  function Pa(e, t) {
    for (var r = t.stack, n = e ? t.rp1 : t.rp2, a = (e ? t.z0 : t.z1)[n], s = t.fv, i = t.pv, o = t.loop, u = t.z2; o--; ) {
      var l = r.pop(), c = u[l], h = i.distance(a, a, !1, !0);
      s.setRelative(c, c, h, i), s.touch(c), g.DEBUG && console.log(
        t.step,
        (t.loop > 1 ? "loop " + (t.loop - o) + ": " : "") + "SHP[" + (e ? "rp1" : "rp2") + "]",
        l
      );
    }
    t.loop = 1;
  }
  function Ma(e, t) {
    var r = t.stack, n = e ? t.rp1 : t.rp2, a = (e ? t.z0 : t.z1)[n], s = t.fv, i = t.pv, o = r.pop(), u = t.z2[t.contours[o]], l = u;
    g.DEBUG && console.log(t.step, "SHC[" + e + "]", o);
    var c = i.distance(a, a, !1, !0);
    do
      l !== a && s.setRelative(l, l, c, i), l = l.nextPointOnContour;
    while (l !== u);
  }
  function Na(e, t) {
    var r = t.stack, n = e ? t.rp1 : t.rp2, a = (e ? t.z0 : t.z1)[n], s = t.fv, i = t.pv, o = r.pop();
    g.DEBUG && console.log(t.step, "SHZ[" + e + "]", o);
    var u;
    switch (o) {
      case 0:
        u = t.tZone;
        break;
      case 1:
        u = t.gZone;
        break;
      default:
        throw new Error("Invalid zone");
    }
    for (var l, c = i.distance(a, a, !1, !0), h = u.length - 2, f = 0; f < h; f++)
      l = u[f], s.setRelative(l, l, c, i);
  }
  function ul(e) {
    for (var t = e.stack, r = e.loop, n = e.fv, a = t.pop() / 64, s = e.z2; r--; ) {
      var i = t.pop(), o = s[i];
      g.DEBUG && console.log(
        e.step,
        (e.loop > 1 ? "loop " + (e.loop - r) + ": " : "") + "SHPIX[]",
        i,
        a
      ), n.setRelative(o, o, a), n.touch(o);
    }
    e.loop = 1;
  }
  function ll(e) {
    for (var t = e.stack, r = e.rp1, n = e.rp2, a = e.loop, s = e.z0[r], i = e.z1[n], o = e.fv, u = e.dpv, l = e.z2; a--; ) {
      var c = t.pop(), h = l[c];
      g.DEBUG && console.log(
        e.step,
        (e.loop > 1 ? "loop " + (e.loop - a) + ": " : "") + "IP[]",
        c,
        r,
        "<->",
        n
      ), o.interpolate(h, s, i, u), o.touch(h);
    }
    e.loop = 1;
  }
  function Ba(e, t) {
    var r = t.stack, n = r.pop() / 64, a = r.pop(), s = t.z1[a], i = t.z0[t.rp0], o = t.fv, u = t.pv;
    o.setRelative(s, i, n, u), o.touch(s), g.DEBUG && console.log(t.step, "MSIRP[" + e + "]", n, a), t.rp1 = t.rp0, t.rp2 = a, e && (t.rp0 = a);
  }
  function cl(e) {
    for (var t = e.stack, r = e.rp0, n = e.z0[r], a = e.loop, s = e.fv, i = e.pv, o = e.z1; a--; ) {
      var u = t.pop(), l = o[u];
      g.DEBUG && console.log(
        e.step,
        (e.loop > 1 ? "loop " + (e.loop - a) + ": " : "") + "ALIGNRP[]",
        u
      ), s.setRelative(l, n, 0, i), s.touch(l);
    }
    e.loop = 1;
  }
  function fl(e) {
    g.DEBUG && console.log(e.step, "RTDG[]"), e.round = Ou;
  }
  function Ga(e, t) {
    var r = t.stack, n = r.pop(), a = r.pop(), s = t.z0[a], i = t.fv, o = t.pv, u = t.cvt[n];
    g.DEBUG && console.log(
      t.step,
      "MIAP[" + e + "]",
      n,
      "(",
      u,
      ")",
      a
    );
    var l = o.distance(s, tr);
    e && (Math.abs(l - u) < t.cvCutIn && (l = u), l = t.round(l)), i.setRelative(s, tr, l, o), t.zp0 === 0 && (s.xo = s.x, s.yo = s.y), i.touch(s), t.rp0 = t.rp1 = a;
  }
  function hl(e) {
    var t = e.prog, r = e.ip, n = e.stack, a = t[++r];
    g.DEBUG && console.log(e.step, "NPUSHB[]", a);
    for (var s = 0; s < a; s++)
      n.push(t[++r]);
    e.ip = r;
  }
  function dl(e) {
    var t = e.ip, r = e.prog, n = e.stack, a = r[++t];
    g.DEBUG && console.log(e.step, "NPUSHW[]", a);
    for (var s = 0; s < a; s++) {
      var i = r[++t] << 8 | r[++t];
      i & 32768 && (i = -((i ^ 65535) + 1)), n.push(i);
    }
    e.ip = t;
  }
  function pl(e) {
    var t = e.stack, r = e.store;
    r || (r = e.store = []);
    var n = t.pop(), a = t.pop();
    g.DEBUG && console.log(e.step, "WS", n, a), r[a] = n;
  }
  function vl(e) {
    var t = e.stack, r = e.store, n = t.pop();
    g.DEBUG && console.log(e.step, "RS", n);
    var a = r && r[n] || 0;
    t.push(a);
  }
  function gl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "WCVTP", r, n), e.cvt[n] = r / 64;
  }
  function ml(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "RCVT", r), t.push(e.cvt[r] * 64);
  }
  function Ha(e, t) {
    var r = t.stack, n = r.pop(), a = t.z2[n];
    g.DEBUG && console.log(t.step, "GC[" + e + "]", n), r.push(t.dpv.distance(a, tr, e, !1) * 64);
  }
  function Wa(e, t) {
    var r = t.stack, n = r.pop(), a = r.pop(), s = t.z1[n], i = t.z0[a], o = t.dpv.distance(i, s, e, e);
    g.DEBUG && console.log(t.step, "MD[" + e + "]", n, a, "->", o), t.stack.push(Math.round(o * 64));
  }
  function yl(e) {
    g.DEBUG && console.log(e.step, "MPPEM[]"), e.stack.push(e.ppem);
  }
  function xl(e) {
    g.DEBUG && console.log(e.step, "FLIPON[]"), e.autoFlip = !0;
  }
  function bl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "LT[]", r, n), t.push(n < r ? 1 : 0);
  }
  function Sl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "LTEQ[]", r, n), t.push(n <= r ? 1 : 0);
  }
  function kl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "GT[]", r, n), t.push(n > r ? 1 : 0);
  }
  function Tl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "GTEQ[]", r, n), t.push(n >= r ? 1 : 0);
  }
  function wl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "EQ[]", r, n), t.push(r === n ? 1 : 0);
  }
  function Cl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "NEQ[]", r, n), t.push(r !== n ? 1 : 0);
  }
  function El(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "ODD[]", r), t.push(Math.trunc(r) % 2 ? 1 : 0);
  }
  function Fl(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "EVEN[]", r), t.push(Math.trunc(r) % 2 ? 0 : 1);
  }
  function Ol(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "IF[]", t), t || (ui(e, !0), g.DEBUG && console.log(e.step, "EIF[]"));
  }
  function Rl(e) {
    g.DEBUG && console.log(e.step, "EIF[]");
  }
  function _l(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "AND[]", r, n), t.push(r && n ? 1 : 0);
  }
  function Ul(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "OR[]", r, n), t.push(r || n ? 1 : 0);
  }
  function Ll(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "NOT[]", r), t.push(r ? 0 : 1);
  }
  function fn(e, t) {
    var r = t.stack, n = r.pop(), a = t.fv, s = t.pv, i = t.ppem, o = t.deltaBase + (e - 1) * 16, u = t.deltaShift, l = t.z0;
    g.DEBUG && console.log(t.step, "DELTAP[" + e + "]", n, r);
    for (var c = 0; c < n; c++) {
      var h = r.pop(), f = r.pop(), d = o + ((f & 240) >> 4);
      if (d === i) {
        var v = (f & 15) - 8;
        v >= 0 && v++, g.DEBUG && console.log(t.step, "DELTAPFIX", h, "by", v * u);
        var S = l[h];
        a.setRelative(S, S, v * u, s);
      }
    }
  }
  function Al(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "SDB[]", r), e.deltaBase = r;
  }
  function Il(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "SDS[]", r), e.deltaShift = Math.pow(0.5, r);
  }
  function Dl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "ADD[]", r, n), t.push(n + r);
  }
  function Pl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "SUB[]", r, n), t.push(n - r);
  }
  function Ml(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "DIV[]", r, n), t.push(n * 64 / r);
  }
  function Nl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "MUL[]", r, n), t.push(n * r / 64);
  }
  function Bl(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "ABS[]", r), t.push(Math.abs(r));
  }
  function Gl(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "NEG[]", r), t.push(-r);
  }
  function Hl(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "FLOOR[]", r), t.push(Math.floor(r / 64) * 64);
  }
  function Wl(e) {
    var t = e.stack, r = t.pop();
    g.DEBUG && console.log(e.step, "CEILING[]", r), t.push(Math.ceil(r / 64) * 64);
  }
  function Ur(e, t) {
    var r = t.stack, n = r.pop();
    g.DEBUG && console.log(t.step, "ROUND[]"), r.push(t.round(n / 64) * 64);
  }
  function zl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "WCVTF[]", r, n), e.cvt[n] = r * e.ppem / e.font.unitsPerEm;
  }
  function hn(e, t) {
    var r = t.stack, n = r.pop(), a = t.ppem, s = t.deltaBase + (e - 1) * 16, i = t.deltaShift;
    g.DEBUG && console.log(t.step, "DELTAC[" + e + "]", n, r);
    for (var o = 0; o < n; o++) {
      var u = r.pop(), l = r.pop(), c = s + ((l & 240) >> 4);
      if (c === a) {
        var h = (l & 15) - 8;
        h >= 0 && h++;
        var f = h * i;
        g.DEBUG && console.log(t.step, "DELTACFIX", u, "by", f), t.cvt[u] += f;
      }
    }
  }
  function jl(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "SROUND[]", t), e.round = oi;
    var r;
    switch (t & 192) {
      case 0:
        r = 0.5;
        break;
      case 64:
        r = 1;
        break;
      case 128:
        r = 2;
        break;
      default:
        throw new Error("invalid SROUND value");
    }
    switch (e.srPeriod = r, t & 48) {
      case 0:
        e.srPhase = 0;
        break;
      case 16:
        e.srPhase = 0.25 * r;
        break;
      case 32:
        e.srPhase = 0.5 * r;
        break;
      case 48:
        e.srPhase = 0.75 * r;
        break;
      default:
        throw new Error("invalid SROUND value");
    }
    t &= 15, t === 0 ? e.srThreshold = 0 : e.srThreshold = (t / 8 - 0.5) * r;
  }
  function Zl(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "S45ROUND[]", t), e.round = oi;
    var r;
    switch (t & 192) {
      case 0:
        r = Math.sqrt(2) / 2;
        break;
      case 64:
        r = Math.sqrt(2);
        break;
      case 128:
        r = 2 * Math.sqrt(2);
        break;
      default:
        throw new Error("invalid S45ROUND value");
    }
    switch (e.srPeriod = r, t & 48) {
      case 0:
        e.srPhase = 0;
        break;
      case 16:
        e.srPhase = 0.25 * r;
        break;
      case 32:
        e.srPhase = 0.5 * r;
        break;
      case 48:
        e.srPhase = 0.75 * r;
        break;
      default:
        throw new Error("invalid S45ROUND value");
    }
    t &= 15, t === 0 ? e.srThreshold = 0 : e.srThreshold = (t / 8 - 0.5) * r;
  }
  function Vl(e) {
    g.DEBUG && console.log(e.step, "ROFF[]"), e.round = Fu;
  }
  function ql(e) {
    g.DEBUG && console.log(e.step, "RUTG[]"), e.round = _u;
  }
  function $l(e) {
    g.DEBUG && console.log(e.step, "RDTG[]"), e.round = Uu;
  }
  function Xl(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "SCANCTRL[]", t);
  }
  function za(e, t) {
    var r = t.stack, n = r.pop(), a = r.pop(), s = t.z2[n], i = t.z1[a];
    g.DEBUG && console.log(t.step, "SDPVTL[" + e + "]", n, a);
    var o, u;
    e ? (o = s.y - i.y, u = i.x - s.x) : (o = i.x - s.x, u = i.y - s.y), t.dpv = kr(o, u);
  }
  function Yl(e) {
    var t = e.stack, r = t.pop(), n = 0;
    g.DEBUG && console.log(e.step, "GETINFO[]", r), r & 1 && (n = 35), r & 32 && (n |= 4096), t.push(n);
  }
  function Ql(e) {
    var t = e.stack, r = t.pop(), n = t.pop(), a = t.pop();
    g.DEBUG && console.log(e.step, "ROLL[]"), t.push(n), t.push(r), t.push(a);
  }
  function Jl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "MAX[]", r, n), t.push(Math.max(n, r));
  }
  function Kl(e) {
    var t = e.stack, r = t.pop(), n = t.pop();
    g.DEBUG && console.log(e.step, "MIN[]", r, n), t.push(Math.min(n, r));
  }
  function ec(e) {
    var t = e.stack.pop();
    g.DEBUG && console.log(e.step, "SCANTYPE[]", t);
  }
  function tc(e) {
    var t = e.stack.pop(), r = e.stack.pop();
    switch (g.DEBUG && console.log(e.step, "INSTCTRL[]", t, r), t) {
      case 1:
        e.inhibitGridFit = !!r;
        return;
      case 2:
        e.ignoreCvt = !!r;
        return;
      default:
        throw new Error("invalid INSTCTRL[] selector");
    }
  }
  function Xe(e, t) {
    var r = t.stack, n = t.prog, a = t.ip;
    g.DEBUG && console.log(t.step, "PUSHB[" + e + "]");
    for (var s = 0; s < e; s++)
      r.push(n[++a]);
    t.ip = a;
  }
  function Ye(e, t) {
    var r = t.ip, n = t.prog, a = t.stack;
    g.DEBUG && console.log(t.ip, "PUSHW[" + e + "]");
    for (var s = 0; s < e; s++) {
      var i = n[++r] << 8 | n[++r];
      i & 32768 && (i = -((i ^ 65535) + 1)), a.push(i);
    }
    t.ip = r;
  }
  function _(e, t, r, n, a, s) {
    var i = s.stack, o = e && i.pop(), u = i.pop(), l = s.rp0, c = s.z0[l], h = s.z1[u], f = s.minDis, d = s.fv, v = s.dpv, S, m, x, E;
    m = S = v.distance(h, c, !0, !0), x = m >= 0 ? 1 : -1, m = Math.abs(m), e && (E = s.cvt[o], n && Math.abs(m - E) < s.cvCutIn && (m = E)), r && m < f && (m = f), n && (m = s.round(m)), d.setRelative(h, c, x * m, v), d.touch(h), g.DEBUG && console.log(
      s.step,
      (e ? "MIRP[" : "MDRP[") + (t ? "M" : "m") + (r ? ">" : "_") + (n ? "R" : "_") + (a === 0 ? "Gr" : a === 1 ? "Bl" : a === 2 ? "Wh" : "") + "]",
      e ? o + "(" + s.cvt[o] + "," + E + ")" : "",
      u,
      "(d =",
      S,
      "->",
      x * m,
      ")"
    ), s.rp1 = s.rp0, s.rp2 = u, t && (s.rp0 = u);
  }
  ni = [
    /* 0x00 */
    Ra.bind(void 0, Ze),
    /* 0x01 */
    Ra.bind(void 0, He),
    /* 0x02 */
    _a.bind(void 0, Ze),
    /* 0x03 */
    _a.bind(void 0, He),
    /* 0x04 */
    Ua.bind(void 0, Ze),
    /* 0x05 */
    Ua.bind(void 0, He),
    /* 0x06 */
    La.bind(void 0, 0),
    /* 0x07 */
    La.bind(void 0, 1),
    /* 0x08 */
    Aa.bind(void 0, 0),
    /* 0x09 */
    Aa.bind(void 0, 1),
    /* 0x0A */
    Au,
    /* 0x0B */
    Iu,
    /* 0x0C */
    Du,
    /* 0x0D */
    Pu,
    /* 0x0E */
    Mu,
    /* 0x0F */
    Nu,
    /* 0x10 */
    Bu,
    /* 0x11 */
    Gu,
    /* 0x12 */
    Hu,
    /* 0x13 */
    Wu,
    /* 0x14 */
    zu,
    /* 0x15 */
    ju,
    /* 0x16 */
    Zu,
    /* 0x17 */
    Vu,
    /* 0x18 */
    qu,
    /* 0x19 */
    $u,
    /* 0x1A */
    Xu,
    /* 0x1B */
    Yu,
    /* 0x1C */
    Qu,
    /* 0x1D */
    Ju,
    /* 0x1E */
    void 0,
    // TODO SSWCI
    /* 0x1F */
    void 0,
    // TODO SSW
    /* 0x20 */
    Ku,
    /* 0x21 */
    cn,
    /* 0x22 */
    el,
    /* 0x23 */
    tl,
    /* 0x24 */
    rl,
    /* 0x25 */
    sl,
    /* 0x26 */
    il,
    /* 0x27 */
    void 0,
    // TODO ALIGNPTS
    /* 0x28 */
    void 0,
    /* 0x29 */
    void 0,
    // TODO UTP
    /* 0x2A */
    nl,
    /* 0x2B */
    al,
    /* 0x2C */
    ol,
    /* 0x2D */
    void 0,
    // ENDF (eaten by FDEF)
    /* 0x2E */
    Ia.bind(void 0, 0),
    /* 0x2F */
    Ia.bind(void 0, 1),
    /* 0x30 */
    Da.bind(void 0, Ze),
    /* 0x31 */
    Da.bind(void 0, He),
    /* 0x32 */
    Pa.bind(void 0, 0),
    /* 0x33 */
    Pa.bind(void 0, 1),
    /* 0x34 */
    Ma.bind(void 0, 0),
    /* 0x35 */
    Ma.bind(void 0, 1),
    /* 0x36 */
    Na.bind(void 0, 0),
    /* 0x37 */
    Na.bind(void 0, 1),
    /* 0x38 */
    ul,
    /* 0x39 */
    ll,
    /* 0x3A */
    Ba.bind(void 0, 0),
    /* 0x3B */
    Ba.bind(void 0, 1),
    /* 0x3C */
    cl,
    /* 0x3D */
    fl,
    /* 0x3E */
    Ga.bind(void 0, 0),
    /* 0x3F */
    Ga.bind(void 0, 1),
    /* 0x40 */
    hl,
    /* 0x41 */
    dl,
    /* 0x42 */
    pl,
    /* 0x43 */
    vl,
    /* 0x44 */
    gl,
    /* 0x45 */
    ml,
    /* 0x46 */
    Ha.bind(void 0, 0),
    /* 0x47 */
    Ha.bind(void 0, 1),
    /* 0x48 */
    void 0,
    // TODO SCFS
    /* 0x49 */
    Wa.bind(void 0, 0),
    /* 0x4A */
    Wa.bind(void 0, 1),
    /* 0x4B */
    yl,
    /* 0x4C */
    void 0,
    // TODO MPS
    /* 0x4D */
    xl,
    /* 0x4E */
    void 0,
    // TODO FLIPOFF
    /* 0x4F */
    void 0,
    // TODO DEBUG
    /* 0x50 */
    bl,
    /* 0x51 */
    Sl,
    /* 0x52 */
    kl,
    /* 0x53 */
    Tl,
    /* 0x54 */
    wl,
    /* 0x55 */
    Cl,
    /* 0x56 */
    El,
    /* 0x57 */
    Fl,
    /* 0x58 */
    Ol,
    /* 0x59 */
    Rl,
    /* 0x5A */
    _l,
    /* 0x5B */
    Ul,
    /* 0x5C */
    Ll,
    /* 0x5D */
    fn.bind(void 0, 1),
    /* 0x5E */
    Al,
    /* 0x5F */
    Il,
    /* 0x60 */
    Dl,
    /* 0x61 */
    Pl,
    /* 0x62 */
    Ml,
    /* 0x63 */
    Nl,
    /* 0x64 */
    Bl,
    /* 0x65 */
    Gl,
    /* 0x66 */
    Hl,
    /* 0x67 */
    Wl,
    /* 0x68 */
    Ur.bind(void 0, 0),
    /* 0x69 */
    Ur.bind(void 0, 1),
    /* 0x6A */
    Ur.bind(void 0, 2),
    /* 0x6B */
    Ur.bind(void 0, 3),
    /* 0x6C */
    void 0,
    // TODO NROUND[ab]
    /* 0x6D */
    void 0,
    // TODO NROUND[ab]
    /* 0x6E */
    void 0,
    // TODO NROUND[ab]
    /* 0x6F */
    void 0,
    // TODO NROUND[ab]
    /* 0x70 */
    zl,
    /* 0x71 */
    fn.bind(void 0, 2),
    /* 0x72 */
    fn.bind(void 0, 3),
    /* 0x73 */
    hn.bind(void 0, 1),
    /* 0x74 */
    hn.bind(void 0, 2),
    /* 0x75 */
    hn.bind(void 0, 3),
    /* 0x76 */
    jl,
    /* 0x77 */
    Zl,
    /* 0x78 */
    void 0,
    // TODO JROT[]
    /* 0x79 */
    void 0,
    // TODO JROF[]
    /* 0x7A */
    Vl,
    /* 0x7B */
    void 0,
    /* 0x7C */
    ql,
    /* 0x7D */
    $l,
    /* 0x7E */
    cn,
    // actually SANGW, supposed to do only a pop though
    /* 0x7F */
    cn,
    // actually AA, supposed to do only a pop though
    /* 0x80 */
    void 0,
    // TODO FLIPPT
    /* 0x81 */
    void 0,
    // TODO FLIPRGON
    /* 0x82 */
    void 0,
    // TODO FLIPRGOFF
    /* 0x83 */
    void 0,
    /* 0x84 */
    void 0,
    /* 0x85 */
    Xl,
    /* 0x86 */
    za.bind(void 0, 0),
    /* 0x87 */
    za.bind(void 0, 1),
    /* 0x88 */
    Yl,
    /* 0x89 */
    void 0,
    // TODO IDEF
    /* 0x8A */
    Ql,
    /* 0x8B */
    Jl,
    /* 0x8C */
    Kl,
    /* 0x8D */
    ec,
    /* 0x8E */
    tc,
    /* 0x8F */
    void 0,
    /* 0x90 */
    void 0,
    /* 0x91 */
    void 0,
    /* 0x92 */
    void 0,
    /* 0x93 */
    void 0,
    /* 0x94 */
    void 0,
    /* 0x95 */
    void 0,
    /* 0x96 */
    void 0,
    /* 0x97 */
    void 0,
    /* 0x98 */
    void 0,
    /* 0x99 */
    void 0,
    /* 0x9A */
    void 0,
    /* 0x9B */
    void 0,
    /* 0x9C */
    void 0,
    /* 0x9D */
    void 0,
    /* 0x9E */
    void 0,
    /* 0x9F */
    void 0,
    /* 0xA0 */
    void 0,
    /* 0xA1 */
    void 0,
    /* 0xA2 */
    void 0,
    /* 0xA3 */
    void 0,
    /* 0xA4 */
    void 0,
    /* 0xA5 */
    void 0,
    /* 0xA6 */
    void 0,
    /* 0xA7 */
    void 0,
    /* 0xA8 */
    void 0,
    /* 0xA9 */
    void 0,
    /* 0xAA */
    void 0,
    /* 0xAB */
    void 0,
    /* 0xAC */
    void 0,
    /* 0xAD */
    void 0,
    /* 0xAE */
    void 0,
    /* 0xAF */
    void 0,
    /* 0xB0 */
    Xe.bind(void 0, 1),
    /* 0xB1 */
    Xe.bind(void 0, 2),
    /* 0xB2 */
    Xe.bind(void 0, 3),
    /* 0xB3 */
    Xe.bind(void 0, 4),
    /* 0xB4 */
    Xe.bind(void 0, 5),
    /* 0xB5 */
    Xe.bind(void 0, 6),
    /* 0xB6 */
    Xe.bind(void 0, 7),
    /* 0xB7 */
    Xe.bind(void 0, 8),
    /* 0xB8 */
    Ye.bind(void 0, 1),
    /* 0xB9 */
    Ye.bind(void 0, 2),
    /* 0xBA */
    Ye.bind(void 0, 3),
    /* 0xBB */
    Ye.bind(void 0, 4),
    /* 0xBC */
    Ye.bind(void 0, 5),
    /* 0xBD */
    Ye.bind(void 0, 6),
    /* 0xBE */
    Ye.bind(void 0, 7),
    /* 0xBF */
    Ye.bind(void 0, 8),
    /* 0xC0 */
    _.bind(void 0, 0, 0, 0, 0, 0),
    /* 0xC1 */
    _.bind(void 0, 0, 0, 0, 0, 1),
    /* 0xC2 */
    _.bind(void 0, 0, 0, 0, 0, 2),
    /* 0xC3 */
    _.bind(void 0, 0, 0, 0, 0, 3),
    /* 0xC4 */
    _.bind(void 0, 0, 0, 0, 1, 0),
    /* 0xC5 */
    _.bind(void 0, 0, 0, 0, 1, 1),
    /* 0xC6 */
    _.bind(void 0, 0, 0, 0, 1, 2),
    /* 0xC7 */
    _.bind(void 0, 0, 0, 0, 1, 3),
    /* 0xC8 */
    _.bind(void 0, 0, 0, 1, 0, 0),
    /* 0xC9 */
    _.bind(void 0, 0, 0, 1, 0, 1),
    /* 0xCA */
    _.bind(void 0, 0, 0, 1, 0, 2),
    /* 0xCB */
    _.bind(void 0, 0, 0, 1, 0, 3),
    /* 0xCC */
    _.bind(void 0, 0, 0, 1, 1, 0),
    /* 0xCD */
    _.bind(void 0, 0, 0, 1, 1, 1),
    /* 0xCE */
    _.bind(void 0, 0, 0, 1, 1, 2),
    /* 0xCF */
    _.bind(void 0, 0, 0, 1, 1, 3),
    /* 0xD0 */
    _.bind(void 0, 0, 1, 0, 0, 0),
    /* 0xD1 */
    _.bind(void 0, 0, 1, 0, 0, 1),
    /* 0xD2 */
    _.bind(void 0, 0, 1, 0, 0, 2),
    /* 0xD3 */
    _.bind(void 0, 0, 1, 0, 0, 3),
    /* 0xD4 */
    _.bind(void 0, 0, 1, 0, 1, 0),
    /* 0xD5 */
    _.bind(void 0, 0, 1, 0, 1, 1),
    /* 0xD6 */
    _.bind(void 0, 0, 1, 0, 1, 2),
    /* 0xD7 */
    _.bind(void 0, 0, 1, 0, 1, 3),
    /* 0xD8 */
    _.bind(void 0, 0, 1, 1, 0, 0),
    /* 0xD9 */
    _.bind(void 0, 0, 1, 1, 0, 1),
    /* 0xDA */
    _.bind(void 0, 0, 1, 1, 0, 2),
    /* 0xDB */
    _.bind(void 0, 0, 1, 1, 0, 3),
    /* 0xDC */
    _.bind(void 0, 0, 1, 1, 1, 0),
    /* 0xDD */
    _.bind(void 0, 0, 1, 1, 1, 1),
    /* 0xDE */
    _.bind(void 0, 0, 1, 1, 1, 2),
    /* 0xDF */
    _.bind(void 0, 0, 1, 1, 1, 3),
    /* 0xE0 */
    _.bind(void 0, 1, 0, 0, 0, 0),
    /* 0xE1 */
    _.bind(void 0, 1, 0, 0, 0, 1),
    /* 0xE2 */
    _.bind(void 0, 1, 0, 0, 0, 2),
    /* 0xE3 */
    _.bind(void 0, 1, 0, 0, 0, 3),
    /* 0xE4 */
    _.bind(void 0, 1, 0, 0, 1, 0),
    /* 0xE5 */
    _.bind(void 0, 1, 0, 0, 1, 1),
    /* 0xE6 */
    _.bind(void 0, 1, 0, 0, 1, 2),
    /* 0xE7 */
    _.bind(void 0, 1, 0, 0, 1, 3),
    /* 0xE8 */
    _.bind(void 0, 1, 0, 1, 0, 0),
    /* 0xE9 */
    _.bind(void 0, 1, 0, 1, 0, 1),
    /* 0xEA */
    _.bind(void 0, 1, 0, 1, 0, 2),
    /* 0xEB */
    _.bind(void 0, 1, 0, 1, 0, 3),
    /* 0xEC */
    _.bind(void 0, 1, 0, 1, 1, 0),
    /* 0xED */
    _.bind(void 0, 1, 0, 1, 1, 1),
    /* 0xEE */
    _.bind(void 0, 1, 0, 1, 1, 2),
    /* 0xEF */
    _.bind(void 0, 1, 0, 1, 1, 3),
    /* 0xF0 */
    _.bind(void 0, 1, 1, 0, 0, 0),
    /* 0xF1 */
    _.bind(void 0, 1, 1, 0, 0, 1),
    /* 0xF2 */
    _.bind(void 0, 1, 1, 0, 0, 2),
    /* 0xF3 */
    _.bind(void 0, 1, 1, 0, 0, 3),
    /* 0xF4 */
    _.bind(void 0, 1, 1, 0, 1, 0),
    /* 0xF5 */
    _.bind(void 0, 1, 1, 0, 1, 1),
    /* 0xF6 */
    _.bind(void 0, 1, 1, 0, 1, 2),
    /* 0xF7 */
    _.bind(void 0, 1, 1, 0, 1, 3),
    /* 0xF8 */
    _.bind(void 0, 1, 1, 1, 0, 0),
    /* 0xF9 */
    _.bind(void 0, 1, 1, 1, 0, 1),
    /* 0xFA */
    _.bind(void 0, 1, 1, 1, 0, 2),
    /* 0xFB */
    _.bind(void 0, 1, 1, 1, 0, 3),
    /* 0xFC */
    _.bind(void 0, 1, 1, 1, 1, 0),
    /* 0xFD */
    _.bind(void 0, 1, 1, 1, 1, 1),
    /* 0xFE */
    _.bind(void 0, 1, 1, 1, 1, 2),
    /* 0xFF */
    _.bind(void 0, 1, 1, 1, 1, 3)
  ];
  function Zt(e) {
    this.char = e, this.state = {}, this.activeState = null;
  }
  function Yn(e, t, r) {
    this.contextName = r, this.startIndex = e, this.endOffset = t;
  }
  function rc(e, t, r) {
    this.contextName = e, this.openRange = null, this.ranges = [], this.checkStart = t, this.checkEnd = r;
  }
  function Le(e, t) {
    this.context = e, this.index = t, this.length = e.length, this.current = e[t], this.backtrack = e.slice(0, t), this.lookahead = e.slice(t + 1);
  }
  function tn(e) {
    this.eventId = e, this.subscribers = [];
  }
  function nc(e) {
    var t = this, r = [
      "start",
      "end",
      "next",
      "newToken",
      "contextStart",
      "contextEnd",
      "insertToken",
      "removeToken",
      "removeRange",
      "replaceToken",
      "replaceRange",
      "composeRUD",
      "updateContextsRanges"
    ];
    r.forEach(function(a) {
      Object.defineProperty(t.events, a, {
        value: new tn(a)
      });
    }), e && r.forEach(function(a) {
      var s = e[a];
      typeof s == "function" && t.events[a].subscribe(s);
    });
    var n = [
      "insertToken",
      "removeToken",
      "removeRange",
      "replaceToken",
      "replaceRange",
      "composeRUD"
    ];
    n.forEach(function(a) {
      t.events[a].subscribe(
        t.updateContextsRanges
      );
    });
  }
  function fe(e) {
    this.tokens = [], this.registeredContexts = {}, this.contextCheckers = [], this.events = {}, this.registeredModifiers = [], nc.call(this, e);
  }
  Zt.prototype.setState = function(e, t) {
    return this.state[e] = t, this.activeState = { key: e, value: this.state[e] }, this.activeState;
  };
  Zt.prototype.getState = function(e) {
    return this.state[e] || null;
  };
  fe.prototype.inboundIndex = function(e) {
    return e >= 0 && e < this.tokens.length;
  };
  fe.prototype.composeRUD = function(e) {
    var t = this, r = !0, n = e.map(function(s) {
      return t[s[0]].apply(t, s.slice(1).concat(r));
    }), a = function(s) {
      return typeof s == "object" && s.hasOwnProperty("FAIL");
    };
    if (n.every(a))
      return {
        FAIL: "composeRUD: one or more operations hasn't completed successfully",
        report: n.filter(a)
      };
    this.dispatch("composeRUD", [n.filter(function(s) {
      return !a(s);
    })]);
  };
  fe.prototype.replaceRange = function(e, t, r, n) {
    t = t !== null ? t : this.tokens.length;
    var a = r.every(function(i) {
      return i instanceof Zt;
    });
    if (!isNaN(e) && this.inboundIndex(e) && a) {
      var s = this.tokens.splice.apply(
        this.tokens,
        [e, t].concat(r)
      );
      return n || this.dispatch("replaceToken", [e, t, r]), [s, r];
    } else
      return { FAIL: "replaceRange: invalid tokens or startIndex." };
  };
  fe.prototype.replaceToken = function(e, t, r) {
    if (!isNaN(e) && this.inboundIndex(e) && t instanceof Zt) {
      var n = this.tokens.splice(e, 1, t);
      return r || this.dispatch("replaceToken", [e, t]), [n[0], t];
    } else
      return { FAIL: "replaceToken: invalid token or index." };
  };
  fe.prototype.removeRange = function(e, t, r) {
    t = isNaN(t) ? this.tokens.length : t;
    var n = this.tokens.splice(e, t);
    return r || this.dispatch("removeRange", [n, e, t]), n;
  };
  fe.prototype.removeToken = function(e, t) {
    if (!isNaN(e) && this.inboundIndex(e)) {
      var r = this.tokens.splice(e, 1);
      return t || this.dispatch("removeToken", [r, e]), r;
    } else
      return { FAIL: "removeToken: invalid token index." };
  };
  fe.prototype.insertToken = function(e, t, r) {
    var n = e.every(
      function(a) {
        return a instanceof Zt;
      }
    );
    return n ? (this.tokens.splice.apply(
      this.tokens,
      [t, 0].concat(e)
    ), r || this.dispatch("insertToken", [e, t]), e) : { FAIL: "insertToken: invalid token(s)." };
  };
  fe.prototype.registerModifier = function(e, t, r) {
    this.events.newToken.subscribe(function(n, a) {
      var s = [n, a], i = t === null || t.apply(this, s) === !0, o = [n, a];
      if (i) {
        var u = r.apply(this, o);
        n.setState(e, u);
      }
    }), this.registeredModifiers.push(e);
  };
  tn.prototype.subscribe = function(e) {
    return typeof e == "function" ? this.subscribers.push(e) - 1 : { FAIL: "invalid '" + this.eventId + "' event handler" };
  };
  tn.prototype.unsubscribe = function(e) {
    this.subscribers.splice(e, 1);
  };
  Le.prototype.setCurrentIndex = function(e) {
    this.index = e, this.current = this.context[e], this.backtrack = this.context.slice(0, e), this.lookahead = this.context.slice(e + 1);
  };
  Le.prototype.get = function(e) {
    switch (!0) {
      case e === 0:
        return this.current;
      case (e < 0 && Math.abs(e) <= this.backtrack.length):
        return this.backtrack.slice(e)[0];
      case (e > 0 && e <= this.lookahead.length):
        return this.lookahead[e - 1];
      default:
        return null;
    }
  };
  fe.prototype.rangeToText = function(e) {
    if (e instanceof Yn)
      return this.getRangeTokens(e).map(function(t) {
        return t.char;
      }).join("");
  };
  fe.prototype.getText = function() {
    return this.tokens.map(function(e) {
      return e.char;
    }).join("");
  };
  fe.prototype.getContext = function(e) {
    var t = this.registeredContexts[e];
    return t || null;
  };
  fe.prototype.on = function(e, t) {
    var r = this.events[e];
    return r ? r.subscribe(t) : null;
  };
  fe.prototype.dispatch = function(e, t) {
    var r = this, n = this.events[e];
    n instanceof tn && n.subscribers.forEach(function(a) {
      a.apply(r, t || []);
    });
  };
  fe.prototype.registerContextChecker = function(e, t, r) {
    if (this.getContext(e))
      return {
        FAIL: "context name '" + e + "' is already registered."
      };
    if (typeof t != "function")
      return {
        FAIL: "missing context start check."
      };
    if (typeof r != "function")
      return {
        FAIL: "missing context end check."
      };
    var n = new rc(
      e,
      t,
      r
    );
    return this.registeredContexts[e] = n, this.contextCheckers.push(n), n;
  };
  fe.prototype.getRangeTokens = function(e) {
    var t = e.startIndex + e.endOffset;
    return [].concat(
      this.tokens.slice(e.startIndex, t)
    );
  };
  fe.prototype.getContextRanges = function(e) {
    var t = this.getContext(e);
    return t ? t.ranges : { FAIL: "context checker '" + e + "' is not registered." };
  };
  fe.prototype.resetContextsRanges = function() {
    var e = this.registeredContexts;
    for (var t in e)
      if (e.hasOwnProperty(t)) {
        var r = e[t];
        r.ranges = [];
      }
  };
  fe.prototype.updateContextsRanges = function() {
    this.resetContextsRanges();
    for (var e = this.tokens.map(function(n) {
      return n.char;
    }), t = 0; t < e.length; t++) {
      var r = new Le(e, t);
      this.runContextCheck(r);
    }
    this.dispatch("updateContextsRanges", [this.registeredContexts]);
  };
  fe.prototype.setEndOffset = function(e, t) {
    var r = this.getContext(t).openRange.startIndex, n = new Yn(r, e, t), a = this.getContext(t).ranges;
    return n.rangeId = t + "." + a.length, a.push(n), this.getContext(t).openRange = null, n;
  };
  fe.prototype.runContextCheck = function(e) {
    var t = this, r = e.index;
    this.contextCheckers.forEach(function(n) {
      var a = n.contextName, s = t.getContext(a).openRange;
      if (!s && n.checkStart(e) && (s = new Yn(r, null, a), t.getContext(a).openRange = s, t.dispatch("contextStart", [a, r])), s && n.checkEnd(e)) {
        var i = r - s.startIndex + 1, o = t.setEndOffset(i, a);
        t.dispatch("contextEnd", [a, o]);
      }
    });
  };
  fe.prototype.tokenize = function(e) {
    this.tokens = [], this.resetContextsRanges();
    var t = Array.from(e);
    this.dispatch("start");
    for (var r = 0; r < t.length; r++) {
      var n = t[r], a = new Le(t, r);
      this.dispatch("next", [a]), this.runContextCheck(a);
      var s = new Zt(n);
      this.tokens.push(s), this.dispatch("newToken", [s, a]);
    }
    return this.dispatch("end", [this.tokens]), this.tokens;
  };
  function st(e) {
    return /[\u0600-\u065F\u066A-\u06D2\u06FA-\u06FF]/.test(e);
  }
  function li(e) {
    return /[\u0630\u0690\u0621\u0631\u0661\u0671\u0622\u0632\u0672\u0692\u06C2\u0623\u0673\u0693\u06C3\u0624\u0694\u06C4\u0625\u0675\u0695\u06C5\u06E5\u0676\u0696\u06C6\u0627\u0677\u0697\u06C7\u0648\u0688\u0698\u06C8\u0689\u0699\u06C9\u068A\u06CA\u066B\u068B\u06CB\u068C\u068D\u06CD\u06FD\u068E\u06EE\u06FE\u062F\u068F\u06CF\u06EF]/.test(e);
  }
  function it(e) {
    return /[\u0600-\u0605\u060C-\u060E\u0610-\u061B\u061E\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED]/.test(e);
  }
  function Dr(e) {
    return /[A-z]/.test(e);
  }
  function ac(e) {
    return /\s/.test(e);
  }
  function we(e) {
    this.font = e, this.features = {};
  }
  function vt(e) {
    this.id = e.id, this.tag = e.tag, this.substitution = e.substitution;
  }
  function Tr(e, t) {
    if (!e)
      return -1;
    switch (t.format) {
      case 1:
        return t.glyphs.indexOf(e);
      case 2:
        for (var r = t.ranges, n = 0; n < r.length; n++) {
          var a = r[n];
          if (e >= a.start && e <= a.end) {
            var s = e - a.start;
            return a.index + s;
          }
        }
        break;
      default:
        return -1;
    }
    return -1;
  }
  function sc(e, t) {
    var r = Tr(e, t.coverage);
    return r === -1 ? null : e + t.deltaGlyphId;
  }
  function ic(e, t) {
    var r = Tr(e, t.coverage);
    return r === -1 ? null : t.substitute[r];
  }
  function dn(e, t) {
    for (var r = [], n = 0; n < e.length; n++) {
      var a = e[n], s = t.current;
      s = Array.isArray(s) ? s[0] : s;
      var i = Tr(s, a);
      i !== -1 && r.push(i);
    }
    return r.length !== e.length ? -1 : r;
  }
  function oc(e, t) {
    var r = t.inputCoverage.length + t.lookaheadCoverage.length + t.backtrackCoverage.length;
    if (e.context.length < r)
      return [];
    var n = dn(
      t.inputCoverage,
      e
    );
    if (n === -1)
      return [];
    var a = t.inputCoverage.length - 1;
    if (e.lookahead.length < t.lookaheadCoverage.length)
      return [];
    for (var s = e.lookahead.slice(a); s.length && it(s[0].char); )
      s.shift();
    var i = new Le(s, 0), o = dn(
      t.lookaheadCoverage,
      i
    ), u = [].concat(e.backtrack);
    for (u.reverse(); u.length && it(u[0].char); )
      u.shift();
    if (u.length < t.backtrackCoverage.length)
      return [];
    var l = new Le(u, 0), c = dn(
      t.backtrackCoverage,
      l
    ), h = n.length === t.inputCoverage.length && o.length === t.lookaheadCoverage.length && c.length === t.backtrackCoverage.length, f = [];
    if (h)
      for (var d = 0; d < t.lookupRecords.length; d++)
        for (var v = t.lookupRecords[d], S = v.lookupListIndex, m = this.getLookupByIndex(S), x = 0; x < m.subtables.length; x++) {
          var E = m.subtables[x], b = this.getLookupMethod(m, E), M = this.getSubstitutionType(m, E);
          if (M === "12")
            for (var I = 0; I < n.length; I++) {
              var W = e.get(I), U = b(W);
              U && f.push(U);
            }
        }
    return f;
  }
  function uc(e, t) {
    var r = e.current, n = Tr(r, t.coverage);
    if (n === -1)
      return null;
    for (var a, s = t.ligatureSets[n], i = 0; i < s.length; i++) {
      a = s[i];
      for (var o = 0; o < a.components.length; o++) {
        var u = e.lookahead[o], l = a.components[o];
        if (u !== l)
          break;
        if (o === a.components.length - 1)
          return a;
      }
    }
    return null;
  }
  function lc(e, t) {
    var r = Tr(e, t.coverage);
    return r === -1 ? null : t.sequences[r];
  }
  we.prototype.getDefaultScriptFeaturesIndexes = function() {
    for (var e = this.font.tables.gsub.scripts, t = 0; t < e.length; t++) {
      var r = e[t];
      if (r.tag === "DFLT")
        return r.script.defaultLangSys.featureIndexes;
    }
    return [];
  };
  we.prototype.getScriptFeaturesIndexes = function(e) {
    var t = this.font.tables;
    if (!t.gsub)
      return [];
    if (!e)
      return this.getDefaultScriptFeaturesIndexes();
    for (var r = this.font.tables.gsub.scripts, n = 0; n < r.length; n++) {
      var a = r[n];
      if (a.tag === e && a.script.defaultLangSys)
        return a.script.defaultLangSys.featureIndexes;
      var s = a.langSysRecords;
      if (s)
        for (var i = 0; i < s.length; i++) {
          var o = s[i];
          if (o.tag === e) {
            var u = o.langSys;
            return u.featureIndexes;
          }
        }
    }
    return this.getDefaultScriptFeaturesIndexes();
  };
  we.prototype.mapTagsToFeatures = function(e, t) {
    for (var r = {}, n = 0; n < e.length; n++) {
      var a = e[n].tag, s = e[n].feature;
      r[a] = s;
    }
    this.features[t].tags = r;
  };
  we.prototype.getScriptFeatures = function(e) {
    var t = this.features[e];
    if (this.features.hasOwnProperty(e))
      return t;
    var r = this.getScriptFeaturesIndexes(e);
    if (!r)
      return null;
    var n = this.font.tables.gsub;
    return t = r.map(function(a) {
      return n.features[a];
    }), this.features[e] = t, this.mapTagsToFeatures(t, e), t;
  };
  we.prototype.getSubstitutionType = function(e, t) {
    var r = e.lookupType.toString(), n = t.substFormat.toString();
    return r + n;
  };
  we.prototype.getLookupMethod = function(e, t) {
    var r = this, n = this.getSubstitutionType(e, t);
    switch (n) {
      case "11":
        return function(a) {
          return sc.apply(
            r,
            [a, t]
          );
        };
      case "12":
        return function(a) {
          return ic.apply(
            r,
            [a, t]
          );
        };
      case "63":
        return function(a) {
          return oc.apply(
            r,
            [a, t]
          );
        };
      case "41":
        return function(a) {
          return uc.apply(
            r,
            [a, t]
          );
        };
      case "21":
        return function(a) {
          return lc.apply(
            r,
            [a, t]
          );
        };
      default:
        throw new Error(
          "lookupType: " + e.lookupType + " - substFormat: " + t.substFormat + " is not yet supported"
        );
    }
  };
  we.prototype.lookupFeature = function(e) {
    var t = e.contextParams, r = t.index, n = this.getFeature({
      tag: e.tag,
      script: e.script
    });
    if (!n)
      return new Error(
        "font '" + this.font.names.fullName.en + "' doesn't support feature '" + e.tag + "' for script '" + e.script + "'."
      );
    for (var a = this.getFeatureLookups(n), s = [].concat(t.context), i = 0; i < a.length; i++)
      for (var o = a[i], u = this.getLookupSubtables(o), l = 0; l < u.length; l++) {
        var c = u[l], h = this.getSubstitutionType(o, c), f = this.getLookupMethod(o, c), d = void 0;
        switch (h) {
          case "11":
            d = f(t.current), d && s.splice(r, 1, new vt({
              id: 11,
              tag: e.tag,
              substitution: d
            }));
            break;
          case "12":
            d = f(t.current), d && s.splice(r, 1, new vt({
              id: 12,
              tag: e.tag,
              substitution: d
            }));
            break;
          case "63":
            d = f(t), Array.isArray(d) && d.length && s.splice(r, 1, new vt({
              id: 63,
              tag: e.tag,
              substitution: d
            }));
            break;
          case "41":
            d = f(t), d && s.splice(r, 1, new vt({
              id: 41,
              tag: e.tag,
              substitution: d
            }));
            break;
          case "21":
            d = f(t.current), d && s.splice(r, 1, new vt({
              id: 21,
              tag: e.tag,
              substitution: d
            }));
            break;
        }
        t = new Le(s, r), !(Array.isArray(d) && !d.length) && (d = null);
      }
    return s.length ? s : null;
  };
  we.prototype.supports = function(e) {
    if (!e.script)
      return !1;
    this.getScriptFeatures(e.script);
    var t = this.features.hasOwnProperty(e.script);
    if (!e.tag)
      return t;
    var r = this.features[e.script].some(function(n) {
      return n.tag === e.tag;
    });
    return t && r;
  };
  we.prototype.getLookupSubtables = function(e) {
    return e.subtables || null;
  };
  we.prototype.getLookupByIndex = function(e) {
    var t = this.font.tables.gsub.lookups;
    return t[e] || null;
  };
  we.prototype.getFeatureLookups = function(e) {
    return e.lookupListIndexes.map(this.getLookupByIndex.bind(this));
  };
  we.prototype.getFeature = function(t) {
    if (!this.font)
      return { FAIL: "No font was found" };
    this.features.hasOwnProperty(t.script) || this.getScriptFeatures(t.script);
    var r = this.features[t.script];
    return r ? r.tags[t.tag] ? this.features[t.script].tags[t.tag] : null : { FAIL: "No feature for script " + t.script };
  };
  function cc(e) {
    var t = e.current, r = e.get(-1);
    return (
      // ? arabic first char
      r === null && st(t) || // ? arabic char preceded with a non arabic char
      !st(r) && st(t)
    );
  }
  function fc(e) {
    var t = e.get(1);
    return (
      // ? last arabic char
      t === null || // ? next char is not arabic
      !st(t)
    );
  }
  var hc = {
    startCheck: cc,
    endCheck: fc
  };
  function dc(e) {
    var t = e.current, r = e.get(-1);
    return (
      // ? an arabic char preceded with a non arabic char
      (st(t) || it(t)) && !st(r)
    );
  }
  function pc(e) {
    var t = e.get(1);
    switch (!0) {
      case t === null:
        return !0;
      case (!st(t) && !it(t)):
        var r = ac(t);
        if (!r)
          return !0;
        if (r) {
          var n = !1;
          if (n = e.lookahead.some(
            function(a) {
              return st(a) || it(a);
            }
          ), !n)
            return !0;
        }
        break;
      default:
        return !1;
    }
  }
  var vc = {
    startCheck: dc,
    endCheck: pc
  };
  function gc(e, t, r) {
    t[r].setState(e.tag, e.substitution);
  }
  function mc(e, t, r) {
    t[r].setState(e.tag, e.substitution);
  }
  function yc(e, t, r) {
    e.substitution.forEach(function(n, a) {
      var s = t[r + a];
      s.setState(e.tag, n);
    });
  }
  function xc(e, t, r) {
    var n = t[r];
    n.setState(e.tag, e.substitution.ligGlyph);
    for (var a = e.substitution.components.length, s = 0; s < a; s++)
      n = t[r + s + 1], n.setState("deleted", !0);
  }
  var ja = {
    11: gc,
    12: mc,
    63: yc,
    41: xc
  };
  function Qn(e, t, r) {
    e instanceof vt && ja[e.id] && ja[e.id](e, t, r);
  }
  function bc(e) {
    for (var t = [].concat(e.backtrack), r = t.length - 1; r >= 0; r--) {
      var n = t[r], a = li(n), s = it(n);
      if (!a && !s)
        return !0;
      if (a)
        return !1;
    }
    return !1;
  }
  function Sc(e) {
    if (li(e.current))
      return !1;
    for (var t = 0; t < e.lookahead.length; t++) {
      var r = e.lookahead[t], n = it(r);
      if (!n)
        return !0;
    }
    return !1;
  }
  function kc(e) {
    var t = this, r = "arab", n = this.featuresTags[r], a = this.tokenizer.getRangeTokens(e);
    if (a.length !== 1) {
      var s = new Le(
        a.map(
          function(o) {
            return o.getState("glyphIndex");
          }
        ),
        0
      ), i = new Le(
        a.map(
          function(o) {
            return o.char;
          }
        ),
        0
      );
      a.forEach(function(o, u) {
        if (!it(o.char)) {
          s.setCurrentIndex(u), i.setCurrentIndex(u);
          var l = 0;
          bc(i) && (l |= 1), Sc(i) && (l |= 2);
          var c;
          switch (l) {
            case 1:
              c = "fina";
              break;
            case 2:
              c = "init";
              break;
            case 3:
              c = "medi";
              break;
          }
          if (n.indexOf(c) !== -1) {
            var h = t.query.lookupFeature({
              tag: c,
              script: r,
              contextParams: s
            });
            if (h instanceof Error)
              return console.info(h.message);
            h.forEach(function(f, d) {
              f instanceof vt && (Qn(f, a, d), s.context[d] = f.substitution);
            });
          }
        }
      });
    }
  }
  function Za(e, t) {
    var r = e.map(function(n) {
      return n.activeState.value;
    });
    return new Le(r, 0);
  }
  function Tc(e) {
    var t = this, r = "arab", n = this.tokenizer.getRangeTokens(e), a = Za(n);
    a.context.forEach(function(s, i) {
      a.setCurrentIndex(i);
      var o = t.query.lookupFeature({
        tag: "rlig",
        script: r,
        contextParams: a
      });
      o.length && (o.forEach(
        function(u) {
          return Qn(u, n, i);
        }
      ), a = Za(n));
    });
  }
  function wc(e) {
    var t = e.current, r = e.get(-1);
    return (
      // ? latin first char
      r === null && Dr(t) || // ? latin char preceded with a non latin char
      !Dr(r) && Dr(t)
    );
  }
  function Cc(e) {
    var t = e.get(1);
    return (
      // ? last latin char
      t === null || // ? next char is not latin
      !Dr(t)
    );
  }
  var Ec = {
    startCheck: wc,
    endCheck: Cc
  };
  function Va(e, t) {
    var r = e.map(function(n) {
      return n.activeState.value;
    });
    return new Le(r, 0);
  }
  function Fc(e) {
    var t = this, r = "latn", n = this.tokenizer.getRangeTokens(e), a = Va(n);
    a.context.forEach(function(s, i) {
      a.setCurrentIndex(i);
      var o = t.query.lookupFeature({
        tag: "liga",
        script: r,
        contextParams: a
      });
      o.length && (o.forEach(
        function(u) {
          return Qn(u, n, i);
        }
      ), a = Va(n));
    });
  }
  function Pe(e) {
    this.baseDir = e || "ltr", this.tokenizer = new fe(), this.featuresTags = {};
  }
  Pe.prototype.setText = function(e) {
    this.text = e;
  };
  Pe.prototype.contextChecks = {
    latinWordCheck: Ec,
    arabicWordCheck: hc,
    arabicSentenceCheck: vc
  };
  function pn(e) {
    var t = this.contextChecks[e + "Check"];
    return this.tokenizer.registerContextChecker(
      e,
      t.startCheck,
      t.endCheck
    );
  }
  function Oc() {
    return pn.call(this, "latinWord"), pn.call(this, "arabicWord"), pn.call(this, "arabicSentence"), this.tokenizer.tokenize(this.text);
  }
  function Rc() {
    var e = this, t = this.tokenizer.getContextRanges("arabicSentence");
    t.forEach(function(r) {
      var n = e.tokenizer.getRangeTokens(r);
      e.tokenizer.replaceRange(
        r.startIndex,
        r.endOffset,
        n.reverse()
      );
    });
  }
  Pe.prototype.registerFeatures = function(e, t) {
    var r = this, n = t.filter(
      function(a) {
        return r.query.supports({ script: e, tag: a });
      }
    );
    this.featuresTags.hasOwnProperty(e) ? this.featuresTags[e] = this.featuresTags[e].concat(n) : this.featuresTags[e] = n;
  };
  Pe.prototype.applyFeatures = function(e, t) {
    if (!e)
      throw new Error(
        "No valid font was provided to apply features"
      );
    this.query || (this.query = new we(e));
    for (var r = 0; r < t.length; r++) {
      var n = t[r];
      this.query.supports({ script: n.script }) && this.registerFeatures(n.script, n.tags);
    }
  };
  Pe.prototype.registerModifier = function(e, t, r) {
    this.tokenizer.registerModifier(e, t, r);
  };
  function Jn() {
    if (this.tokenizer.registeredModifiers.indexOf("glyphIndex") === -1)
      throw new Error(
        "glyphIndex modifier is required to apply arabic presentation features."
      );
  }
  function _c() {
    var e = this, t = "arab";
    if (this.featuresTags.hasOwnProperty(t)) {
      Jn.call(this);
      var r = this.tokenizer.getContextRanges("arabicWord");
      r.forEach(function(n) {
        kc.call(e, n);
      });
    }
  }
  function Uc() {
    var e = this, t = "arab";
    if (this.featuresTags.hasOwnProperty(t)) {
      var r = this.featuresTags[t];
      if (r.indexOf("rlig") !== -1) {
        Jn.call(this);
        var n = this.tokenizer.getContextRanges("arabicWord");
        n.forEach(function(a) {
          Tc.call(e, a);
        });
      }
    }
  }
  function Lc() {
    var e = this, t = "latn";
    if (this.featuresTags.hasOwnProperty(t)) {
      var r = this.featuresTags[t];
      if (r.indexOf("liga") !== -1) {
        Jn.call(this);
        var n = this.tokenizer.getContextRanges("latinWord");
        n.forEach(function(a) {
          Fc.call(e, a);
        });
      }
    }
  }
  Pe.prototype.checkContextReady = function(e) {
    return !!this.tokenizer.getContext(e);
  };
  Pe.prototype.applyFeaturesToContexts = function() {
    this.checkContextReady("arabicWord") && (_c.call(this), Uc.call(this)), this.checkContextReady("latinWord") && Lc.call(this), this.checkContextReady("arabicSentence") && Rc.call(this);
  };
  Pe.prototype.processText = function(e) {
    (!this.text || this.text !== e) && (this.setText(e), Oc.call(this), this.applyFeaturesToContexts());
  };
  Pe.prototype.getBidiText = function(e) {
    return this.processText(e), this.tokenizer.getText();
  };
  Pe.prototype.getTextGlyphs = function(e) {
    this.processText(e);
    for (var t = [], r = 0; r < this.tokenizer.tokens.length; r++) {
      var n = this.tokenizer.tokens[r];
      if (!n.state.deleted) {
        var a = n.activeState.value;
        t.push(Array.isArray(a) ? a[0] : a);
      }
    }
    return t;
  };
  function re(e) {
    e = e || {}, e.tables = e.tables || {}, e.empty || ($t(e.familyName, "When creating a new Font object, familyName is required."), $t(e.styleName, "When creating a new Font object, styleName is required."), $t(e.unitsPerEm, "When creating a new Font object, unitsPerEm is required."), $t(e.ascender, "When creating a new Font object, ascender is required."), $t(e.descender <= 0, "When creating a new Font object, negative descender value is required."), this.names = {
      fontFamily: { en: e.familyName || " " },
      fontSubfamily: { en: e.styleName || " " },
      fullName: { en: e.fullName || e.familyName + " " + e.styleName },
      // postScriptName may not contain any whitespace
      postScriptName: { en: e.postScriptName || (e.familyName + e.styleName).replace(/\s/g, "") },
      designer: { en: e.designer || " " },
      designerURL: { en: e.designerURL || " " },
      manufacturer: { en: e.manufacturer || " " },
      manufacturerURL: { en: e.manufacturerURL || " " },
      license: { en: e.license || " " },
      licenseURL: { en: e.licenseURL || " " },
      version: { en: e.version || "Version 0.1" },
      description: { en: e.description || " " },
      copyright: { en: e.copyright || " " },
      trademark: { en: e.trademark || " " }
    }, this.unitsPerEm = e.unitsPerEm || 1e3, this.ascender = e.ascender, this.descender = e.descender, this.createdTimestamp = e.createdTimestamp, this.tables = Object.assign(e.tables, {
      os2: Object.assign({
        usWeightClass: e.weightClass || this.usWeightClasses.MEDIUM,
        usWidthClass: e.widthClass || this.usWidthClasses.MEDIUM,
        fsSelection: e.fsSelection || this.fsSelectionValues.REGULAR
      }, e.tables.os2)
    })), this.supported = !0, this.glyphs = new We.GlyphSet(this, e.glyphs || []), this.encoding = new Os(this), this.position = new br(this), this.substitution = new Te(this), this.tables = this.tables || {}, this._push = null, this._hmtxTableData = {}, Object.defineProperty(this, "hinting", {
      get: function() {
        if (this._hinting)
          return this._hinting;
        if (this.outlinesFormat === "truetype")
          return this._hinting = new si(this);
      }
    });
  }
  re.prototype.hasChar = function(e) {
    return this.encoding.charToGlyphIndex(e) !== null;
  };
  re.prototype.charToGlyphIndex = function(e) {
    return this.encoding.charToGlyphIndex(e);
  };
  re.prototype.charToGlyph = function(e) {
    var t = this.charToGlyphIndex(e), r = this.glyphs.get(t);
    return r || (r = this.glyphs.get(0)), r;
  };
  re.prototype.updateFeatures = function(e) {
    return this.defaultRenderOptions.features.map(function(t) {
      return t.script === "latn" ? {
        script: "latn",
        tags: t.tags.filter(function(r) {
          return e[r];
        })
      } : t;
    });
  };
  re.prototype.stringToGlyphs = function(e, t) {
    var r = this, n = new Pe(), a = function(h) {
      return r.charToGlyphIndex(h.char);
    };
    n.registerModifier("glyphIndex", null, a);
    var s = t ? this.updateFeatures(t.features) : this.defaultRenderOptions.features;
    n.applyFeatures(this, s);
    for (var i = n.getTextGlyphs(e), o = i.length, u = new Array(o), l = this.glyphs.get(0), c = 0; c < o; c += 1)
      u[c] = this.glyphs.get(i[c]) || l;
    return u;
  };
  re.prototype.nameToGlyphIndex = function(e) {
    return this.glyphNames.nameToGlyphIndex(e);
  };
  re.prototype.nameToGlyph = function(e) {
    var t = this.nameToGlyphIndex(e), r = this.glyphs.get(t);
    return r || (r = this.glyphs.get(0)), r;
  };
  re.prototype.glyphIndexToName = function(e) {
    return this.glyphNames.glyphIndexToName ? this.glyphNames.glyphIndexToName(e) : "";
  };
  re.prototype.getKerningValue = function(e, t) {
    e = e.index || e, t = t.index || t;
    var r = this.position.defaultKerningTables;
    return r ? this.position.getKerningValue(r, e, t) : this.kerningPairs[e + "," + t] || 0;
  };
  re.prototype.defaultRenderOptions = {
    kerning: !0,
    features: [
      /**
       * these 4 features are required to render Arabic text properly
       * and shouldn't be turned off when rendering arabic text.
       */
      { script: "arab", tags: ["init", "medi", "fina", "rlig"] },
      { script: "latn", tags: ["liga", "rlig"] }
    ]
  };
  re.prototype.forEachGlyph = function(e, t, r, n, a, s) {
    t = t !== void 0 ? t : 0, r = r !== void 0 ? r : 0, n = n !== void 0 ? n : 72, a = Object.assign({}, this.defaultRenderOptions, a);
    var i = 1 / this.unitsPerEm * n, o = this.stringToGlyphs(e, a), u;
    if (a.kerning) {
      var l = a.script || this.position.getDefaultScriptName();
      u = this.position.getKerningTables(l, a.language);
    }
    for (var c = 0; c < o.length; c += 1) {
      var h = o[c];
      if (s.call(this, h, t, r, n, a), h.advanceWidth && (t += h.advanceWidth * i), a.kerning && c < o.length - 1) {
        var f = u ? this.position.getKerningValue(u, h.index, o[c + 1].index) : this.getKerningValue(h, o[c + 1]);
        t += f * i;
      }
      a.letterSpacing ? t += a.letterSpacing * n : a.tracking && (t += a.tracking / 1e3 * n);
    }
    return t;
  };
  re.prototype.getPath = function(e, t, r, n, a) {
    var s = new ge();
    return this.forEachGlyph(e, t, r, n, a, function(i, o, u, l) {
      var c = i.getPath(o, u, l, a, this);
      s.extend(c);
    }), s;
  };
  re.prototype.getPaths = function(e, t, r, n, a) {
    var s = [];
    return this.forEachGlyph(e, t, r, n, a, function(i, o, u, l) {
      var c = i.getPath(o, u, l, a, this);
      s.push(c);
    }), s;
  };
  re.prototype.getAdvanceWidth = function(e, t, r) {
    return this.forEachGlyph(e, 0, 0, t, r, function() {
    });
  };
  re.prototype.draw = function(e, t, r, n, a, s) {
    this.getPath(t, r, n, a, s).draw(e);
  };
  re.prototype.drawPoints = function(e, t, r, n, a, s) {
    this.forEachGlyph(t, r, n, a, s, function(i, o, u, l) {
      i.drawPoints(e, o, u, l);
    });
  };
  re.prototype.drawMetrics = function(e, t, r, n, a, s) {
    this.forEachGlyph(t, r, n, a, s, function(i, o, u, l) {
      i.drawMetrics(e, o, u, l);
    });
  };
  re.prototype.getEnglishName = function(e) {
    var t = this.names[e];
    if (t)
      return t.en;
  };
  re.prototype.validate = function() {
    var e = this;
    function t(n, a) {
    }
    function r(n) {
      var a = e.getEnglishName(n);
      a && a.trim().length > 0;
    }
    r("fontFamily"), r("weightName"), r("manufacturer"), r("copyright"), r("version"), this.unitsPerEm > 0;
  };
  re.prototype.toTables = function() {
    return yu.fontToTable(this);
  };
  re.prototype.toBuffer = function() {
    return console.warn("Font.toBuffer is deprecated. Use Font.toArrayBuffer instead."), this.toArrayBuffer();
  };
  re.prototype.toArrayBuffer = function() {
    for (var e = this.toTables(), t = e.encode(), r = new ArrayBuffer(t.length), n = new Uint8Array(r), a = 0; a < t.length; a++)
      n[a] = t[a];
    return r;
  };
  re.prototype.download = function(e) {
    var t = this.getEnglishName("fontFamily"), r = this.getEnglishName("fontSubfamily");
    e = e || t.replace(/\s/g, "") + "-" + r + ".otf";
    var n = this.toArrayBuffer();
    if (bu())
      if (window.URL = window.URL || window.webkitURL, window.URL) {
        var a = new DataView(n), s = new Blob([a], { type: "font/opentype" }), i = document.createElement("a");
        i.href = window.URL.createObjectURL(s), i.download = e;
        var o = document.createEvent("MouseEvents");
        o.initEvent("click", !0, !1), i.dispatchEvent(o);
      } else
        console.warn("Font file could not be downloaded. Try using a different browser.");
    else {
      var u = require("fs"), l = ku(n);
      u.writeFileSync(e, l);
    }
  };
  re.prototype.fsSelectionValues = {
    ITALIC: 1,
    //1
    UNDERSCORE: 2,
    //2
    NEGATIVE: 4,
    //4
    OUTLINED: 8,
    //8
    STRIKEOUT: 16,
    //16
    BOLD: 32,
    //32
    REGULAR: 64,
    //64
    USER_TYPO_METRICS: 128,
    //128
    WWS: 256,
    //256
    OBLIQUE: 512
    //512
  };
  re.prototype.usWidthClasses = {
    ULTRA_CONDENSED: 1,
    EXTRA_CONDENSED: 2,
    CONDENSED: 3,
    SEMI_CONDENSED: 4,
    MEDIUM: 5,
    SEMI_EXPANDED: 6,
    EXPANDED: 7,
    EXTRA_EXPANDED: 8,
    ULTRA_EXPANDED: 9
  };
  re.prototype.usWeightClasses = {
    THIN: 100,
    EXTRA_LIGHT: 200,
    LIGHT: 300,
    NORMAL: 400,
    MEDIUM: 500,
    SEMI_BOLD: 600,
    BOLD: 700,
    EXTRA_BOLD: 800,
    BLACK: 900
  };
  function ci(e, t) {
    var r = JSON.stringify(e), n = 256;
    for (var a in t) {
      var s = parseInt(a);
      if (!(!s || s < 256)) {
        if (JSON.stringify(t[a]) === r)
          return s;
        n <= s && (n = s + 1);
      }
    }
    return t[n] = e, n;
  }
  function Ac(e, t, r) {
    var n = ci(t.name, r);
    return [
      { name: "tag_" + e, type: "TAG", value: t.tag },
      { name: "minValue_" + e, type: "FIXED", value: t.minValue << 16 },
      { name: "defaultValue_" + e, type: "FIXED", value: t.defaultValue << 16 },
      { name: "maxValue_" + e, type: "FIXED", value: t.maxValue << 16 },
      { name: "flags_" + e, type: "USHORT", value: 0 },
      { name: "nameID_" + e, type: "USHORT", value: n }
    ];
  }
  function Ic(e, t, r) {
    var n = {}, a = new A.Parser(e, t);
    return n.tag = a.parseTag(), n.minValue = a.parseFixed(), n.defaultValue = a.parseFixed(), n.maxValue = a.parseFixed(), a.skip("uShort", 1), n.name = r[a.parseUShort()] || {}, n;
  }
  function Dc(e, t, r, n) {
    for (var a = ci(t.name, n), s = [
      { name: "nameID_" + e, type: "USHORT", value: a },
      { name: "flags_" + e, type: "USHORT", value: 0 }
    ], i = 0; i < r.length; ++i) {
      var o = r[i].tag;
      s.push({
        name: "axis_" + e + " " + o,
        type: "FIXED",
        value: t.coordinates[o] << 16
      });
    }
    return s;
  }
  function Pc(e, t, r, n) {
    var a = {}, s = new A.Parser(e, t);
    a.name = n[s.parseUShort()] || {}, s.skip("uShort", 1), a.coordinates = {};
    for (var i = 0; i < r.length; ++i)
      a.coordinates[r[i].tag] = s.parseFixed();
    return a;
  }
  function Mc(e, t) {
    var r = new O.Table("fvar", [
      { name: "version", type: "ULONG", value: 65536 },
      { name: "offsetToData", type: "USHORT", value: 0 },
      { name: "countSizePairs", type: "USHORT", value: 2 },
      { name: "axisCount", type: "USHORT", value: e.axes.length },
      { name: "axisSize", type: "USHORT", value: 20 },
      { name: "instanceCount", type: "USHORT", value: e.instances.length },
      { name: "instanceSize", type: "USHORT", value: 4 + e.axes.length * 4 }
    ]);
    r.offsetToData = r.sizeOf();
    for (var n = 0; n < e.axes.length; n++)
      r.fields = r.fields.concat(Ac(n, e.axes[n], t));
    for (var a = 0; a < e.instances.length; a++)
      r.fields = r.fields.concat(Dc(a, e.instances[a], e.axes, t));
    return r;
  }
  function Nc(e, t, r) {
    var n = new A.Parser(e, t), a = n.parseULong();
    G.argument(a === 65536, "Unsupported fvar table version.");
    var s = n.parseOffset16();
    n.skip("uShort", 1);
    for (var i = n.parseUShort(), o = n.parseUShort(), u = n.parseUShort(), l = n.parseUShort(), c = [], h = 0; h < i; h++)
      c.push(Ic(e, t + s + h * o, r));
    for (var f = [], d = t + s + i * o, v = 0; v < u; v++)
      f.push(Pc(e, d + v * l, c, r));
    return { axes: c, instances: f };
  }
  var Bc = { make: Mc, parse: Nc }, Gc = function() {
    return {
      coverage: this.parsePointer(p.coverage),
      attachPoints: this.parseList(p.pointer(p.uShortList))
    };
  }, Hc = function() {
    var e = this.parseUShort();
    if (G.argument(
      e === 1 || e === 2 || e === 3,
      "Unsupported CaretValue table version."
    ), e === 1)
      return { coordinate: this.parseShort() };
    if (e === 2)
      return { pointindex: this.parseShort() };
    if (e === 3)
      return { coordinate: this.parseShort() };
  }, Wc = function() {
    return this.parseList(p.pointer(Hc));
  }, zc = function() {
    return {
      coverage: this.parsePointer(p.coverage),
      ligGlyphs: this.parseList(p.pointer(Wc))
    };
  }, jc = function() {
    return this.parseUShort(), this.parseList(p.pointer(p.coverage));
  };
  function Zc(e, t) {
    t = t || 0;
    var r = new p(e, t), n = r.parseVersion(1);
    G.argument(
      n === 1 || n === 1.2 || n === 1.3,
      "Unsupported GDEF table version."
    );
    var a = {
      version: n,
      classDef: r.parsePointer(p.classDef),
      attachList: r.parsePointer(Gc),
      ligCaretList: r.parsePointer(zc),
      markAttachClassDef: r.parsePointer(p.classDef)
    };
    return n >= 1.2 && (a.markGlyphSets = r.parsePointer(jc)), a;
  }
  var Vc = { parse: Zc }, Ae = new Array(10);
  Ae[1] = function() {
    var t = this.offset + this.relativeOffset, r = this.parseUShort();
    if (r === 1)
      return {
        posFormat: 1,
        coverage: this.parsePointer(p.coverage),
        value: this.parseValueRecord()
      };
    if (r === 2)
      return {
        posFormat: 2,
        coverage: this.parsePointer(p.coverage),
        values: this.parseValueRecordList()
      };
    G.assert(!1, "0x" + t.toString(16) + ": GPOS lookup type 1 format must be 1 or 2.");
  };
  Ae[2] = function() {
    var t = this.offset + this.relativeOffset, r = this.parseUShort();
    G.assert(r === 1 || r === 2, "0x" + t.toString(16) + ": GPOS lookup type 2 format must be 1 or 2.");
    var n = this.parsePointer(p.coverage), a = this.parseUShort(), s = this.parseUShort();
    if (r === 1)
      return {
        posFormat: r,
        coverage: n,
        valueFormat1: a,
        valueFormat2: s,
        pairSets: this.parseList(p.pointer(p.list(function() {
          return {
            // pairValueRecord
            secondGlyph: this.parseUShort(),
            value1: this.parseValueRecord(a),
            value2: this.parseValueRecord(s)
          };
        })))
      };
    if (r === 2) {
      var i = this.parsePointer(p.classDef), o = this.parsePointer(p.classDef), u = this.parseUShort(), l = this.parseUShort();
      return {
        // Class Pair Adjustment
        posFormat: r,
        coverage: n,
        valueFormat1: a,
        valueFormat2: s,
        classDef1: i,
        classDef2: o,
        class1Count: u,
        class2Count: l,
        classRecords: this.parseList(u, p.list(l, function() {
          return {
            value1: this.parseValueRecord(a),
            value2: this.parseValueRecord(s)
          };
        }))
      };
    }
  };
  Ae[3] = function() {
    return { error: "GPOS Lookup 3 not supported" };
  };
  Ae[4] = function() {
    return { error: "GPOS Lookup 4 not supported" };
  };
  Ae[5] = function() {
    return { error: "GPOS Lookup 5 not supported" };
  };
  Ae[6] = function() {
    return { error: "GPOS Lookup 6 not supported" };
  };
  Ae[7] = function() {
    return { error: "GPOS Lookup 7 not supported" };
  };
  Ae[8] = function() {
    return { error: "GPOS Lookup 8 not supported" };
  };
  Ae[9] = function() {
    return { error: "GPOS Lookup 9 not supported" };
  };
  function qc(e, t) {
    t = t || 0;
    var r = new p(e, t), n = r.parseVersion(1);
    return G.argument(n === 1 || n === 1.1, "Unsupported GPOS table version " + n), n === 1 ? {
      version: n,
      scripts: r.parseScriptList(),
      features: r.parseFeatureList(),
      lookups: r.parseLookupList(Ae)
    } : {
      version: n,
      scripts: r.parseScriptList(),
      features: r.parseFeatureList(),
      lookups: r.parseLookupList(Ae),
      variations: r.parseFeatureVariationsList()
    };
  }
  var $c = new Array(10);
  function Xc(e) {
    return new O.Table("GPOS", [
      { name: "version", type: "ULONG", value: 65536 },
      { name: "scripts", type: "TABLE", value: new O.ScriptList(e.scripts) },
      { name: "features", type: "TABLE", value: new O.FeatureList(e.features) },
      { name: "lookups", type: "TABLE", value: new O.LookupList(e.lookups, $c) }
    ]);
  }
  var Yc = { parse: qc, make: Xc };
  function Qc(e) {
    var t = {};
    e.skip("uShort");
    var r = e.parseUShort();
    G.argument(r === 0, "Unsupported kern sub-table version."), e.skip("uShort", 2);
    var n = e.parseUShort();
    e.skip("uShort", 3);
    for (var a = 0; a < n; a += 1) {
      var s = e.parseUShort(), i = e.parseUShort(), o = e.parseShort();
      t[s + "," + i] = o;
    }
    return t;
  }
  function Jc(e) {
    var t = {};
    e.skip("uShort");
    var r = e.parseULong();
    r > 1 && console.warn("Only the first kern subtable is supported."), e.skip("uLong");
    var n = e.parseUShort(), a = n & 255;
    if (e.skip("uShort"), a === 0) {
      var s = e.parseUShort();
      e.skip("uShort", 3);
      for (var i = 0; i < s; i += 1) {
        var o = e.parseUShort(), u = e.parseUShort(), l = e.parseShort();
        t[o + "," + u] = l;
      }
    }
    return t;
  }
  function Kc(e, t) {
    var r = new A.Parser(e, t), n = r.parseUShort();
    if (n === 0)
      return Qc(r);
    if (n === 1)
      return Jc(r);
    throw new Error("Unsupported kern table version (" + n + ").");
  }
  var ef = { parse: Kc };
  function tf(e, t, r, n) {
    for (var a = new A.Parser(e, t), s = n ? a.parseUShort : a.parseULong, i = [], o = 0; o < r + 1; o += 1) {
      var u = s.call(a);
      n && (u *= 2), i.push(u);
    }
    return i;
  }
  var rf = { parse: tf };
  function nf(e, t) {
    var r = require("fs");
    r.readFile(e, function(n, a) {
      if (n)
        return t(n.message);
      t(null, Su(a));
    });
  }
  function af(e, t) {
    var r = new XMLHttpRequest();
    r.open("get", e, !0), r.responseType = "arraybuffer", r.onload = function() {
      return r.response ? t(null, r.response) : t("Font could not be loaded: " + r.statusText);
    }, r.onerror = function() {
      t("Font could not be loaded");
    }, r.send();
  }
  function qa(e, t) {
    for (var r = [], n = 12, a = 0; a < t; a += 1) {
      var s = A.getTag(e, n), i = A.getULong(e, n + 4), o = A.getULong(e, n + 8), u = A.getULong(e, n + 12);
      r.push({ tag: s, checksum: i, offset: o, length: u, compression: !1 }), n += 16;
    }
    return r;
  }
  function sf(e, t) {
    for (var r = [], n = 44, a = 0; a < t; a += 1) {
      var s = A.getTag(e, n), i = A.getULong(e, n + 4), o = A.getULong(e, n + 8), u = A.getULong(e, n + 12), l = void 0;
      o < u ? l = "WOFF" : l = !1, r.push({
        tag: s,
        offset: i,
        compression: l,
        compressedLength: o,
        length: u
      }), n += 20;
    }
    return r;
  }
  function de(e, t) {
    if (t.compression === "WOFF") {
      var r = new Uint8Array(e.buffer, t.offset + 2, t.compressedLength - 2), n = new Uint8Array(t.length);
      if ($i(r, n), n.byteLength !== t.length)
        throw new Error("Decompression error: " + t.tag + " decompressed length doesn't match recorded length");
      var a = new DataView(n.buffer, 0);
      return { data: a, offset: 0 };
    } else
      return { data: e, offset: t.offset };
  }
  function of(e, t) {
    t = t ?? {};
    var r, n, a = new re({ empty: !0 }), s = new DataView(e, 0), i, o = [], u = A.getTag(s, 0);
    if (u === "\0\0\0" || u === "true" || u === "typ1")
      a.outlinesFormat = "truetype", i = A.getUShort(s, 4), o = qa(s, i);
    else if (u === "OTTO")
      a.outlinesFormat = "cff", i = A.getUShort(s, 4), o = qa(s, i);
    else if (u === "wOFF") {
      var l = A.getTag(s, 4);
      if (l === "\0\0\0")
        a.outlinesFormat = "truetype";
      else if (l === "OTTO")
        a.outlinesFormat = "cff";
      else
        throw new Error("Unsupported OpenType flavor " + u);
      i = A.getUShort(s, 12), o = sf(s, i);
    } else
      throw new Error("Unsupported OpenType signature " + u);
    for (var c, h, f, d, v, S, m, x, E, b, M, I, W = 0; W < i; W += 1) {
      var U = o[W], w = void 0;
      switch (U.tag) {
        case "cmap":
          w = de(s, U), a.tables.cmap = Fs.parse(w.data, w.offset), a.encoding = new Rs(a.tables.cmap);
          break;
        case "cvt ":
          w = de(s, U), I = new A.Parser(w.data, w.offset), a.tables.cvt = I.parseShortList(U.length / 2);
          break;
        case "fvar":
          h = U;
          break;
        case "fpgm":
          w = de(s, U), I = new A.Parser(w.data, w.offset), a.tables.fpgm = I.parseByteList(U.length);
          break;
        case "head":
          w = de(s, U), a.tables.head = Bs.parse(w.data, w.offset), a.unitsPerEm = a.tables.head.unitsPerEm, r = a.tables.head.indexToLocFormat;
          break;
        case "hhea":
          w = de(s, U), a.tables.hhea = Gs.parse(w.data, w.offset), a.ascender = a.tables.hhea.ascender, a.descender = a.tables.hhea.descender, a.numberOfHMetrics = a.tables.hhea.numberOfHMetrics;
          break;
        case "hmtx":
          m = U;
          break;
        case "ltag":
          w = de(s, U), n = Ws.parse(w.data, w.offset);
          break;
        case "maxp":
          w = de(s, U), a.tables.maxp = zs.parse(w.data, w.offset), a.numGlyphs = a.tables.maxp.numGlyphs;
          break;
        case "name":
          b = U;
          break;
        case "OS/2":
          w = de(s, U), a.tables.os2 = Tn.parse(w.data, w.offset);
          break;
        case "post":
          w = de(s, U), a.tables.post = Xs.parse(w.data, w.offset), a.glyphNames = new Vn(a.tables.post);
          break;
        case "prep":
          w = de(s, U), I = new A.Parser(w.data, w.offset), a.tables.prep = I.parseByteList(U.length);
          break;
        case "glyf":
          f = U;
          break;
        case "loca":
          E = U;
          break;
        case "CFF ":
          c = U;
          break;
        case "kern":
          x = U;
          break;
        case "GDEF":
          d = U;
          break;
        case "GPOS":
          v = U;
          break;
        case "GSUB":
          S = U;
          break;
        case "meta":
          M = U;
          break;
      }
    }
    var Q = de(s, b);
    if (a.tables.name = $s.parse(Q.data, Q.offset, n), a.names = a.tables.name, f && E) {
      var q = r === 0, ae = de(s, E), ne = rf.parse(ae.data, ae.offset, a.numGlyphs, q), K = de(s, f);
      a.glyphs = ri.parse(K.data, K.offset, ne, a, t);
    } else if (c) {
      var te = de(s, c);
      Ns.parse(te.data, te.offset, a, t);
    } else
      throw new Error("Font doesn't contain TrueType or CFF outlines.");
    var ee = de(s, m);
    if (Hs.parse(a, ee.data, ee.offset, a.numberOfHMetrics, a.numGlyphs, a.glyphs, t), go(a, t), x) {
      var ie = de(s, x);
      a.kerningPairs = ef.parse(ie.data, ie.offset);
    } else
      a.kerningPairs = {};
    if (d) {
      var le = de(s, d);
      a.tables.gdef = Vc.parse(le.data, le.offset);
    }
    if (v) {
      var ce = de(s, v);
      a.tables.gpos = Yc.parse(ce.data, ce.offset), a.position.init();
    }
    if (S) {
      var se = de(s, S);
      a.tables.gsub = Ys.parse(se.data, se.offset);
    }
    if (h) {
      var y = de(s, h);
      a.tables.fvar = Bc.parse(y.data, y.offset, a.names);
    }
    if (M) {
      var L = de(s, M);
      a.tables.meta = Qs.parse(L.data, L.offset), a.metas = a.tables.meta;
    }
    return a;
  }
  function uf(e, t, r) {
    r = r ?? {};
    var n = typeof window > "u", a = n && !r.isUrl ? nf : af;
    return new Promise(function(s, i) {
      a(e, function(o, u) {
        if (o) {
          if (t)
            return t(o);
          i(o);
        }
        var l;
        try {
          l = of(u, r);
        } catch (c) {
          if (t)
            return t(c, null);
          i(c);
        }
        if (t)
          return t(null, l);
        s(l);
      });
    });
  }
  function lf(e) {
    const t = e.match(/left|center|right/gi) || [], r = t.length === 0 ? "left" : t[0], n = e.match(/baseline|top|bottom|middle/gi) || [], a = n.length === 0 ? "baseline" : n[0];
    return { horizontal: r, vertical: a };
  }
  function cf(e, t) {
    return e.charToGlyph(t).name !== ".notdef";
  }
  class ff {
    constructor(t, r) {
      this.font = t, this.fallbackFont = r;
    }
    toVector(t, r = {}) {
      const n = Object.keys(r.attributes || {}).map((u) => `${u}="${r.attributes[u]}"`).join(" "), { d: a, height: s, cursorX: i } = this.getD(t, r);
      return {
        path: n ? `<path ${n} d="${a}"/>` : `<path d="${a}"/>`,
        width: i,
        height: s
      };
    }
    _getHeight(t) {
      const r = 1 / this.font.unitsPerEm * t;
      return (this.font.ascender - this.font.descender) * r;
    }
    _getWidth(t, r) {
      return this.font.getAdvanceWidth(t, r);
    }
    getD(t, r = {}) {
      const n = r.fontSize || 72, a = "kerning" in r ? r.kerning : !0, s = r.letterSpacing || 0, i = r.tracking || 0;
      let o = r.x || 0;
      const u = r.y || 0, l = lf(r.anchor || ""), c = this._getHeight(n), h = this.font.ascender * (n / this.font.unitsPerEm);
      let f = u;
      switch (l.vertical) {
        case "baseline":
          f = u;
          break;
        case "top":
          f = u + h;
          break;
        case "middle":
          f = u + h - c / 2;
          break;
        case "bottom":
          f = u + h - c;
          break;
      }
      const d = [];
      let v = null, S = null;
      for (let m = 0; m < t.length; m++) {
        const x = t[m], E = cf(this.font, x) ? this.font : this.fallbackFont, b = 1 / E.unitsPerEm * n, M = E.charToGlyph(x);
        if (a && v && S === E) {
          const W = E.getKerningValue(v, M);
          o += W * b;
        }
        const I = E.getPath(x, o, f, n, {
          kerning: !1,
          tracking: i
        });
        if (d.push(I), M.advanceWidth) {
          const W = M.advanceWidth * b;
          o += W;
        }
        s ? o += s * n : i && (o += i / 1e3 * n), v = M, S = E;
      }
      return {
        d: d.map((m) => m.toPathData(4)).join(" "),
        height: c,
        cursorX: o
      };
    }
  }
  var Kn = class {
    constructor() {
      this.listeners = /* @__PURE__ */ new Set(), this.subscribe = this.subscribe.bind(this);
    }
    subscribe(e) {
      return this.listeners.add(e), this.onSubscribe(), () => {
        this.listeners.delete(e), this.onUnsubscribe();
      };
    }
    hasListeners() {
      return this.listeners.size > 0;
    }
    onSubscribe() {
    }
    onUnsubscribe() {
    }
  }, rr = typeof window > "u" || "Deno" in globalThis;
  function Cn() {
  }
  function $a(e) {
    return typeof e == "number" && e >= 0 && e !== 1 / 0;
  }
  function hf(e, t) {
    return Math.max(e + (t || 0) - Date.now(), 0);
  }
  function Qt(e, t) {
    return typeof e == "function" ? e(t) : e;
  }
  function Ge(e, t) {
    return typeof e == "function" ? e(t) : e;
  }
  function En(e, t) {
    if (e === t)
      return e;
    const r = Xa(e) && Xa(t);
    if (r || Ya(e) && Ya(t)) {
      const n = r ? e : Object.keys(e), a = n.length, s = r ? t : Object.keys(t), i = s.length, o = r ? [] : {}, u = new Set(n);
      let l = 0;
      for (let c = 0; c < i; c++) {
        const h = r ? c : s[c];
        (!r && u.has(h) || r) && e[h] === void 0 && t[h] === void 0 ? (o[h] = void 0, l++) : (o[h] = En(e[h], t[h]), o[h] === e[h] && e[h] !== void 0 && l++);
      }
      return a === i && l === a ? e : o;
    }
    return t;
  }
  function Fn(e, t) {
    if (!t || Object.keys(e).length !== Object.keys(t).length)
      return !1;
    for (const r in e)
      if (e[r] !== t[r])
        return !1;
    return !0;
  }
  function Xa(e) {
    return Array.isArray(e) && e.length === Object.keys(e).length;
  }
  function Ya(e) {
    if (!Qa(e))
      return !1;
    const t = e.constructor;
    if (t === void 0)
      return !0;
    const r = t.prototype;
    return !(!Qa(r) || !r.hasOwnProperty("isPrototypeOf") || Object.getPrototypeOf(e) !== Object.prototype);
  }
  function Qa(e) {
    return Object.prototype.toString.call(e) === "[object Object]";
  }
  function Ja(e, t, r) {
    if (typeof r.structuralSharing == "function")
      return r.structuralSharing(e, t);
    if (r.structuralSharing !== !1) {
      if (process.env.NODE_ENV !== "production")
        try {
          return En(e, t);
        } catch (n) {
          throw console.error(
            `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${r.queryHash}]: ${n}`
          ), n;
        }
      return En(e, t);
    }
    return t;
  }
  function df(e, t) {
    return typeof e == "function" ? e(...t) : !!e;
  }
  var yt, Ke, At, cs, pf = (cs = class extends Kn {
    constructor() {
      super();
      oe(this, yt);
      oe(this, Ke);
      oe(this, At);
      X(this, At, (t) => {
        if (!rr && window.addEventListener) {
          const r = () => t();
          return window.addEventListener("visibilitychange", r, !1), () => {
            window.removeEventListener("visibilitychange", r);
          };
        }
      });
    }
    onSubscribe() {
      k(this, Ke) || this.setEventListener(k(this, At));
    }
    onUnsubscribe() {
      var t;
      this.hasListeners() || ((t = k(this, Ke)) == null || t.call(this), X(this, Ke, void 0));
    }
    setEventListener(t) {
      var r;
      X(this, At, t), (r = k(this, Ke)) == null || r.call(this), X(this, Ke, t((n) => {
        typeof n == "boolean" ? this.setFocused(n) : this.onFocus();
      }));
    }
    setFocused(t) {
      k(this, yt) !== t && (X(this, yt, t), this.onFocus());
    }
    onFocus() {
      const t = this.isFocused();
      this.listeners.forEach((r) => {
        r(t);
      });
    }
    isFocused() {
      var t;
      return typeof k(this, yt) == "boolean" ? k(this, yt) : ((t = globalThis.document) == null ? void 0 : t.visibilityState) !== "hidden";
    }
  }, yt = new WeakMap(), Ke = new WeakMap(), At = new WeakMap(), cs), vf = new pf(), It, et, Dt, fs, gf = (fs = class extends Kn {
    constructor() {
      super();
      oe(this, It, !0);
      oe(this, et);
      oe(this, Dt);
      X(this, Dt, (t) => {
        if (!rr && window.addEventListener) {
          const r = () => t(!0), n = () => t(!1);
          return window.addEventListener("online", r, !1), window.addEventListener("offline", n, !1), () => {
            window.removeEventListener("online", r), window.removeEventListener("offline", n);
          };
        }
      });
    }
    onSubscribe() {
      k(this, et) || this.setEventListener(k(this, Dt));
    }
    onUnsubscribe() {
      var t;
      this.hasListeners() || ((t = k(this, et)) == null || t.call(this), X(this, et, void 0));
    }
    setEventListener(t) {
      var r;
      X(this, Dt, t), (r = k(this, et)) == null || r.call(this), X(this, et, t(this.setOnline.bind(this)));
    }
    setOnline(t) {
      k(this, It) !== t && (X(this, It, t), this.listeners.forEach((n) => {
        n(t);
      }));
    }
    isOnline() {
      return k(this, It);
    }
  }, It = new WeakMap(), et = new WeakMap(), Dt = new WeakMap(), fs), mf = new gf();
  function Ka() {
    let e, t;
    const r = new Promise((a, s) => {
      e = a, t = s;
    });
    r.status = "pending", r.catch(() => {
    });
    function n(a) {
      Object.assign(r, a), delete r.resolve, delete r.reject;
    }
    return r.resolve = (a) => {
      n({
        status: "fulfilled",
        value: a
      }), e(a);
    }, r.reject = (a) => {
      n({
        status: "rejected",
        reason: a
      }), t(a);
    }, r;
  }
  function yf(e) {
    return (e ?? "online") === "online" ? mf.isOnline() : !0;
  }
  var xf = (e) => setTimeout(e, 0);
  function bf() {
    let e = [], t = 0, r = (o) => {
      o();
    }, n = (o) => {
      o();
    }, a = xf;
    const s = (o) => {
      t ? e.push(o) : a(() => {
        r(o);
      });
    }, i = () => {
      const o = e;
      e = [], o.length && a(() => {
        n(() => {
          o.forEach((u) => {
            r(u);
          });
        });
      });
    };
    return {
      batch: (o) => {
        let u;
        t++;
        try {
          u = o();
        } finally {
          t--, t || i();
        }
        return u;
      },
      /**
       * All calls to the wrapped function will be batched.
       */
      batchCalls: (o) => (...u) => {
        s(() => {
          o(...u);
        });
      },
      schedule: s,
      /**
       * Use this method to set a custom notify function.
       * This can be used to for example wrap notifications with `React.act` while running tests.
       */
      setNotifyFunction: (o) => {
        r = o;
      },
      /**
       * Use this method to set a custom function to batch notifications together into a single tick.
       * By default React Query will use the batch function provided by ReactDOM or React Native.
       */
      setBatchNotifyFunction: (o) => {
        n = o;
      },
      setScheduler: (o) => {
        a = o;
      }
    };
  }
  var fi = bf();
  function Sf(e, t) {
    return {
      fetchFailureCount: 0,
      fetchFailureReason: null,
      fetchStatus: yf(t.networkMode) ? "fetching" : "paused",
      ...e === void 0 && {
        error: null,
        status: "pending"
      }
    };
  }
  var Se, $, vr, xe, xt, Pt, tt, rt, gr, Mt, Nt, bt, St, nt, Bt, J, Xt, On, Rn, _n, Un, Ln, An, In, hi, hs, kf = (hs = class extends Kn {
    constructor(t, r) {
      super();
      oe(this, J);
      oe(this, Se);
      oe(this, $);
      oe(this, vr);
      oe(this, xe);
      oe(this, xt);
      oe(this, Pt);
      oe(this, tt);
      oe(this, rt);
      oe(this, gr);
      oe(this, Mt);
      // This property keeps track of the last query with defined data.
      // It will be used to pass the previous data and query to the placeholder function between renders.
      oe(this, Nt);
      oe(this, bt);
      oe(this, St);
      oe(this, nt);
      oe(this, Bt, /* @__PURE__ */ new Set());
      this.options = r, X(this, Se, t), X(this, rt, null), X(this, tt, Ka()), this.options.experimental_prefetchInRender || k(this, tt).reject(
        new Error("experimental_prefetchInRender feature flag is not enabled")
      ), this.bindMethods(), this.setOptions(r);
    }
    bindMethods() {
      this.refetch = this.refetch.bind(this);
    }
    onSubscribe() {
      this.listeners.size === 1 && (k(this, $).addObserver(this), es(k(this, $), this.options) ? ve(this, J, Xt).call(this) : this.updateResult(), ve(this, J, Un).call(this));
    }
    onUnsubscribe() {
      this.hasListeners() || this.destroy();
    }
    shouldFetchOnReconnect() {
      return Dn(
        k(this, $),
        this.options,
        this.options.refetchOnReconnect
      );
    }
    shouldFetchOnWindowFocus() {
      return Dn(
        k(this, $),
        this.options,
        this.options.refetchOnWindowFocus
      );
    }
    destroy() {
      this.listeners = /* @__PURE__ */ new Set(), ve(this, J, Ln).call(this), ve(this, J, An).call(this), k(this, $).removeObserver(this);
    }
    setOptions(t) {
      const r = this.options, n = k(this, $);
      if (this.options = k(this, Se).defaultQueryOptions(t), this.options.enabled !== void 0 && typeof this.options.enabled != "boolean" && typeof this.options.enabled != "function" && typeof Ge(this.options.enabled, k(this, $)) != "boolean")
        throw new Error(
          "Expected enabled to be a boolean or a callback that returns a boolean"
        );
      ve(this, J, In).call(this), k(this, $).setOptions(this.options), r._defaulted && !Fn(this.options, r) && k(this, Se).getQueryCache().notify({
        type: "observerOptionsUpdated",
        query: k(this, $),
        observer: this
      });
      const a = this.hasListeners();
      a && ts(
        k(this, $),
        n,
        this.options,
        r
      ) && ve(this, J, Xt).call(this), this.updateResult(), a && (k(this, $) !== n || Ge(this.options.enabled, k(this, $)) !== Ge(r.enabled, k(this, $)) || Qt(this.options.staleTime, k(this, $)) !== Qt(r.staleTime, k(this, $))) && ve(this, J, On).call(this);
      const s = ve(this, J, Rn).call(this);
      a && (k(this, $) !== n || Ge(this.options.enabled, k(this, $)) !== Ge(r.enabled, k(this, $)) || s !== k(this, nt)) && ve(this, J, _n).call(this, s);
    }
    getOptimisticResult(t) {
      const r = k(this, Se).getQueryCache().build(k(this, Se), t), n = this.createResult(r, t);
      return wf(this, n) && (X(this, xe, n), X(this, Pt, this.options), X(this, xt, k(this, $).state)), n;
    }
    getCurrentResult() {
      return k(this, xe);
    }
    trackResult(t, r) {
      return new Proxy(t, {
        get: (n, a) => (this.trackProp(a), r == null || r(a), Reflect.get(n, a))
      });
    }
    trackProp(t) {
      k(this, Bt).add(t);
    }
    getCurrentQuery() {
      return k(this, $);
    }
    refetch({ ...t } = {}) {
      return this.fetch({
        ...t
      });
    }
    fetchOptimistic(t) {
      const r = k(this, Se).defaultQueryOptions(t), n = k(this, Se).getQueryCache().build(k(this, Se), r);
      return n.fetch().then(() => this.createResult(n, r));
    }
    fetch(t) {
      return ve(this, J, Xt).call(this, {
        ...t,
        cancelRefetch: t.cancelRefetch ?? !0
      }).then(() => (this.updateResult(), k(this, xe)));
    }
    createResult(t, r) {
      var Q;
      const n = k(this, $), a = this.options, s = k(this, xe), i = k(this, xt), o = k(this, Pt), l = t !== n ? t.state : k(this, vr), { state: c } = t;
      let h = { ...c }, f = !1, d;
      if (r._optimisticResults) {
        const q = this.hasListeners(), ae = !q && es(t, r), ne = q && ts(t, n, r, a);
        (ae || ne) && (h = {
          ...h,
          ...Sf(c.data, t.options)
        }), r._optimisticResults === "isRestoring" && (h.fetchStatus = "idle");
      }
      let { error: v, errorUpdatedAt: S, status: m } = h;
      d = h.data;
      let x = !1;
      if (r.placeholderData !== void 0 && d === void 0 && m === "pending") {
        let q;
        s != null && s.isPlaceholderData && r.placeholderData === (o == null ? void 0 : o.placeholderData) ? (q = s.data, x = !0) : q = typeof r.placeholderData == "function" ? r.placeholderData(
          (Q = k(this, Nt)) == null ? void 0 : Q.state.data,
          k(this, Nt)
        ) : r.placeholderData, q !== void 0 && (m = "success", d = Ja(
          s == null ? void 0 : s.data,
          q,
          r
        ), f = !0);
      }
      if (r.select && d !== void 0 && !x)
        if (s && d === (i == null ? void 0 : i.data) && r.select === k(this, gr))
          d = k(this, Mt);
        else
          try {
            X(this, gr, r.select), d = r.select(d), d = Ja(s == null ? void 0 : s.data, d, r), X(this, Mt, d), X(this, rt, null);
          } catch (q) {
            X(this, rt, q);
          }
      k(this, rt) && (v = k(this, rt), d = k(this, Mt), S = Date.now(), m = "error");
      const E = h.fetchStatus === "fetching", b = m === "pending", M = m === "error", I = b && E, W = d !== void 0, w = {
        status: m,
        fetchStatus: h.fetchStatus,
        isPending: b,
        isSuccess: m === "success",
        isError: M,
        isInitialLoading: I,
        isLoading: I,
        data: d,
        dataUpdatedAt: h.dataUpdatedAt,
        error: v,
        errorUpdatedAt: S,
        failureCount: h.fetchFailureCount,
        failureReason: h.fetchFailureReason,
        errorUpdateCount: h.errorUpdateCount,
        isFetched: h.dataUpdateCount > 0 || h.errorUpdateCount > 0,
        isFetchedAfterMount: h.dataUpdateCount > l.dataUpdateCount || h.errorUpdateCount > l.errorUpdateCount,
        isFetching: E,
        isRefetching: E && !b,
        isLoadingError: M && !W,
        isPaused: h.fetchStatus === "paused",
        isPlaceholderData: f,
        isRefetchError: M && W,
        isStale: ea(t, r),
        refetch: this.refetch,
        promise: k(this, tt)
      };
      if (this.options.experimental_prefetchInRender) {
        const q = (K) => {
          w.status === "error" ? K.reject(w.error) : w.data !== void 0 && K.resolve(w.data);
        }, ae = () => {
          const K = X(this, tt, w.promise = Ka());
          q(K);
        }, ne = k(this, tt);
        switch (ne.status) {
          case "pending":
            t.queryHash === n.queryHash && q(ne);
            break;
          case "fulfilled":
            (w.status === "error" || w.data !== ne.value) && ae();
            break;
          case "rejected":
            (w.status !== "error" || w.error !== ne.reason) && ae();
            break;
        }
      }
      return w;
    }
    updateResult() {
      const t = k(this, xe), r = this.createResult(k(this, $), this.options);
      if (X(this, xt, k(this, $).state), X(this, Pt, this.options), k(this, xt).data !== void 0 && X(this, Nt, k(this, $)), Fn(r, t))
        return;
      X(this, xe, r);
      const n = () => {
        if (!t)
          return !0;
        const { notifyOnChangeProps: a } = this.options, s = typeof a == "function" ? a() : a;
        if (s === "all" || !s && !k(this, Bt).size)
          return !0;
        const i = new Set(
          s ?? k(this, Bt)
        );
        return this.options.throwOnError && i.add("error"), Object.keys(k(this, xe)).some((o) => {
          const u = o;
          return k(this, xe)[u] !== t[u] && i.has(u);
        });
      };
      ve(this, J, hi).call(this, { listeners: n() });
    }
    onQueryUpdate() {
      this.updateResult(), this.hasListeners() && ve(this, J, Un).call(this);
    }
  }, Se = new WeakMap(), $ = new WeakMap(), vr = new WeakMap(), xe = new WeakMap(), xt = new WeakMap(), Pt = new WeakMap(), tt = new WeakMap(), rt = new WeakMap(), gr = new WeakMap(), Mt = new WeakMap(), Nt = new WeakMap(), bt = new WeakMap(), St = new WeakMap(), nt = new WeakMap(), Bt = new WeakMap(), J = new WeakSet(), Xt = function(t) {
    ve(this, J, In).call(this);
    let r = k(this, $).fetch(
      this.options,
      t
    );
    return t != null && t.throwOnError || (r = r.catch(Cn)), r;
  }, On = function() {
    ve(this, J, Ln).call(this);
    const t = Qt(
      this.options.staleTime,
      k(this, $)
    );
    if (rr || k(this, xe).isStale || !$a(t))
      return;
    const n = hf(k(this, xe).dataUpdatedAt, t) + 1;
    X(this, bt, setTimeout(() => {
      k(this, xe).isStale || this.updateResult();
    }, n));
  }, Rn = function() {
    return (typeof this.options.refetchInterval == "function" ? this.options.refetchInterval(k(this, $)) : this.options.refetchInterval) ?? !1;
  }, _n = function(t) {
    ve(this, J, An).call(this), X(this, nt, t), !(rr || Ge(this.options.enabled, k(this, $)) === !1 || !$a(k(this, nt)) || k(this, nt) === 0) && X(this, St, setInterval(() => {
      (this.options.refetchIntervalInBackground || vf.isFocused()) && ve(this, J, Xt).call(this);
    }, k(this, nt)));
  }, Un = function() {
    ve(this, J, On).call(this), ve(this, J, _n).call(this, ve(this, J, Rn).call(this));
  }, Ln = function() {
    k(this, bt) && (clearTimeout(k(this, bt)), X(this, bt, void 0));
  }, An = function() {
    k(this, St) && (clearInterval(k(this, St)), X(this, St, void 0));
  }, In = function() {
    const t = k(this, Se).getQueryCache().build(k(this, Se), this.options);
    if (t === k(this, $))
      return;
    const r = k(this, $);
    X(this, $, t), X(this, vr, t.state), this.hasListeners() && (r == null || r.removeObserver(this), t.addObserver(this));
  }, hi = function(t) {
    fi.batch(() => {
      t.listeners && this.listeners.forEach((r) => {
        r(k(this, xe));
      }), k(this, Se).getQueryCache().notify({
        query: k(this, $),
        type: "observerResultsUpdated"
      });
    });
  }, hs);
  function Tf(e, t) {
    return Ge(t.enabled, e) !== !1 && e.state.data === void 0 && !(e.state.status === "error" && t.retryOnMount === !1);
  }
  function es(e, t) {
    return Tf(e, t) || e.state.data !== void 0 && Dn(e, t, t.refetchOnMount);
  }
  function Dn(e, t, r) {
    if (Ge(t.enabled, e) !== !1 && Qt(t.staleTime, e) !== "static") {
      const n = typeof r == "function" ? r(e) : r;
      return n === "always" || n !== !1 && ea(e, t);
    }
    return !1;
  }
  function ts(e, t, r, n) {
    return (e !== t || Ge(n.enabled, e) === !1) && (!r.suspense || e.state.status !== "error") && ea(e, r);
  }
  function ea(e, t) {
    return Ge(t.enabled, e) !== !1 && e.isStaleByTime(Qt(t.staleTime, e));
  }
  function wf(e, t) {
    return !Fn(e.getCurrentResult(), t);
  }
  var Cf = Fe.createContext(
    void 0
  ), Ef = (e) => {
    const t = Fe.useContext(Cf);
    if (!t)
      throw new Error("No QueryClient set, use QueryClientProvider to set one");
    return t;
  }, di = Fe.createContext(!1), Ff = () => Fe.useContext(di);
  di.Provider;
  function Of() {
    let e = !1;
    return {
      clearReset: () => {
        e = !1;
      },
      reset: () => {
        e = !0;
      },
      isReset: () => e
    };
  }
  var Rf = Fe.createContext(Of()), _f = () => Fe.useContext(Rf), Uf = (e, t) => {
    (e.suspense || e.throwOnError || e.experimental_prefetchInRender) && (t.isReset() || (e.retryOnMount = !1));
  }, Lf = (e) => {
    Fe.useEffect(() => {
      e.clearReset();
    }, [e]);
  }, Af = ({
    result: e,
    errorResetBoundary: t,
    throwOnError: r,
    query: n,
    suspense: a
  }) => e.isError && !t.isReset() && !e.isFetching && n && (a && e.data === void 0 || df(r, [e.error, n])), If = (e) => {
    if (e.suspense) {
      const t = (n) => n === "static" ? n : Math.max(n ?? 1e3, 1e3), r = e.staleTime;
      e.staleTime = typeof r == "function" ? (...n) => t(r(...n)) : t(r), typeof e.gcTime == "number" && (e.gcTime = Math.max(e.gcTime, 1e3));
    }
  }, Df = (e, t) => e.isLoading && e.isFetching && !t, Pf = (e, t) => (e == null ? void 0 : e.suspense) && t.isPending, rs = (e, t, r) => t.fetchOptimistic(e).catch(() => {
    r.clearReset();
  });
  function Mf(e, t, r) {
    var h, f, d, v, S;
    if (process.env.NODE_ENV !== "production" && (typeof e != "object" || Array.isArray(e)))
      throw new Error(
        'Bad argument type. Starting with v5, only the "Object" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object'
      );
    const n = Ff(), a = _f(), s = Ef(), i = s.defaultQueryOptions(e);
    (f = (h = s.getDefaultOptions().queries) == null ? void 0 : h._experimental_beforeQuery) == null || f.call(
      h,
      i
    ), process.env.NODE_ENV !== "production" && (i.queryFn || console.error(
      `[${i.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`
    )), i._optimisticResults = n ? "isRestoring" : "optimistic", If(i), Uf(i, a), Lf(a);
    const o = !s.getQueryCache().get(i.queryHash), [u] = Fe.useState(
      () => new t(
        s,
        i
      )
    ), l = u.getOptimisticResult(i), c = !n && e.subscribed !== !1;
    if (Fe.useSyncExternalStore(
      Fe.useCallback(
        (m) => {
          const x = c ? u.subscribe(fi.batchCalls(m)) : Cn;
          return u.updateResult(), x;
        },
        [u, c]
      ),
      () => u.getCurrentResult(),
      () => u.getCurrentResult()
    ), Fe.useEffect(() => {
      u.setOptions(i);
    }, [i, u]), Pf(i, l))
      throw rs(i, u, a);
    if (Af({
      result: l,
      errorResetBoundary: a,
      throwOnError: i.throwOnError,
      query: s.getQueryCache().get(i.queryHash),
      suspense: i.suspense
    }))
      throw l.error;
    if ((v = (d = s.getDefaultOptions().queries) == null ? void 0 : d._experimental_afterQuery) == null || v.call(
      d,
      i,
      l
    ), i.experimental_prefetchInRender && !rr && Df(l, n)) {
      const m = o ? (
        // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted
        rs(i, u, a)
      ) : (
        // subscribe to the "cache promise" so that we can finalize the currentThenable once data comes in
        (S = s.getQueryCache().get(i.queryHash)) == null ? void 0 : S.promise
      );
      m == null || m.catch(Cn).finally(() => {
        u.updateResult();
      });
    }
    return i.notifyOnChangeProps ? l : u.trackResult(l);
  }
  function Nf(e, t) {
    return Mf(e, kf);
  }
  const mt = class mt {
    constructor() {
      ht(this, "cache", /* @__PURE__ */ new Map());
    }
    static getInstance() {
      return mt.instance || (mt.instance = new mt()), mt.instance;
    }
    getCacheKey(t, r, n, a) {
      return a !== void 0 ? `${t}:${r}:${n}:${a}` : `${t}:${r}:${n}`;
    }
    getCachedWidth(t, r, n, a) {
      const s = this.getCacheKey(t, r, n, a);
      return this.cache.get(s) || null;
    }
    setCachedWidth(t, r, n, a, s) {
      const i = this.getCacheKey(t, r, n, s);
      this.cache.set(i, a);
    }
  };
  ht(mt, "instance", null);
  let Pn = mt;
  class Bf {
    constructor(t, r, n, a = 0) {
      ht(this, "fontPath");
      ht(this, "fontSize");
      ht(this, "containerWidth");
      ht(this, "letterSpacing");
      this.fontPath = t, this.fontSize = r, this.containerWidth = n, this.letterSpacing = a;
    }
    /**
     * 获取文本片段的宽度（包含字间距）
     */
    getTextWidth(t) {
      if (!t) return 0;
      try {
        const r = this.fontPath.getAdvanceWidth(t, this.fontSize), n = Math.max(0, t.length - 1) * this.letterSpacing;
        return r + n;
      } catch (r) {
        console.warn("[贪心换行] 获取文本宽度失败:", t, r);
        const n = t.length * this.fontSize * 0.6, a = Math.max(0, t.length - 1) * this.letterSpacing;
        return n + a;
      }
    }
    /**
     * 贪心算法：考虑单词边界
     * @param content 要分割的文本内容
     * @param respectWordBoundary 是否尊重单词边界（对中文无效）
     * @returns 分割后的字符串数组
     */
    wrapText(t, r = !1) {
      if (!t || !this.fontPath)
        return [t || ""];
      const n = [], a = t.split(`
`);
      for (const s of a) {
        if (!s.trim()) {
          n.push("");
          continue;
        }
        r && /^[a-zA-Z\s]+$/.test(s) ? this.wrapEnglishText(s, n) : this.wrapChineseText(s, n);
      }
      return n.length > 0 ? n : [""];
    }
    /**
     * 处理英文文本换行（按单词）
     */
    wrapEnglishText(t, r) {
      const n = t.split(/(\s+)/);
      let a = "", s = 0;
      for (const i of n) {
        const o = this.getTextWidth(i);
        s + o > this.containerWidth && a.trim().length > 0 ? (r.push(a.trimEnd()), a = i, s = o) : (a += i, s += o);
      }
      a.trim().length > 0 && r.push(a.trimEnd());
    }
    /**
     * 处理中文文本换行（按字符）
     */
    wrapChineseText(t, r) {
      let n = "", a = 0;
      for (let s = 0; s < t.length; s++) {
        const i = t[s], o = this.fontPath.getAdvanceWidth(i, this.fontSize), u = n.length === 0 ? o : o + this.letterSpacing;
        a + u > this.containerWidth && n.length > 0 ? (r.push(n), n = i, a = o) : (n += i, a += u);
      }
      n.length > 0 && r.push(n);
    }
    /**
     * 获取换行后的总高度
     * @param lines 文本行数组
     * @param lineHeight 自定义行高，如果不提供则使用默认值
     */
    // public getWrappedTextHeight(lines: string[], lineHeight?: number): number {
    //   const actualLineHeight = lineHeight || this.fontSize * 1.2
    //   return lines.length * actualLineHeight
    // }
    /**
     * 获取每行的详细信息
     * @param lines 文本行数组
     * @param lineHeight 自定义行高，如果不提供则使用默认值
     */
    // public getLineDetails(lines: string[], lineHeight?: number): Array<{ line: string, width: number, height: number }> {
    //   const actualLineHeight = lineHeight || this.fontSize * 1.2
    //
    //   return lines.map(line => ({
    //     line,
    //     width: this.getTextWidth(line),
    //     height: actualLineHeight
    //   }))
    // }
    /**
     * 获取换行后的总高度（包含行间距）
     * @param lines 文本行数组
     * @param lineHeight 行高，如果不提供则使用默认值
     * @param lineSpacingRatio 行间距倍数，默认为0
     */
    getWrappedTextHeightWithSpacing(t, r, n = 0) {
      const a = r || this.fontSize * 1.2, s = t.length, i = n * this.fontSize;
      return s * a + Math.max(0, s - 1) * i;
    }
    /**
     * 获取每行的详细信息（包含行间距）
     * @param lines 文本行数组
     * @param lineHeight 行高，如果不提供则使用默认值
     * @param lineSpacingRatio 行间距倍数，默认为0
     */
    getLineDetailsWithSpacing(t, r, n = 0) {
      const a = r || this.fontSize * 1.2, s = n * this.fontSize;
      return t.map((i, o) => ({
        line: i,
        width: this.getTextWidth(i),
        height: a,
        y: o * (a + s)
        // 每行的Y坐标包含行间距
      }));
    }
  }
  const ns = Pn.getInstance(), Gf = 1, as = (e, t, r, n, a = 0) => {
    if (!r)
      return 1;
    const s = ns.getCachedWidth(t, r, n, a);
    if (s !== null)
      return s;
    try {
      const i = e.getAdvanceWidth(r, n), o = Math.max(0, r.length - 1) * a, u = i + o;
      return ns.setCachedWidth(t, r, n, u, a), u;
    } catch (i) {
      return console.error("[增强文本渲染器] 计算文本宽度失败:", i), 1;
    }
  };
  function Hf(e, t, r, n, a, s = 0, i = !1) {
    try {
      return new Bf(e, a, n, s).wrapText(r, i).map((l) => ({
        content: l,
        width: as(e, t, l, a, s)
      }));
    } catch (o) {
      return console.error("[增强文本渲染器] 优化贪心换行失败:", o), [{
        content: r,
        width: as(e, t, r, a, s)
      }];
    }
  }
  function Wf(e, t, r) {
    const {
      width: n = t.width,
      content: a = t.content,
      fontSize: s = t.styles.fontSize ?? 0,
      lineSpacing: i = t.styles.lineSpacing ?? 0,
      letterSpacing: o = t.styles.letterSpacing ?? 0
    } = r || {}, u = s * Gf, l = i * s, c = Hf(
      e,
      t.src,
      a,
      n,
      s,
      o,
      !0
    ), h = c.length, f = h * u + Math.max(0, h - 1) * l, d = Math.max(...c.map((v) => v.width));
    return {
      wrappedLines: c,
      lineHeight: u,
      totalHeight: f,
      maxLineWidth: d
    };
  }
  const zf = (e, t) => ({
    buildCalcTextRenderInfoFunction: Mr(
      () => !t || !e ? null : (n) => {
        const {
          width: a = t.width,
          fontSize: s = t.styles.fontSize,
          lineSpacing: i = t.styles.lineSpacing ?? 0,
          letterSpacing: o = t.styles.letterSpacing ?? 0
        } = n || {}, u = a / 2, l = t.height / 2, c = t.styles.textAlign, h = i * s, { totalHeight: f, lineHeight: d, wrappedLines: v, maxLineWidth: S } = Wf(
          e,
          t,
          n
        ), m = () => {
          switch (c) {
            case "left":
              return 0;
            case "right":
              return a;
            case "center":
            default:
              return u;
          }
        }, x = 1, E = m(), b = l - f / 2 + d / 2;
        return {
          x: E,
          y: b,
          scale: x,
          fontSize: s,
          letterSpacing: o,
          wrappedLines: v,
          lineHeight: d,
          totalHeight: f,
          minHeight: f,
          minWidth: S,
          lineSpacing: h
        };
      },
      [t, e]
    )
  }), jf = "CURRENT_FONT_PATH", ta = 0, Zf = ({ overlay: e, font: t, renderInfo: r }) => {
    const { styles: n } = e, a = !!(n.strokeEnabled && e.styles.strokeWidth && e.styles.strokeColor), s = !!(n.shadowEnabled && e.styles.shadowDistance && e.styles.shadowColor);
    if (!a && !s)
      return null;
    const i = ps(null);
    if (e.width === 0)
      return null;
    const o = Mr(
      () => {
        const f = [];
        if (s) {
          const d = e.styles.shadowDistance ? e.styles.shadowDistance / 2 : 0, v = e.styles.shadowAngle || 45, S = e.styles.shadowBlur || 2, m = e.styles.shadowColor || "#000000", x = e.styles.shadowOpacity || 0.5;
          if (d > 0) {
            const E = v * Math.PI / 180, b = Math.cos(E) * d, M = Math.sin(E) * d;
            f.push(
              /* @__PURE__ */ N.jsx(
                "filter",
                {
                  id: `shadow-${e.id}`,
                  x: "-100%",
                  y: "-100%",
                  width: "300%",
                  height: "300%",
                  filterUnits: "userSpaceOnUse",
                  colorInterpolationFilters: "sRGB",
                  children: /* @__PURE__ */ N.jsx(
                    "feDropShadow",
                    {
                      dx: b,
                      dy: M,
                      stdDeviation: S,
                      floodColor: m,
                      floodOpacity: x
                    }
                  )
                },
                "shadow"
              )
            );
          }
        }
        return f;
      },
      [s, e.styles, e.id]
    ), u = Mr(
      () => {
        try {
          const f = e.styles.strokeWidth ?? 0, d = e.styles.strokeColor || "#000000", v = s ? `shadow-${e.id}` : void 0, S = new ff(t, t), m = [], {
            x,
            y: E,
            fontSize: b,
            wrappedLines: M,
            lineHeight: I,
            lineSpacing: W
          } = r;
          return M.forEach((U, w) => {
            if (!U.content.trim()) return;
            const { d: Q } = S.getD(U.content, {
              x: 0,
              y: 0,
              fontSize: b,
              anchor: "left middle",
              letterSpacing: (e.styles.letterSpacing ?? 0) / b
              // 转换为相对值
            });
            if (!Q) return;
            const q = E + w * (I + W), ae = n.textAlign || "center", ne = U.width;
            let K;
            switch (ae) {
              case "left":
                K = x;
                break;
              case "right":
                K = x - ne;
                break;
              case "center":
              default:
                K = x - ne / 2;
                break;
            }
            let te = `translate(${K}, ${q})`;
            if (n.fontStyle === "italic" && (te += " skewX(-12)"), m.push(
              /* @__PURE__ */ N.jsx(
                "path",
                {
                  d: Q,
                  fill: "none",
                  stroke: a ? d : "none",
                  strokeWidth: f,
                  strokeLinejoin: "round",
                  strokeLinecap: "round",
                  filter: v ? `url(#${v})` : void 0,
                  transform: te,
                  style: {
                    vectorEffect: "non-scaling-stroke"
                  }
                },
                `line-${w}`
              )
            ), n.fontWeight === "bold") {
              const ee = Math.max(0.5, b * 0.01);
              [
                `translate(${K + ee}, ${q})`,
                `translate(${K}, ${q + ee})`,
                `translate(${K + ee}, ${q + ee})`
              ].forEach((le, ce) => {
                let se = le;
                n.fontStyle === "italic" && (se += " skewX(-12)"), m.push(
                  /* @__PURE__ */ N.jsx(
                    "path",
                    {
                      d: Q,
                      fill: "none",
                      stroke: a ? d : "none",
                      strokeWidth: f,
                      strokeLinejoin: "round",
                      strokeLinecap: "round",
                      filter: v ? `url(#${v})` : void 0,
                      transform: se,
                      style: {
                        vectorEffect: "non-scaling-stroke"
                      }
                    },
                    `line-bold-${w}-${ce}`
                  )
                );
              });
            }
            if (n.underlineEnabled) {
              const ee = q + b * 0.5 + b * 0.1, ie = Math.max(1, b * 0.05);
              m.push(
                /* @__PURE__ */ N.jsx(
                  "line",
                  {
                    x1: K,
                    y1: ee,
                    x2: K + ne,
                    y2: ee,
                    stroke: a ? d : e.styles.color || "#ffffff",
                    strokeWidth: ie,
                    strokeLinecap: "round",
                    filter: v ? `url(#${v})` : void 0
                  },
                  `underline-${w}`
                )
              );
            }
          }), /* @__PURE__ */ N.jsx("g", { children: m });
        } catch (f) {
          return console.error("[增强文本渲染器] SVG路径渲染失败:", f), null;
        }
      },
      [e, a, s, n, r]
    ), l = o(), c = u(), h = Math.max(e.height, r.totalHeight + ta);
    return /* @__PURE__ */ N.jsxs(
      "svg",
      {
        ref: i,
        style: {
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 1,
          pointerEvents: "none"
        },
        viewBox: `0 0 ${e.width} ${h}`,
        preserveAspectRatio: "xMidYMid meet",
        children: [
          /* @__PURE__ */ N.jsx("defs", { children: l }),
          c
        ]
      }
    );
  }, Vf = ({ overlay: e, renderInfo: t }) => {
    const r = ps(null), n = Mr((a, s, i, o, u, l, c) => {
      let h = i;
      if (l === "center" ? h = i - c / 2 : l === "right" && (h = i - c), u === 0) {
        a.fillText(s, h, o);
        return;
      }
      let f = h;
      for (let d = 0; d < s.length; d++) {
        const v = s[d];
        a.fillText(v, f, o);
        const S = a.measureText(v).width;
        f += S, d < s.length - 1 && (f += u);
      }
    }, []);
    return Nr(
      () => {
        if (!r.current || e.width === 0)
          return;
        const { styles: a } = e, s = r.current;
        if (!s) return;
        const i = s.getContext("2d");
        if (!i) return;
        const {
          x: o,
          y: u,
          totalHeight: l,
          wrappedLines: c,
          lineHeight: h,
          letterSpacing: f,
          lineSpacing: d
        } = t, v = Math.max(e.height, l + ta);
        s.width = e.width * window.devicePixelRatio, s.height = v * window.devicePixelRatio, s.style.width = `${e.width}px`, s.style.height = `${v}px`, i.scale(window.devicePixelRatio, window.devicePixelRatio), i.clearRect(0, 0, e.width, v);
        const S = a.fontFamily || "Arial", m = `${e.styles.fontSize}px "${e.styles.fontFamily}", ${S}, sans-serif`;
        i.font = m, i.fillStyle = e.styles.color || "#ffffff", i.textBaseline = "middle", c.forEach((x, E) => {
          const b = u + E * (h + d);
          i.save();
          let M = 0;
          a.fontStyle === "italic" && (M = b * -0.2, i.transform(1, 0, -0.2, 1, 0, 0));
          const I = o - M, W = a.textAlign || "center";
          if (n(
            i,
            x.content,
            I,
            b,
            f,
            W,
            x.width
          ), a.fontWeight === "bold") {
            const U = Math.max(0.5, e.styles.fontSize * 0.01);
            n(
              i,
              x.content,
              I + U,
              b,
              f,
              W,
              x.width
            ), n(
              i,
              x.content,
              I,
              b + U,
              f,
              W,
              x.width
            ), n(
              i,
              x.content,
              I + U,
              b + U,
              f,
              W,
              x.width
            );
          }
          i.restore();
        });
      },
      [e, t, n]
    ), /* @__PURE__ */ N.jsx(
      "canvas",
      {
        ref: r,
        style: {
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 2,
          pointerEvents: "none"
        }
      }
    );
  }, qf = ({ font: e, overlay: t, containerStyle: r }) => {
    const { styles: n } = t, a = !!n.backgroundImage, s = a && !!t.styles.bubbleTextRect, { buildCalcTextRenderInfoFunction: i } = zf(e, t), o = i(), u = vn(
      () => o(),
      [o]
    ), l = {
      ...r,
      backgroundImage: a ? `url(${t.styles.backgroundImage})` : void 0,
      backgroundSize: "contain",
      backgroundRepeat: "no-repeat",
      backgroundPosition: "center"
    }, c = vn(() => a ? {
      position: "absolute",
      left: "50%",
      top: "50%",
      transform: "translate(-50%, -50%)",
      width: "100%",
      height: "60%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      boxSizing: "border-box"
    } : {}, [a]), h = () => /* @__PURE__ */ N.jsxs(
      "div",
      {
        style: {
          position: "relative",
          width: "100%",
          height: t.height,
          backgroundColor: n.backgroundColor,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          opacity: t.styles.textOpacity ?? 1
        },
        children: [
          /* @__PURE__ */ N.jsx(Zf, { overlay: t, renderInfo: u, font: e }),
          /* @__PURE__ */ N.jsx(Vf, { overlay: t, renderInfo: u })
        ]
      }
    ), f = () => s ? /* @__PURE__ */ N.jsx("div", { style: c, children: h() }) : h(), d = Math.max(t.height, u.totalHeight + ta);
    return /* @__PURE__ */ N.jsx(
      "div",
      {
        style: {
          ...l,
          position: "relative",
          overflow: "hidden",
          height: `${d}px`
        },
        children: f()
      }
    );
  }, $f = ({
    overlay: e,
    containerStyle: t
  }) => {
    const [r] = Fi(() => vs()), { data: n } = Nf({
      queryKey: [jf, e.src, r, e.styles.fontFamily],
      queryFn: async () => {
        const a = e.src, s = e.styles.fontFamily;
        if (!a)
          return null;
        const i = await uf(e.src);
        try {
          const u = ((c) => {
            if (c.startsWith("http://") || c.startsWith("https://"))
              return c;
            const h = c.replace(/\\/g, "/");
            return h.startsWith("/") ? `file://${h}` : `file:///${h}`;
          })(a);
          if (!Array.from(document.fonts).find(
            (c) => c.family === s
          )) {
            const c = new FontFace(s, `url("${u}")`);
            await c.load(), document.fonts.add(c), console.log(`[CLOUD] 字体已加载到 DOM: ${s}`);
          }
        } catch (o) {
          console.warn("[CLOUD] DOM 字体加载失败，但 opentype.js 加载成功:", o);
        }
        return {
          font: i,
          fontFamily: s
        };
      },
      enabled: !!e.src,
      staleTime: 1 / 0
    });
    return Nr(() => {
      (async () => {
        if (n != null && n.fontFamily)
          try {
            await document.fonts.ready, await document.fonts.load(`${e.styles.fontSize}px "${n.fontFamily}"`);
          } catch (a) {
            console.warn("[CLOUD] 等待字体加载失败:", a);
          }
      })();
    }, []), Nr(() => {
      n && Ar(r);
    }, [n]), n ? /* @__PURE__ */ N.jsx(
      qf,
      {
        font: n.font,
        overlay: e,
        containerStyle: t
      }
    ) : null;
  }, Xf = Hn.memo(
    $f,
    (e, t) => {
      const r = e.overlay.id === t.overlay.id && e.overlay.content === t.overlay.content && e.overlay.src === t.overlay.src && e.overlay.width === t.overlay.width && e.overlay.left === t.overlay.left && e.overlay.top === t.overlay.top && e.overlay.rotation === t.overlay.rotation, n = JSON.stringify(e.overlay.styles) === JSON.stringify(t.overlay.styles), a = JSON.stringify(e.containerStyle) === JSON.stringify(t.containerStyle);
      return r && n && a;
    }
  ), Yf = ({ overlay: e }) => {
    var o, u, l, c;
    const t = mr(), { playerMetadata: { fps: r } } = yr(), n = t >= e.durationInFrames - r, a = !n && ((o = e.styles.animation) != null && o.enter) ? (u = Gt[e.styles.animation.enter]) == null ? void 0 : u.enter(
      t,
      e.durationInFrames
    ) : {}, s = n && ((l = e.styles.animation) != null && l.exit) ? (c = Gt[e.styles.animation.exit]) == null ? void 0 : c.exit(
      t,
      e.durationInFrames
    ) : {}, i = {
      width: "100%",
      height: "100%",
      display: "flex",
      alignItems: "center",
      // Center vertically
      textAlign: e.styles.textAlign,
      justifyContent: e.styles.textAlign === "center" ? "center" : e.styles.textAlign === "right" ? "flex-end" : "flex-start",
      overflow: "hidden",
      // 确保容器能够容纳换行文本
      boxSizing: "border-box",
      ...n ? s : a
    };
    return /* @__PURE__ */ N.jsx(
      Xf,
      {
        overlay: e,
        containerStyle: i
      }
    );
  }, Qf = ({
    overlay: e,
    baseUrl: t
  }) => {
    var E, b, M, I;
    const r = mr(), { playerMetadata: { fps: n } } = yr();
    let a = e.src;
    e.src.startsWith("/") && t ? a = `${t}${e.src}` : e.src.startsWith("/") && (a = ys(e.src)), Nr(() => {
      const W = vs("Loading video"), U = document.createElement("video");
      U.src = a;
      const w = () => {
        Ar(W);
      }, Q = (q) => {
        Ar(W);
      };
      return U.addEventListener("loadedmetadata", w), U.addEventListener("error", Q), () => {
        U.removeEventListener("loadedmetadata", w), U.removeEventListener("error", Q), Ar(W);
      };
    }, [e.src]);
    const s = e.fadeInDuration ? Math.round(e.fadeInDuration * n) : 0, i = e.fadeOutDuration ? Math.round(e.fadeOutDuration * n) : 0;
    let o = 1, u = 1;
    s > 0 && r < s && (o = r / s), i > 0 && r >= e.durationInFrames - i && (u = (e.durationInFrames - r) / i);
    let l = o * u;
    l = Math.max(0, Math.min(1, l));
    const c = r >= e.durationInFrames - n, h = !c && ((E = e.styles.animation) != null && E.enter) ? (b = Gt[e.styles.animation.enter]) == null ? void 0 : b.enter(
      r,
      e.durationInFrames
    ) : {}, f = c && ((M = e.styles.animation) != null && M.exit) ? (I = Gt[e.styles.animation.exit]) == null ? void 0 : I.exit(
      r,
      e.durationInFrames
    ) : {}, d = {
      width: "100%",
      height: "100%",
      objectFit: e.styles.objectFit || "cover",
      // 将基础不透明度与淡入淡出不透明度相乘，确保两者都生效
      opacity: (e.styles.opacity ?? 1) * l,
      transform: e.styles.transform || "none",
      borderRadius: e.styles.borderRadius || "0px",
      filter: e.styles.filter || "none",
      boxShadow: e.styles.boxShadow || "none",
      border: e.styles.border || "none",
      // 应用动画效果
      ...c ? f : h,
      // 添加平滑过渡效果
      transition: "opacity 0.1s ease-in-out"
    }, v = {
      width: "100%",
      height: "100%",
      padding: e.styles.padding || "0px",
      backgroundColor: e.styles.paddingBackgroundColor || "transparent",
      display: "flex",
      // Use flexbox for centering
      alignItems: "center",
      justifyContent: "center"
    }, S = (e.videoStartTime || 0) + (e.trimStart || 0), m = e.trimEnd ? Math.round(e.trimEnd * n) : 0, x = e.durationInFrames - m;
    return r >= x ? /* @__PURE__ */ N.jsx("div", { style: v }) : /* @__PURE__ */ N.jsx("div", { style: v, children: /* @__PURE__ */ N.jsx(
      _i,
      {
        src: a,
        startFrom: S,
        style: d,
        volume: e.styles.volume ?? 1,
        playbackRate: e.speed ?? 1
      }
    ) });
  };
  var Y;
  (function(e) {
    e.assertEqual = (a) => a;
    function t(a) {
    }
    e.assertIs = t;
    function r(a) {
      throw new Error();
    }
    e.assertNever = r, e.arrayToEnum = (a) => {
      const s = {};
      for (const i of a)
        s[i] = i;
      return s;
    }, e.getValidEnumValues = (a) => {
      const s = e.objectKeys(a).filter((o) => typeof a[a[o]] != "number"), i = {};
      for (const o of s)
        i[o] = a[o];
      return e.objectValues(i);
    }, e.objectValues = (a) => e.objectKeys(a).map(function(s) {
      return a[s];
    }), e.objectKeys = typeof Object.keys == "function" ? (a) => Object.keys(a) : (a) => {
      const s = [];
      for (const i in a)
        Object.prototype.hasOwnProperty.call(a, i) && s.push(i);
      return s;
    }, e.find = (a, s) => {
      for (const i of a)
        if (s(i))
          return i;
    }, e.isInteger = typeof Number.isInteger == "function" ? (a) => Number.isInteger(a) : (a) => typeof a == "number" && isFinite(a) && Math.floor(a) === a;
    function n(a, s = " | ") {
      return a.map((i) => typeof i == "string" ? `'${i}'` : i).join(s);
    }
    e.joinValues = n, e.jsonStringifyReplacer = (a, s) => typeof s == "bigint" ? s.toString() : s;
  })(Y || (Y = {}));
  var Mn;
  (function(e) {
    e.mergeShapes = (t, r) => ({
      ...t,
      ...r
      // second overwrites first
    });
  })(Mn || (Mn = {}));
  const F = Y.arrayToEnum([
    "string",
    "nan",
    "number",
    "integer",
    "float",
    "boolean",
    "date",
    "bigint",
    "symbol",
    "function",
    "undefined",
    "null",
    "array",
    "object",
    "unknown",
    "promise",
    "void",
    "never",
    "map",
    "set"
  ]), Je = (e) => {
    switch (typeof e) {
      case "undefined":
        return F.undefined;
      case "string":
        return F.string;
      case "number":
        return isNaN(e) ? F.nan : F.number;
      case "boolean":
        return F.boolean;
      case "function":
        return F.function;
      case "bigint":
        return F.bigint;
      case "symbol":
        return F.symbol;
      case "object":
        return Array.isArray(e) ? F.array : e === null ? F.null : e.then && typeof e.then == "function" && e.catch && typeof e.catch == "function" ? F.promise : typeof Map < "u" && e instanceof Map ? F.map : typeof Set < "u" && e instanceof Set ? F.set : typeof Date < "u" && e instanceof Date ? F.date : F.object;
      default:
        return F.unknown;
    }
  }, T = Y.arrayToEnum([
    "invalid_type",
    "invalid_literal",
    "custom",
    "invalid_union",
    "invalid_union_discriminator",
    "invalid_enum_value",
    "unrecognized_keys",
    "invalid_arguments",
    "invalid_return_type",
    "invalid_date",
    "invalid_string",
    "too_small",
    "too_big",
    "invalid_intersection_types",
    "not_multiple_of",
    "not_finite"
  ]), Jf = (e) => JSON.stringify(e, null, 2).replace(/"([^"]+)":/g, "$1:");
  class Re extends Error {
    constructor(t) {
      super(), this.issues = [], this.addIssue = (n) => {
        this.issues = [...this.issues, n];
      }, this.addIssues = (n = []) => {
        this.issues = [...this.issues, ...n];
      };
      const r = new.target.prototype;
      Object.setPrototypeOf ? Object.setPrototypeOf(this, r) : this.__proto__ = r, this.name = "ZodError", this.issues = t;
    }
    get errors() {
      return this.issues;
    }
    format(t) {
      const r = t || function(s) {
        return s.message;
      }, n = { _errors: [] }, a = (s) => {
        for (const i of s.issues)
          if (i.code === "invalid_union")
            i.unionErrors.map(a);
          else if (i.code === "invalid_return_type")
            a(i.returnTypeError);
          else if (i.code === "invalid_arguments")
            a(i.argumentsError);
          else if (i.path.length === 0)
            n._errors.push(r(i));
          else {
            let o = n, u = 0;
            for (; u < i.path.length; ) {
              const l = i.path[u];
              u === i.path.length - 1 ? (o[l] = o[l] || { _errors: [] }, o[l]._errors.push(r(i))) : o[l] = o[l] || { _errors: [] }, o = o[l], u++;
            }
          }
      };
      return a(this), n;
    }
    toString() {
      return this.message;
    }
    get message() {
      return JSON.stringify(this.issues, Y.jsonStringifyReplacer, 2);
    }
    get isEmpty() {
      return this.issues.length === 0;
    }
    flatten(t = (r) => r.message) {
      const r = {}, n = [];
      for (const a of this.issues)
        a.path.length > 0 ? (r[a.path[0]] = r[a.path[0]] || [], r[a.path[0]].push(t(a))) : n.push(t(a));
      return { formErrors: n, fieldErrors: r };
    }
    get formErrors() {
      return this.flatten();
    }
  }
  Re.create = (e) => new Re(e);
  const nr = (e, t) => {
    let r;
    switch (e.code) {
      case T.invalid_type:
        e.received === F.undefined ? r = "Required" : r = `Expected ${e.expected}, received ${e.received}`;
        break;
      case T.invalid_literal:
        r = `Invalid literal value, expected ${JSON.stringify(e.expected, Y.jsonStringifyReplacer)}`;
        break;
      case T.unrecognized_keys:
        r = `Unrecognized key(s) in object: ${Y.joinValues(e.keys, ", ")}`;
        break;
      case T.invalid_union:
        r = "Invalid input";
        break;
      case T.invalid_union_discriminator:
        r = `Invalid discriminator value. Expected ${Y.joinValues(e.options)}`;
        break;
      case T.invalid_enum_value:
        r = `Invalid enum value. Expected ${Y.joinValues(e.options)}, received '${e.received}'`;
        break;
      case T.invalid_arguments:
        r = "Invalid function arguments";
        break;
      case T.invalid_return_type:
        r = "Invalid function return type";
        break;
      case T.invalid_date:
        r = "Invalid date";
        break;
      case T.invalid_string:
        typeof e.validation == "object" ? "includes" in e.validation ? (r = `Invalid input: must include "${e.validation.includes}"`, typeof e.validation.position == "number" && (r = `${r} at one or more positions greater than or equal to ${e.validation.position}`)) : "startsWith" in e.validation ? r = `Invalid input: must start with "${e.validation.startsWith}"` : "endsWith" in e.validation ? r = `Invalid input: must end with "${e.validation.endsWith}"` : Y.assertNever(e.validation) : e.validation !== "regex" ? r = `Invalid ${e.validation}` : r = "Invalid";
        break;
      case T.too_small:
        e.type === "array" ? r = `Array must contain ${e.exact ? "exactly" : e.inclusive ? "at least" : "more than"} ${e.minimum} element(s)` : e.type === "string" ? r = `String must contain ${e.exact ? "exactly" : e.inclusive ? "at least" : "over"} ${e.minimum} character(s)` : e.type === "number" ? r = `Number must be ${e.exact ? "exactly equal to " : e.inclusive ? "greater than or equal to " : "greater than "}${e.minimum}` : e.type === "date" ? r = `Date must be ${e.exact ? "exactly equal to " : e.inclusive ? "greater than or equal to " : "greater than "}${new Date(Number(e.minimum))}` : r = "Invalid input";
        break;
      case T.too_big:
        e.type === "array" ? r = `Array must contain ${e.exact ? "exactly" : e.inclusive ? "at most" : "less than"} ${e.maximum} element(s)` : e.type === "string" ? r = `String must contain ${e.exact ? "exactly" : e.inclusive ? "at most" : "under"} ${e.maximum} character(s)` : e.type === "number" ? r = `Number must be ${e.exact ? "exactly" : e.inclusive ? "less than or equal to" : "less than"} ${e.maximum}` : e.type === "bigint" ? r = `BigInt must be ${e.exact ? "exactly" : e.inclusive ? "less than or equal to" : "less than"} ${e.maximum}` : e.type === "date" ? r = `Date must be ${e.exact ? "exactly" : e.inclusive ? "smaller than or equal to" : "smaller than"} ${new Date(Number(e.maximum))}` : r = "Invalid input";
        break;
      case T.custom:
        r = "Invalid input";
        break;
      case T.invalid_intersection_types:
        r = "Intersection results could not be merged";
        break;
      case T.not_multiple_of:
        r = `Number must be a multiple of ${e.multipleOf}`;
        break;
      case T.not_finite:
        r = "Number must be finite";
        break;
      default:
        r = t.defaultError, Y.assertNever(e);
    }
    return { message: r };
  };
  let pi = nr;
  function Kf(e) {
    pi = e;
  }
  function Zr() {
    return pi;
  }
  const Vr = (e) => {
    const { data: t, path: r, errorMaps: n, issueData: a } = e, s = [...r, ...a.path || []], i = {
      ...a,
      path: s
    };
    let o = "";
    const u = n.filter((l) => !!l).slice().reverse();
    for (const l of u)
      o = l(i, { data: t, defaultError: o }).message;
    return {
      ...a,
      path: s,
      message: a.message || o
    };
  }, eh = [];
  function R(e, t) {
    const r = Vr({
      issueData: t,
      data: e.data,
      path: e.path,
      errorMaps: [
        e.common.contextualErrorMap,
        e.schemaErrorMap,
        Zr(),
        nr
        // then global default map
      ].filter((n) => !!n)
    });
    e.common.issues.push(r);
  }
  class ye {
    constructor() {
      this.value = "valid";
    }
    dirty() {
      this.value === "valid" && (this.value = "dirty");
    }
    abort() {
      this.value !== "aborted" && (this.value = "aborted");
    }
    static mergeArray(t, r) {
      const n = [];
      for (const a of r) {
        if (a.status === "aborted")
          return z;
        a.status === "dirty" && t.dirty(), n.push(a.value);
      }
      return { status: t.value, value: n };
    }
    static async mergeObjectAsync(t, r) {
      const n = [];
      for (const a of r)
        n.push({
          key: await a.key,
          value: await a.value
        });
      return ye.mergeObjectSync(t, n);
    }
    static mergeObjectSync(t, r) {
      const n = {};
      for (const a of r) {
        const { key: s, value: i } = a;
        if (s.status === "aborted" || i.status === "aborted")
          return z;
        s.status === "dirty" && t.dirty(), i.status === "dirty" && t.dirty(), s.value !== "__proto__" && (typeof i.value < "u" || a.alwaysSet) && (n[s.value] = i.value);
      }
      return { status: t.value, value: n };
    }
  }
  const z = Object.freeze({
    status: "aborted"
  }), vi = (e) => ({ status: "dirty", value: e }), be = (e) => ({ status: "valid", value: e }), Nn = (e) => e.status === "aborted", Bn = (e) => e.status === "dirty", ar = (e) => e.status === "valid", qr = (e) => typeof Promise < "u" && e instanceof Promise;
  var D;
  (function(e) {
    e.errToObj = (t) => typeof t == "string" ? { message: t } : t || {}, e.toString = (t) => typeof t == "string" ? t : t == null ? void 0 : t.message;
  })(D || (D = {}));
  class ze {
    constructor(t, r, n, a) {
      this._cachedPath = [], this.parent = t, this.data = r, this._path = n, this._key = a;
    }
    get path() {
      return this._cachedPath.length || (this._key instanceof Array ? this._cachedPath.push(...this._path, ...this._key) : this._cachedPath.push(...this._path, this._key)), this._cachedPath;
    }
  }
  const ss = (e, t) => {
    if (ar(t))
      return { success: !0, data: t.value };
    if (!e.common.issues.length)
      throw new Error("Validation failed but no issues detected.");
    return {
      success: !1,
      get error() {
        if (this._error)
          return this._error;
        const r = new Re(e.common.issues);
        return this._error = r, this._error;
      }
    };
  };
  function j(e) {
    if (!e)
      return {};
    const { errorMap: t, invalid_type_error: r, required_error: n, description: a } = e;
    if (t && (r || n))
      throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
    return t ? { errorMap: t, description: a } : { errorMap: (i, o) => i.code !== "invalid_type" ? { message: o.defaultError } : typeof o.data > "u" ? { message: n ?? o.defaultError } : { message: r ?? o.defaultError }, description: a };
  }
  class V {
    constructor(t) {
      this.spa = this.safeParseAsync, this._def = t, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync = this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe = this.describe.bind(this), this.pipe = this.pipe.bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.bind(this), this.isOptional = this.isOptional.bind(this);
    }
    get description() {
      return this._def.description;
    }
    _getType(t) {
      return Je(t.data);
    }
    _getOrReturnCtx(t, r) {
      return r || {
        common: t.parent.common,
        data: t.data,
        parsedType: Je(t.data),
        schemaErrorMap: this._def.errorMap,
        path: t.path,
        parent: t.parent
      };
    }
    _processInputParams(t) {
      return {
        status: new ye(),
        ctx: {
          common: t.parent.common,
          data: t.data,
          parsedType: Je(t.data),
          schemaErrorMap: this._def.errorMap,
          path: t.path,
          parent: t.parent
        }
      };
    }
    _parseSync(t) {
      const r = this._parse(t);
      if (qr(r))
        throw new Error("Synchronous parse encountered promise.");
      return r;
    }
    _parseAsync(t) {
      const r = this._parse(t);
      return Promise.resolve(r);
    }
    parse(t, r) {
      const n = this.safeParse(t, r);
      if (n.success)
        return n.data;
      throw n.error;
    }
    safeParse(t, r) {
      var n;
      const a = {
        common: {
          issues: [],
          async: (n = r == null ? void 0 : r.async) !== null && n !== void 0 ? n : !1,
          contextualErrorMap: r == null ? void 0 : r.errorMap
        },
        path: (r == null ? void 0 : r.path) || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: t,
        parsedType: Je(t)
      }, s = this._parseSync({ data: t, path: a.path, parent: a });
      return ss(a, s);
    }
    async parseAsync(t, r) {
      const n = await this.safeParseAsync(t, r);
      if (n.success)
        return n.data;
      throw n.error;
    }
    async safeParseAsync(t, r) {
      const n = {
        common: {
          issues: [],
          contextualErrorMap: r == null ? void 0 : r.errorMap,
          async: !0
        },
        path: (r == null ? void 0 : r.path) || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: t,
        parsedType: Je(t)
      }, a = this._parse({ data: t, path: n.path, parent: n }), s = await (qr(a) ? a : Promise.resolve(a));
      return ss(n, s);
    }
    refine(t, r) {
      const n = (a) => typeof r == "string" || typeof r > "u" ? { message: r } : typeof r == "function" ? r(a) : r;
      return this._refinement((a, s) => {
        const i = t(a), o = () => s.addIssue({
          code: T.custom,
          ...n(a)
        });
        return typeof Promise < "u" && i instanceof Promise ? i.then((u) => u ? !0 : (o(), !1)) : i ? !0 : (o(), !1);
      });
    }
    refinement(t, r) {
      return this._refinement((n, a) => t(n) ? !0 : (a.addIssue(typeof r == "function" ? r(n, a) : r), !1));
    }
    _refinement(t) {
      return new Ie({
        schema: this,
        typeName: B.ZodEffects,
        effect: { type: "refinement", refinement: t }
      });
    }
    superRefine(t) {
      return this._refinement(t);
    }
    optional() {
      return qe.create(this, this._def);
    }
    nullable() {
      return Et.create(this, this._def);
    }
    nullish() {
      return this.nullable().optional();
    }
    array() {
      return _e.create(this, this._def);
    }
    promise() {
      return zt.create(this, this._def);
    }
    or(t) {
      return ur.create([this, t], this._def);
    }
    and(t) {
      return lr.create(this, t, this._def);
    }
    transform(t) {
      return new Ie({
        ...j(this._def),
        schema: this,
        typeName: B.ZodEffects,
        effect: { type: "transform", transform: t }
      });
    }
    default(t) {
      const r = typeof t == "function" ? t : () => t;
      return new pr({
        ...j(this._def),
        innerType: this,
        defaultValue: r,
        typeName: B.ZodDefault
      });
    }
    brand() {
      return new mi({
        typeName: B.ZodBranded,
        type: this,
        ...j(this._def)
      });
    }
    catch(t) {
      const r = typeof t == "function" ? t : () => t;
      return new Qr({
        ...j(this._def),
        innerType: this,
        catchValue: r,
        typeName: B.ZodCatch
      });
    }
    describe(t) {
      const r = this.constructor;
      return new r({
        ...this._def,
        description: t
      });
    }
    pipe(t) {
      return wr.create(this, t);
    }
    readonly() {
      return Kr.create(this);
    }
    isOptional() {
      return this.safeParse(void 0).success;
    }
    isNullable() {
      return this.safeParse(null).success;
    }
  }
  const th = /^c[^\s-]{8,}$/i, rh = /^[a-z][a-z0-9]*$/, nh = /[0-9A-HJKMNP-TV-Z]{26}/, ah = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i, sh = /^(?!\.)(?!.*\.\.)([A-Z0-9_+-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i, ih = new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$", "u"), oh = /^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/, uh = /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/, lh = (e) => e.precision ? e.offset ? new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${e.precision}}(([+-]\\d{2}(:?\\d{2})?)|Z)$`) : new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${e.precision}}Z$`) : e.precision === 0 ? e.offset ? new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(([+-]\\d{2}(:?\\d{2})?)|Z)$") : new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$") : e.offset ? new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$") : new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$");
  function ch(e, t) {
    return !!((t === "v4" || !t) && oh.test(e) || (t === "v6" || !t) && uh.test(e));
  }
  class Oe extends V {
    constructor() {
      super(...arguments), this._regex = (t, r, n) => this.refinement((a) => t.test(a), {
        validation: r,
        code: T.invalid_string,
        ...D.errToObj(n)
      }), this.nonempty = (t) => this.min(1, D.errToObj(t)), this.trim = () => new Oe({
        ...this._def,
        checks: [...this._def.checks, { kind: "trim" }]
      }), this.toLowerCase = () => new Oe({
        ...this._def,
        checks: [...this._def.checks, { kind: "toLowerCase" }]
      }), this.toUpperCase = () => new Oe({
        ...this._def,
        checks: [...this._def.checks, { kind: "toUpperCase" }]
      });
    }
    _parse(t) {
      if (this._def.coerce && (t.data = String(t.data)), this._getType(t) !== F.string) {
        const s = this._getOrReturnCtx(t);
        return R(
          s,
          {
            code: T.invalid_type,
            expected: F.string,
            received: s.parsedType
          }
          //
        ), z;
      }
      const n = new ye();
      let a;
      for (const s of this._def.checks)
        if (s.kind === "min")
          t.data.length < s.value && (a = this._getOrReturnCtx(t, a), R(a, {
            code: T.too_small,
            minimum: s.value,
            type: "string",
            inclusive: !0,
            exact: !1,
            message: s.message
          }), n.dirty());
        else if (s.kind === "max")
          t.data.length > s.value && (a = this._getOrReturnCtx(t, a), R(a, {
            code: T.too_big,
            maximum: s.value,
            type: "string",
            inclusive: !0,
            exact: !1,
            message: s.message
          }), n.dirty());
        else if (s.kind === "length") {
          const i = t.data.length > s.value, o = t.data.length < s.value;
          (i || o) && (a = this._getOrReturnCtx(t, a), i ? R(a, {
            code: T.too_big,
            maximum: s.value,
            type: "string",
            inclusive: !0,
            exact: !0,
            message: s.message
          }) : o && R(a, {
            code: T.too_small,
            minimum: s.value,
            type: "string",
            inclusive: !0,
            exact: !0,
            message: s.message
          }), n.dirty());
        } else if (s.kind === "email")
          sh.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
            validation: "email",
            code: T.invalid_string,
            message: s.message
          }), n.dirty());
        else if (s.kind === "emoji")
          ih.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
            validation: "emoji",
            code: T.invalid_string,
            message: s.message
          }), n.dirty());
        else if (s.kind === "uuid")
          ah.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
            validation: "uuid",
            code: T.invalid_string,
            message: s.message
          }), n.dirty());
        else if (s.kind === "cuid")
          th.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
            validation: "cuid",
            code: T.invalid_string,
            message: s.message
          }), n.dirty());
        else if (s.kind === "cuid2")
          rh.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
            validation: "cuid2",
            code: T.invalid_string,
            message: s.message
          }), n.dirty());
        else if (s.kind === "ulid")
          nh.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
            validation: "ulid",
            code: T.invalid_string,
            message: s.message
          }), n.dirty());
        else if (s.kind === "url")
          try {
            new URL(t.data);
          } catch {
            a = this._getOrReturnCtx(t, a), R(a, {
              validation: "url",
              code: T.invalid_string,
              message: s.message
            }), n.dirty();
          }
        else s.kind === "regex" ? (s.regex.lastIndex = 0, s.regex.test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
          validation: "regex",
          code: T.invalid_string,
          message: s.message
        }), n.dirty())) : s.kind === "trim" ? t.data = t.data.trim() : s.kind === "includes" ? t.data.includes(s.value, s.position) || (a = this._getOrReturnCtx(t, a), R(a, {
          code: T.invalid_string,
          validation: { includes: s.value, position: s.position },
          message: s.message
        }), n.dirty()) : s.kind === "toLowerCase" ? t.data = t.data.toLowerCase() : s.kind === "toUpperCase" ? t.data = t.data.toUpperCase() : s.kind === "startsWith" ? t.data.startsWith(s.value) || (a = this._getOrReturnCtx(t, a), R(a, {
          code: T.invalid_string,
          validation: { startsWith: s.value },
          message: s.message
        }), n.dirty()) : s.kind === "endsWith" ? t.data.endsWith(s.value) || (a = this._getOrReturnCtx(t, a), R(a, {
          code: T.invalid_string,
          validation: { endsWith: s.value },
          message: s.message
        }), n.dirty()) : s.kind === "datetime" ? lh(s).test(t.data) || (a = this._getOrReturnCtx(t, a), R(a, {
          code: T.invalid_string,
          validation: "datetime",
          message: s.message
        }), n.dirty()) : s.kind === "ip" ? ch(t.data, s.version) || (a = this._getOrReturnCtx(t, a), R(a, {
          validation: "ip",
          code: T.invalid_string,
          message: s.message
        }), n.dirty()) : Y.assertNever(s);
      return { status: n.value, value: t.data };
    }
    _addCheck(t) {
      return new Oe({
        ...this._def,
        checks: [...this._def.checks, t]
      });
    }
    email(t) {
      return this._addCheck({ kind: "email", ...D.errToObj(t) });
    }
    url(t) {
      return this._addCheck({ kind: "url", ...D.errToObj(t) });
    }
    emoji(t) {
      return this._addCheck({ kind: "emoji", ...D.errToObj(t) });
    }
    uuid(t) {
      return this._addCheck({ kind: "uuid", ...D.errToObj(t) });
    }
    cuid(t) {
      return this._addCheck({ kind: "cuid", ...D.errToObj(t) });
    }
    cuid2(t) {
      return this._addCheck({ kind: "cuid2", ...D.errToObj(t) });
    }
    ulid(t) {
      return this._addCheck({ kind: "ulid", ...D.errToObj(t) });
    }
    ip(t) {
      return this._addCheck({ kind: "ip", ...D.errToObj(t) });
    }
    datetime(t) {
      var r;
      return typeof t == "string" ? this._addCheck({
        kind: "datetime",
        precision: null,
        offset: !1,
        message: t
      }) : this._addCheck({
        kind: "datetime",
        precision: typeof (t == null ? void 0 : t.precision) > "u" ? null : t == null ? void 0 : t.precision,
        offset: (r = t == null ? void 0 : t.offset) !== null && r !== void 0 ? r : !1,
        ...D.errToObj(t == null ? void 0 : t.message)
      });
    }
    regex(t, r) {
      return this._addCheck({
        kind: "regex",
        regex: t,
        ...D.errToObj(r)
      });
    }
    includes(t, r) {
      return this._addCheck({
        kind: "includes",
        value: t,
        position: r == null ? void 0 : r.position,
        ...D.errToObj(r == null ? void 0 : r.message)
      });
    }
    startsWith(t, r) {
      return this._addCheck({
        kind: "startsWith",
        value: t,
        ...D.errToObj(r)
      });
    }
    endsWith(t, r) {
      return this._addCheck({
        kind: "endsWith",
        value: t,
        ...D.errToObj(r)
      });
    }
    min(t, r) {
      return this._addCheck({
        kind: "min",
        value: t,
        ...D.errToObj(r)
      });
    }
    max(t, r) {
      return this._addCheck({
        kind: "max",
        value: t,
        ...D.errToObj(r)
      });
    }
    length(t, r) {
      return this._addCheck({
        kind: "length",
        value: t,
        ...D.errToObj(r)
      });
    }
    get isDatetime() {
      return !!this._def.checks.find((t) => t.kind === "datetime");
    }
    get isEmail() {
      return !!this._def.checks.find((t) => t.kind === "email");
    }
    get isURL() {
      return !!this._def.checks.find((t) => t.kind === "url");
    }
    get isEmoji() {
      return !!this._def.checks.find((t) => t.kind === "emoji");
    }
    get isUUID() {
      return !!this._def.checks.find((t) => t.kind === "uuid");
    }
    get isCUID() {
      return !!this._def.checks.find((t) => t.kind === "cuid");
    }
    get isCUID2() {
      return !!this._def.checks.find((t) => t.kind === "cuid2");
    }
    get isULID() {
      return !!this._def.checks.find((t) => t.kind === "ulid");
    }
    get isIP() {
      return !!this._def.checks.find((t) => t.kind === "ip");
    }
    get minLength() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "min" && (t === null || r.value > t) && (t = r.value);
      return t;
    }
    get maxLength() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "max" && (t === null || r.value < t) && (t = r.value);
      return t;
    }
  }
  Oe.create = (e) => {
    var t;
    return new Oe({
      checks: [],
      typeName: B.ZodString,
      coerce: (t = e == null ? void 0 : e.coerce) !== null && t !== void 0 ? t : !1,
      ...j(e)
    });
  };
  function fh(e, t) {
    const r = (e.toString().split(".")[1] || "").length, n = (t.toString().split(".")[1] || "").length, a = r > n ? r : n, s = parseInt(e.toFixed(a).replace(".", "")), i = parseInt(t.toFixed(a).replace(".", ""));
    return s % i / Math.pow(10, a);
  }
  class ot extends V {
    constructor() {
      super(...arguments), this.min = this.gte, this.max = this.lte, this.step = this.multipleOf;
    }
    _parse(t) {
      if (this._def.coerce && (t.data = Number(t.data)), this._getType(t) !== F.number) {
        const s = this._getOrReturnCtx(t);
        return R(s, {
          code: T.invalid_type,
          expected: F.number,
          received: s.parsedType
        }), z;
      }
      let n;
      const a = new ye();
      for (const s of this._def.checks)
        s.kind === "int" ? Y.isInteger(t.data) || (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.invalid_type,
          expected: "integer",
          received: "float",
          message: s.message
        }), a.dirty()) : s.kind === "min" ? (s.inclusive ? t.data < s.value : t.data <= s.value) && (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.too_small,
          minimum: s.value,
          type: "number",
          inclusive: s.inclusive,
          exact: !1,
          message: s.message
        }), a.dirty()) : s.kind === "max" ? (s.inclusive ? t.data > s.value : t.data >= s.value) && (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.too_big,
          maximum: s.value,
          type: "number",
          inclusive: s.inclusive,
          exact: !1,
          message: s.message
        }), a.dirty()) : s.kind === "multipleOf" ? fh(t.data, s.value) !== 0 && (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.not_multiple_of,
          multipleOf: s.value,
          message: s.message
        }), a.dirty()) : s.kind === "finite" ? Number.isFinite(t.data) || (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.not_finite,
          message: s.message
        }), a.dirty()) : Y.assertNever(s);
      return { status: a.value, value: t.data };
    }
    gte(t, r) {
      return this.setLimit("min", t, !0, D.toString(r));
    }
    gt(t, r) {
      return this.setLimit("min", t, !1, D.toString(r));
    }
    lte(t, r) {
      return this.setLimit("max", t, !0, D.toString(r));
    }
    lt(t, r) {
      return this.setLimit("max", t, !1, D.toString(r));
    }
    setLimit(t, r, n, a) {
      return new ot({
        ...this._def,
        checks: [
          ...this._def.checks,
          {
            kind: t,
            value: r,
            inclusive: n,
            message: D.toString(a)
          }
        ]
      });
    }
    _addCheck(t) {
      return new ot({
        ...this._def,
        checks: [...this._def.checks, t]
      });
    }
    int(t) {
      return this._addCheck({
        kind: "int",
        message: D.toString(t)
      });
    }
    positive(t) {
      return this._addCheck({
        kind: "min",
        value: 0,
        inclusive: !1,
        message: D.toString(t)
      });
    }
    negative(t) {
      return this._addCheck({
        kind: "max",
        value: 0,
        inclusive: !1,
        message: D.toString(t)
      });
    }
    nonpositive(t) {
      return this._addCheck({
        kind: "max",
        value: 0,
        inclusive: !0,
        message: D.toString(t)
      });
    }
    nonnegative(t) {
      return this._addCheck({
        kind: "min",
        value: 0,
        inclusive: !0,
        message: D.toString(t)
      });
    }
    multipleOf(t, r) {
      return this._addCheck({
        kind: "multipleOf",
        value: t,
        message: D.toString(r)
      });
    }
    finite(t) {
      return this._addCheck({
        kind: "finite",
        message: D.toString(t)
      });
    }
    safe(t) {
      return this._addCheck({
        kind: "min",
        inclusive: !0,
        value: Number.MIN_SAFE_INTEGER,
        message: D.toString(t)
      })._addCheck({
        kind: "max",
        inclusive: !0,
        value: Number.MAX_SAFE_INTEGER,
        message: D.toString(t)
      });
    }
    get minValue() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "min" && (t === null || r.value > t) && (t = r.value);
      return t;
    }
    get maxValue() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "max" && (t === null || r.value < t) && (t = r.value);
      return t;
    }
    get isInt() {
      return !!this._def.checks.find((t) => t.kind === "int" || t.kind === "multipleOf" && Y.isInteger(t.value));
    }
    get isFinite() {
      let t = null, r = null;
      for (const n of this._def.checks) {
        if (n.kind === "finite" || n.kind === "int" || n.kind === "multipleOf")
          return !0;
        n.kind === "min" ? (r === null || n.value > r) && (r = n.value) : n.kind === "max" && (t === null || n.value < t) && (t = n.value);
      }
      return Number.isFinite(r) && Number.isFinite(t);
    }
  }
  ot.create = (e) => new ot({
    checks: [],
    typeName: B.ZodNumber,
    coerce: (e == null ? void 0 : e.coerce) || !1,
    ...j(e)
  });
  class ut extends V {
    constructor() {
      super(...arguments), this.min = this.gte, this.max = this.lte;
    }
    _parse(t) {
      if (this._def.coerce && (t.data = BigInt(t.data)), this._getType(t) !== F.bigint) {
        const s = this._getOrReturnCtx(t);
        return R(s, {
          code: T.invalid_type,
          expected: F.bigint,
          received: s.parsedType
        }), z;
      }
      let n;
      const a = new ye();
      for (const s of this._def.checks)
        s.kind === "min" ? (s.inclusive ? t.data < s.value : t.data <= s.value) && (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.too_small,
          type: "bigint",
          minimum: s.value,
          inclusive: s.inclusive,
          message: s.message
        }), a.dirty()) : s.kind === "max" ? (s.inclusive ? t.data > s.value : t.data >= s.value) && (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.too_big,
          type: "bigint",
          maximum: s.value,
          inclusive: s.inclusive,
          message: s.message
        }), a.dirty()) : s.kind === "multipleOf" ? t.data % s.value !== BigInt(0) && (n = this._getOrReturnCtx(t, n), R(n, {
          code: T.not_multiple_of,
          multipleOf: s.value,
          message: s.message
        }), a.dirty()) : Y.assertNever(s);
      return { status: a.value, value: t.data };
    }
    gte(t, r) {
      return this.setLimit("min", t, !0, D.toString(r));
    }
    gt(t, r) {
      return this.setLimit("min", t, !1, D.toString(r));
    }
    lte(t, r) {
      return this.setLimit("max", t, !0, D.toString(r));
    }
    lt(t, r) {
      return this.setLimit("max", t, !1, D.toString(r));
    }
    setLimit(t, r, n, a) {
      return new ut({
        ...this._def,
        checks: [
          ...this._def.checks,
          {
            kind: t,
            value: r,
            inclusive: n,
            message: D.toString(a)
          }
        ]
      });
    }
    _addCheck(t) {
      return new ut({
        ...this._def,
        checks: [...this._def.checks, t]
      });
    }
    positive(t) {
      return this._addCheck({
        kind: "min",
        value: BigInt(0),
        inclusive: !1,
        message: D.toString(t)
      });
    }
    negative(t) {
      return this._addCheck({
        kind: "max",
        value: BigInt(0),
        inclusive: !1,
        message: D.toString(t)
      });
    }
    nonpositive(t) {
      return this._addCheck({
        kind: "max",
        value: BigInt(0),
        inclusive: !0,
        message: D.toString(t)
      });
    }
    nonnegative(t) {
      return this._addCheck({
        kind: "min",
        value: BigInt(0),
        inclusive: !0,
        message: D.toString(t)
      });
    }
    multipleOf(t, r) {
      return this._addCheck({
        kind: "multipleOf",
        value: t,
        message: D.toString(r)
      });
    }
    get minValue() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "min" && (t === null || r.value > t) && (t = r.value);
      return t;
    }
    get maxValue() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "max" && (t === null || r.value < t) && (t = r.value);
      return t;
    }
  }
  ut.create = (e) => {
    var t;
    return new ut({
      checks: [],
      typeName: B.ZodBigInt,
      coerce: (t = e == null ? void 0 : e.coerce) !== null && t !== void 0 ? t : !1,
      ...j(e)
    });
  };
  class sr extends V {
    _parse(t) {
      if (this._def.coerce && (t.data = !!t.data), this._getType(t) !== F.boolean) {
        const n = this._getOrReturnCtx(t);
        return R(n, {
          code: T.invalid_type,
          expected: F.boolean,
          received: n.parsedType
        }), z;
      }
      return be(t.data);
    }
  }
  sr.create = (e) => new sr({
    typeName: B.ZodBoolean,
    coerce: (e == null ? void 0 : e.coerce) || !1,
    ...j(e)
  });
  class wt extends V {
    _parse(t) {
      if (this._def.coerce && (t.data = new Date(t.data)), this._getType(t) !== F.date) {
        const s = this._getOrReturnCtx(t);
        return R(s, {
          code: T.invalid_type,
          expected: F.date,
          received: s.parsedType
        }), z;
      }
      if (isNaN(t.data.getTime())) {
        const s = this._getOrReturnCtx(t);
        return R(s, {
          code: T.invalid_date
        }), z;
      }
      const n = new ye();
      let a;
      for (const s of this._def.checks)
        s.kind === "min" ? t.data.getTime() < s.value && (a = this._getOrReturnCtx(t, a), R(a, {
          code: T.too_small,
          message: s.message,
          inclusive: !0,
          exact: !1,
          minimum: s.value,
          type: "date"
        }), n.dirty()) : s.kind === "max" ? t.data.getTime() > s.value && (a = this._getOrReturnCtx(t, a), R(a, {
          code: T.too_big,
          message: s.message,
          inclusive: !0,
          exact: !1,
          maximum: s.value,
          type: "date"
        }), n.dirty()) : Y.assertNever(s);
      return {
        status: n.value,
        value: new Date(t.data.getTime())
      };
    }
    _addCheck(t) {
      return new wt({
        ...this._def,
        checks: [...this._def.checks, t]
      });
    }
    min(t, r) {
      return this._addCheck({
        kind: "min",
        value: t.getTime(),
        message: D.toString(r)
      });
    }
    max(t, r) {
      return this._addCheck({
        kind: "max",
        value: t.getTime(),
        message: D.toString(r)
      });
    }
    get minDate() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "min" && (t === null || r.value > t) && (t = r.value);
      return t != null ? new Date(t) : null;
    }
    get maxDate() {
      let t = null;
      for (const r of this._def.checks)
        r.kind === "max" && (t === null || r.value < t) && (t = r.value);
      return t != null ? new Date(t) : null;
    }
  }
  wt.create = (e) => new wt({
    checks: [],
    coerce: (e == null ? void 0 : e.coerce) || !1,
    typeName: B.ZodDate,
    ...j(e)
  });
  class $r extends V {
    _parse(t) {
      if (this._getType(t) !== F.symbol) {
        const n = this._getOrReturnCtx(t);
        return R(n, {
          code: T.invalid_type,
          expected: F.symbol,
          received: n.parsedType
        }), z;
      }
      return be(t.data);
    }
  }
  $r.create = (e) => new $r({
    typeName: B.ZodSymbol,
    ...j(e)
  });
  class ir extends V {
    _parse(t) {
      if (this._getType(t) !== F.undefined) {
        const n = this._getOrReturnCtx(t);
        return R(n, {
          code: T.invalid_type,
          expected: F.undefined,
          received: n.parsedType
        }), z;
      }
      return be(t.data);
    }
  }
  ir.create = (e) => new ir({
    typeName: B.ZodUndefined,
    ...j(e)
  });
  class or extends V {
    _parse(t) {
      if (this._getType(t) !== F.null) {
        const n = this._getOrReturnCtx(t);
        return R(n, {
          code: T.invalid_type,
          expected: F.null,
          received: n.parsedType
        }), z;
      }
      return be(t.data);
    }
  }
  or.create = (e) => new or({
    typeName: B.ZodNull,
    ...j(e)
  });
  class Wt extends V {
    constructor() {
      super(...arguments), this._any = !0;
    }
    _parse(t) {
      return be(t.data);
    }
  }
  Wt.create = (e) => new Wt({
    typeName: B.ZodAny,
    ...j(e)
  });
  class kt extends V {
    constructor() {
      super(...arguments), this._unknown = !0;
    }
    _parse(t) {
      return be(t.data);
    }
  }
  kt.create = (e) => new kt({
    typeName: B.ZodUnknown,
    ...j(e)
  });
  class $e extends V {
    _parse(t) {
      const r = this._getOrReturnCtx(t);
      return R(r, {
        code: T.invalid_type,
        expected: F.never,
        received: r.parsedType
      }), z;
    }
  }
  $e.create = (e) => new $e({
    typeName: B.ZodNever,
    ...j(e)
  });
  class Xr extends V {
    _parse(t) {
      if (this._getType(t) !== F.undefined) {
        const n = this._getOrReturnCtx(t);
        return R(n, {
          code: T.invalid_type,
          expected: F.void,
          received: n.parsedType
        }), z;
      }
      return be(t.data);
    }
  }
  Xr.create = (e) => new Xr({
    typeName: B.ZodVoid,
    ...j(e)
  });
  class _e extends V {
    _parse(t) {
      const { ctx: r, status: n } = this._processInputParams(t), a = this._def;
      if (r.parsedType !== F.array)
        return R(r, {
          code: T.invalid_type,
          expected: F.array,
          received: r.parsedType
        }), z;
      if (a.exactLength !== null) {
        const i = r.data.length > a.exactLength.value, o = r.data.length < a.exactLength.value;
        (i || o) && (R(r, {
          code: i ? T.too_big : T.too_small,
          minimum: o ? a.exactLength.value : void 0,
          maximum: i ? a.exactLength.value : void 0,
          type: "array",
          inclusive: !0,
          exact: !0,
          message: a.exactLength.message
        }), n.dirty());
      }
      if (a.minLength !== null && r.data.length < a.minLength.value && (R(r, {
        code: T.too_small,
        minimum: a.minLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: a.minLength.message
      }), n.dirty()), a.maxLength !== null && r.data.length > a.maxLength.value && (R(r, {
        code: T.too_big,
        maximum: a.maxLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: a.maxLength.message
      }), n.dirty()), r.common.async)
        return Promise.all([...r.data].map((i, o) => a.type._parseAsync(new ze(r, i, r.path, o)))).then((i) => ye.mergeArray(n, i));
      const s = [...r.data].map((i, o) => a.type._parseSync(new ze(r, i, r.path, o)));
      return ye.mergeArray(n, s);
    }
    get element() {
      return this._def.type;
    }
    min(t, r) {
      return new _e({
        ...this._def,
        minLength: { value: t, message: D.toString(r) }
      });
    }
    max(t, r) {
      return new _e({
        ...this._def,
        maxLength: { value: t, message: D.toString(r) }
      });
    }
    length(t, r) {
      return new _e({
        ...this._def,
        exactLength: { value: t, message: D.toString(r) }
      });
    }
    nonempty(t) {
      return this.min(1, t);
    }
  }
  _e.create = (e, t) => new _e({
    type: e,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: B.ZodArray,
    ...j(t)
  });
  function _t(e) {
    if (e instanceof ue) {
      const t = {};
      for (const r in e.shape) {
        const n = e.shape[r];
        t[r] = qe.create(_t(n));
      }
      return new ue({
        ...e._def,
        shape: () => t
      });
    } else return e instanceof _e ? new _e({
      ...e._def,
      type: _t(e.element)
    }) : e instanceof qe ? qe.create(_t(e.unwrap())) : e instanceof Et ? Et.create(_t(e.unwrap())) : e instanceof je ? je.create(e.items.map((t) => _t(t))) : e;
  }
  class ue extends V {
    constructor() {
      super(...arguments), this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend;
    }
    _getCached() {
      if (this._cached !== null)
        return this._cached;
      const t = this._def.shape(), r = Y.objectKeys(t);
      return this._cached = { shape: t, keys: r };
    }
    _parse(t) {
      if (this._getType(t) !== F.object) {
        const l = this._getOrReturnCtx(t);
        return R(l, {
          code: T.invalid_type,
          expected: F.object,
          received: l.parsedType
        }), z;
      }
      const { status: n, ctx: a } = this._processInputParams(t), { shape: s, keys: i } = this._getCached(), o = [];
      if (!(this._def.catchall instanceof $e && this._def.unknownKeys === "strip"))
        for (const l in a.data)
          i.includes(l) || o.push(l);
      const u = [];
      for (const l of i) {
        const c = s[l], h = a.data[l];
        u.push({
          key: { status: "valid", value: l },
          value: c._parse(new ze(a, h, a.path, l)),
          alwaysSet: l in a.data
        });
      }
      if (this._def.catchall instanceof $e) {
        const l = this._def.unknownKeys;
        if (l === "passthrough")
          for (const c of o)
            u.push({
              key: { status: "valid", value: c },
              value: { status: "valid", value: a.data[c] }
            });
        else if (l === "strict")
          o.length > 0 && (R(a, {
            code: T.unrecognized_keys,
            keys: o
          }), n.dirty());
        else if (l !== "strip") throw new Error("Internal ZodObject error: invalid unknownKeys value.");
      } else {
        const l = this._def.catchall;
        for (const c of o) {
          const h = a.data[c];
          u.push({
            key: { status: "valid", value: c },
            value: l._parse(
              new ze(a, h, a.path, c)
              //, ctx.child(key), value, getParsedType(value)
            ),
            alwaysSet: c in a.data
          });
        }
      }
      return a.common.async ? Promise.resolve().then(async () => {
        const l = [];
        for (const c of u) {
          const h = await c.key;
          l.push({
            key: h,
            value: await c.value,
            alwaysSet: c.alwaysSet
          });
        }
        return l;
      }).then((l) => ye.mergeObjectSync(n, l)) : ye.mergeObjectSync(n, u);
    }
    get shape() {
      return this._def.shape();
    }
    strict(t) {
      return D.errToObj, new ue({
        ...this._def,
        unknownKeys: "strict",
        ...t !== void 0 ? {
          errorMap: (r, n) => {
            var a, s, i, o;
            const u = (i = (s = (a = this._def).errorMap) === null || s === void 0 ? void 0 : s.call(a, r, n).message) !== null && i !== void 0 ? i : n.defaultError;
            return r.code === "unrecognized_keys" ? {
              message: (o = D.errToObj(t).message) !== null && o !== void 0 ? o : u
            } : {
              message: u
            };
          }
        } : {}
      });
    }
    strip() {
      return new ue({
        ...this._def,
        unknownKeys: "strip"
      });
    }
    passthrough() {
      return new ue({
        ...this._def,
        unknownKeys: "passthrough"
      });
    }
    // const AugmentFactory =
    //   <Def extends ZodObjectDef>(def: Def) =>
    //   <Augmentation extends ZodRawShape>(
    //     augmentation: Augmentation
    //   ): ZodObject<
    //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
    //     Def["unknownKeys"],
    //     Def["catchall"]
    //   > => {
    //     return new ZodObject({
    //       ...def,
    //       shape: () => ({
    //         ...def.shape(),
    //         ...augmentation,
    //       }),
    //     }) as any;
    //   };
    extend(t) {
      return new ue({
        ...this._def,
        shape: () => ({
          ...this._def.shape(),
          ...t
        })
      });
    }
    /**
     * Prior to zod@1.0.12 there was a bug in the
     * inferred type of merged objects. Please
     * upgrade if you are experiencing issues.
     */
    merge(t) {
      return new ue({
        unknownKeys: t._def.unknownKeys,
        catchall: t._def.catchall,
        shape: () => ({
          ...this._def.shape(),
          ...t._def.shape()
        }),
        typeName: B.ZodObject
      });
    }
    // merge<
    //   Incoming extends AnyZodObject,
    //   Augmentation extends Incoming["shape"],
    //   NewOutput extends {
    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
    //       ? Augmentation[k]["_output"]
    //       : k extends keyof Output
    //       ? Output[k]
    //       : never;
    //   },
    //   NewInput extends {
    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
    //       ? Augmentation[k]["_input"]
    //       : k extends keyof Input
    //       ? Input[k]
    //       : never;
    //   }
    // >(
    //   merging: Incoming
    // ): ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"],
    //   NewOutput,
    //   NewInput
    // > {
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    setKey(t, r) {
      return this.augment({ [t]: r });
    }
    // merge<Incoming extends AnyZodObject>(
    //   merging: Incoming
    // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
    // ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"]
    // > {
    //   // const mergedShape = objectUtil.mergeShapes(
    //   //   this._def.shape(),
    //   //   merging._def.shape()
    //   // );
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    catchall(t) {
      return new ue({
        ...this._def,
        catchall: t
      });
    }
    pick(t) {
      const r = {};
      return Y.objectKeys(t).forEach((n) => {
        t[n] && this.shape[n] && (r[n] = this.shape[n]);
      }), new ue({
        ...this._def,
        shape: () => r
      });
    }
    omit(t) {
      const r = {};
      return Y.objectKeys(this.shape).forEach((n) => {
        t[n] || (r[n] = this.shape[n]);
      }), new ue({
        ...this._def,
        shape: () => r
      });
    }
    /**
     * @deprecated
     */
    deepPartial() {
      return _t(this);
    }
    partial(t) {
      const r = {};
      return Y.objectKeys(this.shape).forEach((n) => {
        const a = this.shape[n];
        t && !t[n] ? r[n] = a : r[n] = a.optional();
      }), new ue({
        ...this._def,
        shape: () => r
      });
    }
    required(t) {
      const r = {};
      return Y.objectKeys(this.shape).forEach((n) => {
        if (t && !t[n])
          r[n] = this.shape[n];
        else {
          let s = this.shape[n];
          for (; s instanceof qe; )
            s = s._def.innerType;
          r[n] = s;
        }
      }), new ue({
        ...this._def,
        shape: () => r
      });
    }
    keyof() {
      return gi(Y.objectKeys(this.shape));
    }
  }
  ue.create = (e, t) => new ue({
    shape: () => e,
    unknownKeys: "strip",
    catchall: $e.create(),
    typeName: B.ZodObject,
    ...j(t)
  });
  ue.strictCreate = (e, t) => new ue({
    shape: () => e,
    unknownKeys: "strict",
    catchall: $e.create(),
    typeName: B.ZodObject,
    ...j(t)
  });
  ue.lazycreate = (e, t) => new ue({
    shape: e,
    unknownKeys: "strip",
    catchall: $e.create(),
    typeName: B.ZodObject,
    ...j(t)
  });
  class ur extends V {
    _parse(t) {
      const { ctx: r } = this._processInputParams(t), n = this._def.options;
      function a(s) {
        for (const o of s)
          if (o.result.status === "valid")
            return o.result;
        for (const o of s)
          if (o.result.status === "dirty")
            return r.common.issues.push(...o.ctx.common.issues), o.result;
        const i = s.map((o) => new Re(o.ctx.common.issues));
        return R(r, {
          code: T.invalid_union,
          unionErrors: i
        }), z;
      }
      if (r.common.async)
        return Promise.all(n.map(async (s) => {
          const i = {
            ...r,
            common: {
              ...r.common,
              issues: []
            },
            parent: null
          };
          return {
            result: await s._parseAsync({
              data: r.data,
              path: r.path,
              parent: i
            }),
            ctx: i
          };
        })).then(a);
      {
        let s;
        const i = [];
        for (const u of n) {
          const l = {
            ...r,
            common: {
              ...r.common,
              issues: []
            },
            parent: null
          }, c = u._parseSync({
            data: r.data,
            path: r.path,
            parent: l
          });
          if (c.status === "valid")
            return c;
          c.status === "dirty" && !s && (s = { result: c, ctx: l }), l.common.issues.length && i.push(l.common.issues);
        }
        if (s)
          return r.common.issues.push(...s.ctx.common.issues), s.result;
        const o = i.map((u) => new Re(u));
        return R(r, {
          code: T.invalid_union,
          unionErrors: o
        }), z;
      }
    }
    get options() {
      return this._def.options;
    }
  }
  ur.create = (e, t) => new ur({
    options: e,
    typeName: B.ZodUnion,
    ...j(t)
  });
  const Pr = (e) => e instanceof fr ? Pr(e.schema) : e instanceof Ie ? Pr(e.innerType()) : e instanceof hr ? [e.value] : e instanceof lt ? e.options : e instanceof dr ? Object.keys(e.enum) : e instanceof pr ? Pr(e._def.innerType) : e instanceof ir ? [void 0] : e instanceof or ? [null] : null;
  class rn extends V {
    _parse(t) {
      const { ctx: r } = this._processInputParams(t);
      if (r.parsedType !== F.object)
        return R(r, {
          code: T.invalid_type,
          expected: F.object,
          received: r.parsedType
        }), z;
      const n = this.discriminator, a = r.data[n], s = this.optionsMap.get(a);
      return s ? r.common.async ? s._parseAsync({
        data: r.data,
        path: r.path,
        parent: r
      }) : s._parseSync({
        data: r.data,
        path: r.path,
        parent: r
      }) : (R(r, {
        code: T.invalid_union_discriminator,
        options: Array.from(this.optionsMap.keys()),
        path: [n]
      }), z);
    }
    get discriminator() {
      return this._def.discriminator;
    }
    get options() {
      return this._def.options;
    }
    get optionsMap() {
      return this._def.optionsMap;
    }
    /**
     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
     * have a different value for each object in the union.
     * @param discriminator the name of the discriminator property
     * @param types an array of object schemas
     * @param params
     */
    static create(t, r, n) {
      const a = /* @__PURE__ */ new Map();
      for (const s of r) {
        const i = Pr(s.shape[t]);
        if (!i)
          throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);
        for (const o of i) {
          if (a.has(o))
            throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(o)}`);
          a.set(o, s);
        }
      }
      return new rn({
        typeName: B.ZodDiscriminatedUnion,
        discriminator: t,
        options: r,
        optionsMap: a,
        ...j(n)
      });
    }
  }
  function Gn(e, t) {
    const r = Je(e), n = Je(t);
    if (e === t)
      return { valid: !0, data: e };
    if (r === F.object && n === F.object) {
      const a = Y.objectKeys(t), s = Y.objectKeys(e).filter((o) => a.indexOf(o) !== -1), i = { ...e, ...t };
      for (const o of s) {
        const u = Gn(e[o], t[o]);
        if (!u.valid)
          return { valid: !1 };
        i[o] = u.data;
      }
      return { valid: !0, data: i };
    } else if (r === F.array && n === F.array) {
      if (e.length !== t.length)
        return { valid: !1 };
      const a = [];
      for (let s = 0; s < e.length; s++) {
        const i = e[s], o = t[s], u = Gn(i, o);
        if (!u.valid)
          return { valid: !1 };
        a.push(u.data);
      }
      return { valid: !0, data: a };
    } else return r === F.date && n === F.date && +e == +t ? { valid: !0, data: e } : { valid: !1 };
  }
  class lr extends V {
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t), a = (s, i) => {
        if (Nn(s) || Nn(i))
          return z;
        const o = Gn(s.value, i.value);
        return o.valid ? ((Bn(s) || Bn(i)) && r.dirty(), { status: r.value, value: o.data }) : (R(n, {
          code: T.invalid_intersection_types
        }), z);
      };
      return n.common.async ? Promise.all([
        this._def.left._parseAsync({
          data: n.data,
          path: n.path,
          parent: n
        }),
        this._def.right._parseAsync({
          data: n.data,
          path: n.path,
          parent: n
        })
      ]).then(([s, i]) => a(s, i)) : a(this._def.left._parseSync({
        data: n.data,
        path: n.path,
        parent: n
      }), this._def.right._parseSync({
        data: n.data,
        path: n.path,
        parent: n
      }));
    }
  }
  lr.create = (e, t, r) => new lr({
    left: e,
    right: t,
    typeName: B.ZodIntersection,
    ...j(r)
  });
  class je extends V {
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t);
      if (n.parsedType !== F.array)
        return R(n, {
          code: T.invalid_type,
          expected: F.array,
          received: n.parsedType
        }), z;
      if (n.data.length < this._def.items.length)
        return R(n, {
          code: T.too_small,
          minimum: this._def.items.length,
          inclusive: !0,
          exact: !1,
          type: "array"
        }), z;
      !this._def.rest && n.data.length > this._def.items.length && (R(n, {
        code: T.too_big,
        maximum: this._def.items.length,
        inclusive: !0,
        exact: !1,
        type: "array"
      }), r.dirty());
      const s = [...n.data].map((i, o) => {
        const u = this._def.items[o] || this._def.rest;
        return u ? u._parse(new ze(n, i, n.path, o)) : null;
      }).filter((i) => !!i);
      return n.common.async ? Promise.all(s).then((i) => ye.mergeArray(r, i)) : ye.mergeArray(r, s);
    }
    get items() {
      return this._def.items;
    }
    rest(t) {
      return new je({
        ...this._def,
        rest: t
      });
    }
  }
  je.create = (e, t) => {
    if (!Array.isArray(e))
      throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
    return new je({
      items: e,
      typeName: B.ZodTuple,
      rest: null,
      ...j(t)
    });
  };
  class cr extends V {
    get keySchema() {
      return this._def.keyType;
    }
    get valueSchema() {
      return this._def.valueType;
    }
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t);
      if (n.parsedType !== F.object)
        return R(n, {
          code: T.invalid_type,
          expected: F.object,
          received: n.parsedType
        }), z;
      const a = [], s = this._def.keyType, i = this._def.valueType;
      for (const o in n.data)
        a.push({
          key: s._parse(new ze(n, o, n.path, o)),
          value: i._parse(new ze(n, n.data[o], n.path, o))
        });
      return n.common.async ? ye.mergeObjectAsync(r, a) : ye.mergeObjectSync(r, a);
    }
    get element() {
      return this._def.valueType;
    }
    static create(t, r, n) {
      return r instanceof V ? new cr({
        keyType: t,
        valueType: r,
        typeName: B.ZodRecord,
        ...j(n)
      }) : new cr({
        keyType: Oe.create(),
        valueType: t,
        typeName: B.ZodRecord,
        ...j(r)
      });
    }
  }
  class Yr extends V {
    get keySchema() {
      return this._def.keyType;
    }
    get valueSchema() {
      return this._def.valueType;
    }
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t);
      if (n.parsedType !== F.map)
        return R(n, {
          code: T.invalid_type,
          expected: F.map,
          received: n.parsedType
        }), z;
      const a = this._def.keyType, s = this._def.valueType, i = [...n.data.entries()].map(([o, u], l) => ({
        key: a._parse(new ze(n, o, n.path, [l, "key"])),
        value: s._parse(new ze(n, u, n.path, [l, "value"]))
      }));
      if (n.common.async) {
        const o = /* @__PURE__ */ new Map();
        return Promise.resolve().then(async () => {
          for (const u of i) {
            const l = await u.key, c = await u.value;
            if (l.status === "aborted" || c.status === "aborted")
              return z;
            (l.status === "dirty" || c.status === "dirty") && r.dirty(), o.set(l.value, c.value);
          }
          return { status: r.value, value: o };
        });
      } else {
        const o = /* @__PURE__ */ new Map();
        for (const u of i) {
          const l = u.key, c = u.value;
          if (l.status === "aborted" || c.status === "aborted")
            return z;
          (l.status === "dirty" || c.status === "dirty") && r.dirty(), o.set(l.value, c.value);
        }
        return { status: r.value, value: o };
      }
    }
  }
  Yr.create = (e, t, r) => new Yr({
    valueType: t,
    keyType: e,
    typeName: B.ZodMap,
    ...j(r)
  });
  class Ct extends V {
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t);
      if (n.parsedType !== F.set)
        return R(n, {
          code: T.invalid_type,
          expected: F.set,
          received: n.parsedType
        }), z;
      const a = this._def;
      a.minSize !== null && n.data.size < a.minSize.value && (R(n, {
        code: T.too_small,
        minimum: a.minSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: a.minSize.message
      }), r.dirty()), a.maxSize !== null && n.data.size > a.maxSize.value && (R(n, {
        code: T.too_big,
        maximum: a.maxSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: a.maxSize.message
      }), r.dirty());
      const s = this._def.valueType;
      function i(u) {
        const l = /* @__PURE__ */ new Set();
        for (const c of u) {
          if (c.status === "aborted")
            return z;
          c.status === "dirty" && r.dirty(), l.add(c.value);
        }
        return { status: r.value, value: l };
      }
      const o = [...n.data.values()].map((u, l) => s._parse(new ze(n, u, n.path, l)));
      return n.common.async ? Promise.all(o).then((u) => i(u)) : i(o);
    }
    min(t, r) {
      return new Ct({
        ...this._def,
        minSize: { value: t, message: D.toString(r) }
      });
    }
    max(t, r) {
      return new Ct({
        ...this._def,
        maxSize: { value: t, message: D.toString(r) }
      });
    }
    size(t, r) {
      return this.min(t, r).max(t, r);
    }
    nonempty(t) {
      return this.min(1, t);
    }
  }
  Ct.create = (e, t) => new Ct({
    valueType: e,
    minSize: null,
    maxSize: null,
    typeName: B.ZodSet,
    ...j(t)
  });
  class Lt extends V {
    constructor() {
      super(...arguments), this.validate = this.implement;
    }
    _parse(t) {
      const { ctx: r } = this._processInputParams(t);
      if (r.parsedType !== F.function)
        return R(r, {
          code: T.invalid_type,
          expected: F.function,
          received: r.parsedType
        }), z;
      function n(o, u) {
        return Vr({
          data: o,
          path: r.path,
          errorMaps: [
            r.common.contextualErrorMap,
            r.schemaErrorMap,
            Zr(),
            nr
          ].filter((l) => !!l),
          issueData: {
            code: T.invalid_arguments,
            argumentsError: u
          }
        });
      }
      function a(o, u) {
        return Vr({
          data: o,
          path: r.path,
          errorMaps: [
            r.common.contextualErrorMap,
            r.schemaErrorMap,
            Zr(),
            nr
          ].filter((l) => !!l),
          issueData: {
            code: T.invalid_return_type,
            returnTypeError: u
          }
        });
      }
      const s = { errorMap: r.common.contextualErrorMap }, i = r.data;
      if (this._def.returns instanceof zt) {
        const o = this;
        return be(async function(...u) {
          const l = new Re([]), c = await o._def.args.parseAsync(u, s).catch((d) => {
            throw l.addIssue(n(u, d)), l;
          }), h = await Reflect.apply(i, this, c);
          return await o._def.returns._def.type.parseAsync(h, s).catch((d) => {
            throw l.addIssue(a(h, d)), l;
          });
        });
      } else {
        const o = this;
        return be(function(...u) {
          const l = o._def.args.safeParse(u, s);
          if (!l.success)
            throw new Re([n(u, l.error)]);
          const c = Reflect.apply(i, this, l.data), h = o._def.returns.safeParse(c, s);
          if (!h.success)
            throw new Re([a(c, h.error)]);
          return h.data;
        });
      }
    }
    parameters() {
      return this._def.args;
    }
    returnType() {
      return this._def.returns;
    }
    args(...t) {
      return new Lt({
        ...this._def,
        args: je.create(t).rest(kt.create())
      });
    }
    returns(t) {
      return new Lt({
        ...this._def,
        returns: t
      });
    }
    implement(t) {
      return this.parse(t);
    }
    strictImplement(t) {
      return this.parse(t);
    }
    static create(t, r, n) {
      return new Lt({
        args: t || je.create([]).rest(kt.create()),
        returns: r || kt.create(),
        typeName: B.ZodFunction,
        ...j(n)
      });
    }
  }
  class fr extends V {
    get schema() {
      return this._def.getter();
    }
    _parse(t) {
      const { ctx: r } = this._processInputParams(t);
      return this._def.getter()._parse({ data: r.data, path: r.path, parent: r });
    }
  }
  fr.create = (e, t) => new fr({
    getter: e,
    typeName: B.ZodLazy,
    ...j(t)
  });
  class hr extends V {
    _parse(t) {
      if (t.data !== this._def.value) {
        const r = this._getOrReturnCtx(t);
        return R(r, {
          received: r.data,
          code: T.invalid_literal,
          expected: this._def.value
        }), z;
      }
      return { status: "valid", value: t.data };
    }
    get value() {
      return this._def.value;
    }
  }
  hr.create = (e, t) => new hr({
    value: e,
    typeName: B.ZodLiteral,
    ...j(t)
  });
  function gi(e, t) {
    return new lt({
      values: e,
      typeName: B.ZodEnum,
      ...j(t)
    });
  }
  class lt extends V {
    _parse(t) {
      if (typeof t.data != "string") {
        const r = this._getOrReturnCtx(t), n = this._def.values;
        return R(r, {
          expected: Y.joinValues(n),
          received: r.parsedType,
          code: T.invalid_type
        }), z;
      }
      if (this._def.values.indexOf(t.data) === -1) {
        const r = this._getOrReturnCtx(t), n = this._def.values;
        return R(r, {
          received: r.data,
          code: T.invalid_enum_value,
          options: n
        }), z;
      }
      return be(t.data);
    }
    get options() {
      return this._def.values;
    }
    get enum() {
      const t = {};
      for (const r of this._def.values)
        t[r] = r;
      return t;
    }
    get Values() {
      const t = {};
      for (const r of this._def.values)
        t[r] = r;
      return t;
    }
    get Enum() {
      const t = {};
      for (const r of this._def.values)
        t[r] = r;
      return t;
    }
    extract(t) {
      return lt.create(t);
    }
    exclude(t) {
      return lt.create(this.options.filter((r) => !t.includes(r)));
    }
  }
  lt.create = gi;
  class dr extends V {
    _parse(t) {
      const r = Y.getValidEnumValues(this._def.values), n = this._getOrReturnCtx(t);
      if (n.parsedType !== F.string && n.parsedType !== F.number) {
        const a = Y.objectValues(r);
        return R(n, {
          expected: Y.joinValues(a),
          received: n.parsedType,
          code: T.invalid_type
        }), z;
      }
      if (r.indexOf(t.data) === -1) {
        const a = Y.objectValues(r);
        return R(n, {
          received: n.data,
          code: T.invalid_enum_value,
          options: a
        }), z;
      }
      return be(t.data);
    }
    get enum() {
      return this._def.values;
    }
  }
  dr.create = (e, t) => new dr({
    values: e,
    typeName: B.ZodNativeEnum,
    ...j(t)
  });
  class zt extends V {
    unwrap() {
      return this._def.type;
    }
    _parse(t) {
      const { ctx: r } = this._processInputParams(t);
      if (r.parsedType !== F.promise && r.common.async === !1)
        return R(r, {
          code: T.invalid_type,
          expected: F.promise,
          received: r.parsedType
        }), z;
      const n = r.parsedType === F.promise ? r.data : Promise.resolve(r.data);
      return be(n.then((a) => this._def.type.parseAsync(a, {
        path: r.path,
        errorMap: r.common.contextualErrorMap
      })));
    }
  }
  zt.create = (e, t) => new zt({
    type: e,
    typeName: B.ZodPromise,
    ...j(t)
  });
  class Ie extends V {
    innerType() {
      return this._def.schema;
    }
    sourceType() {
      return this._def.schema._def.typeName === B.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
    }
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t), a = this._def.effect || null, s = {
        addIssue: (i) => {
          R(n, i), i.fatal ? r.abort() : r.dirty();
        },
        get path() {
          return n.path;
        }
      };
      if (s.addIssue = s.addIssue.bind(s), a.type === "preprocess") {
        const i = a.transform(n.data, s);
        return n.common.issues.length ? {
          status: "dirty",
          value: n.data
        } : n.common.async ? Promise.resolve(i).then((o) => this._def.schema._parseAsync({
          data: o,
          path: n.path,
          parent: n
        })) : this._def.schema._parseSync({
          data: i,
          path: n.path,
          parent: n
        });
      }
      if (a.type === "refinement") {
        const i = (o) => {
          const u = a.refinement(o, s);
          if (n.common.async)
            return Promise.resolve(u);
          if (u instanceof Promise)
            throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
          return o;
        };
        if (n.common.async === !1) {
          const o = this._def.schema._parseSync({
            data: n.data,
            path: n.path,
            parent: n
          });
          return o.status === "aborted" ? z : (o.status === "dirty" && r.dirty(), i(o.value), { status: r.value, value: o.value });
        } else
          return this._def.schema._parseAsync({ data: n.data, path: n.path, parent: n }).then((o) => o.status === "aborted" ? z : (o.status === "dirty" && r.dirty(), i(o.value).then(() => ({ status: r.value, value: o.value }))));
      }
      if (a.type === "transform")
        if (n.common.async === !1) {
          const i = this._def.schema._parseSync({
            data: n.data,
            path: n.path,
            parent: n
          });
          if (!ar(i))
            return i;
          const o = a.transform(i.value, s);
          if (o instanceof Promise)
            throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
          return { status: r.value, value: o };
        } else
          return this._def.schema._parseAsync({ data: n.data, path: n.path, parent: n }).then((i) => ar(i) ? Promise.resolve(a.transform(i.value, s)).then((o) => ({ status: r.value, value: o })) : i);
      Y.assertNever(a);
    }
  }
  Ie.create = (e, t, r) => new Ie({
    schema: e,
    typeName: B.ZodEffects,
    effect: t,
    ...j(r)
  });
  Ie.createWithPreprocess = (e, t, r) => new Ie({
    schema: t,
    effect: { type: "preprocess", transform: e },
    typeName: B.ZodEffects,
    ...j(r)
  });
  class qe extends V {
    _parse(t) {
      return this._getType(t) === F.undefined ? be(void 0) : this._def.innerType._parse(t);
    }
    unwrap() {
      return this._def.innerType;
    }
  }
  qe.create = (e, t) => new qe({
    innerType: e,
    typeName: B.ZodOptional,
    ...j(t)
  });
  class Et extends V {
    _parse(t) {
      return this._getType(t) === F.null ? be(null) : this._def.innerType._parse(t);
    }
    unwrap() {
      return this._def.innerType;
    }
  }
  Et.create = (e, t) => new Et({
    innerType: e,
    typeName: B.ZodNullable,
    ...j(t)
  });
  class pr extends V {
    _parse(t) {
      const { ctx: r } = this._processInputParams(t);
      let n = r.data;
      return r.parsedType === F.undefined && (n = this._def.defaultValue()), this._def.innerType._parse({
        data: n,
        path: r.path,
        parent: r
      });
    }
    removeDefault() {
      return this._def.innerType;
    }
  }
  pr.create = (e, t) => new pr({
    innerType: e,
    typeName: B.ZodDefault,
    defaultValue: typeof t.default == "function" ? t.default : () => t.default,
    ...j(t)
  });
  class Qr extends V {
    _parse(t) {
      const { ctx: r } = this._processInputParams(t), n = {
        ...r,
        common: {
          ...r.common,
          issues: []
        }
      }, a = this._def.innerType._parse({
        data: n.data,
        path: n.path,
        parent: {
          ...n
        }
      });
      return qr(a) ? a.then((s) => ({
        status: "valid",
        value: s.status === "valid" ? s.value : this._def.catchValue({
          get error() {
            return new Re(n.common.issues);
          },
          input: n.data
        })
      })) : {
        status: "valid",
        value: a.status === "valid" ? a.value : this._def.catchValue({
          get error() {
            return new Re(n.common.issues);
          },
          input: n.data
        })
      };
    }
    removeCatch() {
      return this._def.innerType;
    }
  }
  Qr.create = (e, t) => new Qr({
    innerType: e,
    typeName: B.ZodCatch,
    catchValue: typeof t.catch == "function" ? t.catch : () => t.catch,
    ...j(t)
  });
  class Jr extends V {
    _parse(t) {
      if (this._getType(t) !== F.nan) {
        const n = this._getOrReturnCtx(t);
        return R(n, {
          code: T.invalid_type,
          expected: F.nan,
          received: n.parsedType
        }), z;
      }
      return { status: "valid", value: t.data };
    }
  }
  Jr.create = (e) => new Jr({
    typeName: B.ZodNaN,
    ...j(e)
  });
  const hh = Symbol("zod_brand");
  class mi extends V {
    _parse(t) {
      const { ctx: r } = this._processInputParams(t), n = r.data;
      return this._def.type._parse({
        data: n,
        path: r.path,
        parent: r
      });
    }
    unwrap() {
      return this._def.type;
    }
  }
  class wr extends V {
    _parse(t) {
      const { status: r, ctx: n } = this._processInputParams(t);
      if (n.common.async)
        return (async () => {
          const s = await this._def.in._parseAsync({
            data: n.data,
            path: n.path,
            parent: n
          });
          return s.status === "aborted" ? z : s.status === "dirty" ? (r.dirty(), vi(s.value)) : this._def.out._parseAsync({
            data: s.value,
            path: n.path,
            parent: n
          });
        })();
      {
        const a = this._def.in._parseSync({
          data: n.data,
          path: n.path,
          parent: n
        });
        return a.status === "aborted" ? z : a.status === "dirty" ? (r.dirty(), {
          status: "dirty",
          value: a.value
        }) : this._def.out._parseSync({
          data: a.value,
          path: n.path,
          parent: n
        });
      }
    }
    static create(t, r) {
      return new wr({
        in: t,
        out: r,
        typeName: B.ZodPipeline
      });
    }
  }
  class Kr extends V {
    _parse(t) {
      const r = this._def.innerType._parse(t);
      return ar(r) && (r.value = Object.freeze(r.value)), r;
    }
  }
  Kr.create = (e, t) => new Kr({
    innerType: e,
    typeName: B.ZodReadonly,
    ...j(t)
  });
  const yi = (e, t = {}, r) => e ? Wt.create().superRefine((n, a) => {
    var s, i;
    if (!e(n)) {
      const o = typeof t == "function" ? t(n) : typeof t == "string" ? { message: t } : t, u = (i = (s = o.fatal) !== null && s !== void 0 ? s : r) !== null && i !== void 0 ? i : !0, l = typeof o == "string" ? { message: o } : o;
      a.addIssue({ code: "custom", ...l, fatal: u });
    }
  }) : Wt.create(), dh = {
    object: ue.lazycreate
  };
  var B;
  (function(e) {
    e.ZodString = "ZodString", e.ZodNumber = "ZodNumber", e.ZodNaN = "ZodNaN", e.ZodBigInt = "ZodBigInt", e.ZodBoolean = "ZodBoolean", e.ZodDate = "ZodDate", e.ZodSymbol = "ZodSymbol", e.ZodUndefined = "ZodUndefined", e.ZodNull = "ZodNull", e.ZodAny = "ZodAny", e.ZodUnknown = "ZodUnknown", e.ZodNever = "ZodNever", e.ZodVoid = "ZodVoid", e.ZodArray = "ZodArray", e.ZodObject = "ZodObject", e.ZodUnion = "ZodUnion", e.ZodDiscriminatedUnion = "ZodDiscriminatedUnion", e.ZodIntersection = "ZodIntersection", e.ZodTuple = "ZodTuple", e.ZodRecord = "ZodRecord", e.ZodMap = "ZodMap", e.ZodSet = "ZodSet", e.ZodFunction = "ZodFunction", e.ZodLazy = "ZodLazy", e.ZodLiteral = "ZodLiteral", e.ZodEnum = "ZodEnum", e.ZodEffects = "ZodEffects", e.ZodNativeEnum = "ZodNativeEnum", e.ZodOptional = "ZodOptional", e.ZodNullable = "ZodNullable", e.ZodDefault = "ZodDefault", e.ZodCatch = "ZodCatch", e.ZodPromise = "ZodPromise", e.ZodBranded = "ZodBranded", e.ZodPipeline = "ZodPipeline", e.ZodReadonly = "ZodReadonly";
  })(B || (B = {}));
  const ph = (e, t = {
    message: `Input not instance of ${e.name}`
  }) => yi((r) => r instanceof e, t), xi = Oe.create, bi = ot.create, vh = Jr.create, gh = ut.create, Si = sr.create, mh = wt.create, yh = $r.create, xh = ir.create, bh = or.create, Sh = Wt.create, kh = kt.create, Th = $e.create, wh = Xr.create, Ch = _e.create, Eh = ue.create, Fh = ue.strictCreate, Oh = ur.create, Rh = rn.create, _h = lr.create, Uh = je.create, Lh = cr.create, Ah = Yr.create, Ih = Ct.create, Dh = Lt.create, Ph = fr.create, Mh = hr.create, Nh = lt.create, Bh = dr.create, Gh = zt.create, is = Ie.create, Hh = qe.create, Wh = Et.create, zh = Ie.createWithPreprocess, jh = wr.create, Zh = () => xi().optional(), Vh = () => bi().optional(), qh = () => Si().optional(), $h = {
    string: (e) => Oe.create({ ...e, coerce: !0 }),
    number: (e) => ot.create({ ...e, coerce: !0 }),
    boolean: (e) => sr.create({
      ...e,
      coerce: !0
    }),
    bigint: (e) => ut.create({ ...e, coerce: !0 }),
    date: (e) => wt.create({ ...e, coerce: !0 })
  }, Xh = z;
  var ke = /* @__PURE__ */ Object.freeze({
    __proto__: null,
    defaultErrorMap: nr,
    setErrorMap: Kf,
    getErrorMap: Zr,
    makeIssue: Vr,
    EMPTY_PATH: eh,
    addIssueToContext: R,
    ParseStatus: ye,
    INVALID: z,
    DIRTY: vi,
    OK: be,
    isAborted: Nn,
    isDirty: Bn,
    isValid: ar,
    isAsync: qr,
    get util() {
      return Y;
    },
    get objectUtil() {
      return Mn;
    },
    ZodParsedType: F,
    getParsedType: Je,
    ZodType: V,
    ZodString: Oe,
    ZodNumber: ot,
    ZodBigInt: ut,
    ZodBoolean: sr,
    ZodDate: wt,
    ZodSymbol: $r,
    ZodUndefined: ir,
    ZodNull: or,
    ZodAny: Wt,
    ZodUnknown: kt,
    ZodNever: $e,
    ZodVoid: Xr,
    ZodArray: _e,
    ZodObject: ue,
    ZodUnion: ur,
    ZodDiscriminatedUnion: rn,
    ZodIntersection: lr,
    ZodTuple: je,
    ZodRecord: cr,
    ZodMap: Yr,
    ZodSet: Ct,
    ZodFunction: Lt,
    ZodLazy: fr,
    ZodLiteral: hr,
    ZodEnum: lt,
    ZodNativeEnum: dr,
    ZodPromise: zt,
    ZodEffects: Ie,
    ZodTransformer: Ie,
    ZodOptional: qe,
    ZodNullable: Et,
    ZodDefault: pr,
    ZodCatch: Qr,
    ZodNaN: Jr,
    BRAND: hh,
    ZodBranded: mi,
    ZodPipeline: wr,
    ZodReadonly: Kr,
    custom: yi,
    Schema: V,
    ZodSchema: V,
    late: dh,
    get ZodFirstPartyTypeKind() {
      return B;
    },
    coerce: $h,
    any: Sh,
    array: Ch,
    bigint: gh,
    boolean: Si,
    date: mh,
    discriminatedUnion: Rh,
    effect: is,
    enum: Nh,
    function: Dh,
    instanceof: ph,
    intersection: _h,
    lazy: Ph,
    literal: Mh,
    map: Ah,
    nan: vh,
    nativeEnum: Bh,
    never: Th,
    null: bh,
    nullable: Wh,
    number: bi,
    object: Eh,
    oboolean: qh,
    onumber: Vh,
    optional: Hh,
    ostring: Zh,
    pipeline: jh,
    preprocess: zh,
    promise: Gh,
    record: Lh,
    set: Ih,
    strictObject: Fh,
    string: xi,
    symbol: yh,
    transformer: is,
    tuple: Uh,
    undefined: xh,
    union: Oh,
    unknown: kh,
    void: wh,
    NEVER: Xh,
    ZodIssueCode: T,
    quotelessJson: Jf,
    ZodError: Re
  }), gt = /* @__PURE__ */ ((e) => (e.STORYBOARD = "storyboard", e.TEXT = "text", e.VIDEO = "video", e.SOUND = "sound", e.NARRATION = "NARRATION", e.CAPTION = "caption", e.STICKER = "sticker", e.material = "material", e))(gt || {});
  const Yh = ke.object({
    overlays: ke.array(ke.any()),
    // Replace with your actual Overlay type
    durationInFrames: ke.number(),
    width: ke.number(),
    height: ke.number(),
    fps: ke.number(),
    src: ke.string()
  });
  ke.object({
    id: ke.string(),
    inputProps: Yh
  });
  ke.object({
    bucketName: ke.string(),
    id: ke.string()
  });
  const Lr = {
    width: "100%",
    height: "100%"
  }, os = ({
    overlay: e,
    baseUrl: t
  }) => {
    switch (e.type) {
      case gt.VIDEO:
        return /* @__PURE__ */ N.jsx("div", { style: { ...Lr }, children: /* @__PURE__ */ N.jsx(Qf, { overlay: e, baseUrl: t }) });
      case gt.TEXT:
        return /* @__PURE__ */ N.jsx("div", { style: { ...Lr }, children: /* @__PURE__ */ N.jsx(Yf, { overlay: e }) });
      case gt.CAPTION:
        return /* @__PURE__ */ N.jsx(
          "div",
          {
            style: {
              ...Lr,
              position: "relative",
              overflow: "hidden",
              display: "flex"
            },
            children: /* @__PURE__ */ N.jsx(Pi, { overlay: e })
          }
        );
      case gt.STICKER:
        return /* @__PURE__ */ N.jsx("div", { style: { ...Lr }, className: "layer-content-wrapper", children: /* @__PURE__ */ N.jsx(Gi, { overlay: e }) });
      case gt.SOUND:
        return /* @__PURE__ */ N.jsx(Mi, { overlay: e, baseUrl: t });
      default:
        return null;
    }
  }, Qh = ({ overlay: e, baseUrl: t }) => {
    const r = vn(() => ({
      position: "absolute",
      left: e.left,
      top: e.top,
      width: e.width,
      height: e.height,
      transform: `rotate(${e.rotation || 0}deg)`,
      transformOrigin: "center center",
      zIndex: e.zIndex
    }), [e]);
    return e.type === gt.STORYBOARD ? null : e.type === "sound" ? /* @__PURE__ */ N.jsx(
      na,
      {
        from: e.from,
        durationInFrames: e.durationInFrames,
        children: /* @__PURE__ */ N.jsx(os, { overlay: e, baseUrl: t })
      },
      e.id
    ) : /* @__PURE__ */ N.jsx(Oi, { children: /* @__PURE__ */ N.jsx(
      gs,
      {
        style: {
          overflow: "hidden",
          maxWidth: "3000px"
        },
        children: /* @__PURE__ */ N.jsx(
          na,
          {
            from: e.from,
            durationInFrames: e.durationInFrames,
            layout: "none",
            children: /* @__PURE__ */ N.jsx("div", { style: r, children: /* @__PURE__ */ N.jsx(os, { overlay: e, baseUrl: t }) })
          },
          e.id
        )
      }
    ) });
  }, Jh = ({
    overlays: e,
    baseUrl: t,
    playerMetadata: r
  }) => {
    if (!r)
      throw new Error("Unable to load player metadata");
    return /* @__PURE__ */ N.jsx(
      ms.Provider,
      {
        value: {
          overlays: e,
          baseUrl: t,
          playerMetadata: r
        },
        children: /* @__PURE__ */ N.jsx(gs, { style: { backgroundColor: "#000000" }, children: e.map((n) => /* @__PURE__ */ N.jsx(
          Qh,
          {
            overlay: n,
            baseUrl: t
          },
          n.id
        )) })
      }
    );
  }, us = 0, ls = 30, Kh = "V0-0-1", ed = () => {
    const e = {
      overlays: [],
      durationInFrames: us,
      fps: ls,
      width: 1920,
      height: 1920,
      src: "",
      setSelectedOverlayId: () => {
      },
      selectedOverlayId: null,
      changeOverlay: () => {
      }
    };
    return /* @__PURE__ */ N.jsx(N.Fragment, { children: /* @__PURE__ */ N.jsx(
      Li,
      {
        id: Kh,
        component: Jh,
        durationInFrames: us,
        fps: ls,
        width: 1920,
        height: 1920,
        calculateMetadata: async ({ props: t }) => {
          const { durationInFrames: r, width: n, height: a } = t.playerMetadata;
          return {
            durationInFrames: r,
            width: n,
            height: a
          };
        },
        defaultProps: e
      }
    ) });
  };
  Ui(ed);
});
export default td();
