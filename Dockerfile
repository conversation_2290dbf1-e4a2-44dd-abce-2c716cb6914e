# ================================
# 构建阶段
# ================================
FROM node:22-bookworm-slim AS builder

# 安装构建时需要的系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3 \
    make \
    g++ \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 package 文件并安装所有依赖（包括 devDependencies）
COPY package.json package-lock.json* ./
RUN npm ci --include=dev

# 复制源代码和配置文件
COPY . .

# 构建 NestJS 应用
RUN npm run build

# ================================
# 生产运行阶段
# ================================
FROM node:22-bookworm-slim AS production

# 安装 Chrome 和运行时依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libnss3 \
    libdbus-1-3 \
    libatk1.0-0 \
    libgbm-dev \
    libasound2 \
    libxrandr2 \
    libxkbcommon-dev \
    libxfixes3 \
    libxcomposite1 \
    libxdamage1 \
    libatk-bridge2.0-0 \
    libpango-1.0-0 \
    libcairo2 \
    libcups2 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 package 文件并只安装生产依赖
COPY package.json package-lock.json* ./
RUN npm ci --only=production && npm cache clean --force

# 从构建阶段复制构建产物
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public

# 复制其他必要的文件
COPY --from=builder /app/packages ./packages
COPY --from=builder /app/lib ./lib
COPY --from=builder /app/utils ./utils

# 安装 Chrome（Remotion 渲染用）
RUN npx remotion browser ensure

# 创建非 root 用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 切换到 root（默认是 root）
USER root

# 创建目录并赋权
RUN mkdir -p /home/<USER>/logs/rocketmq && \
    chown -R appuser:appuser /home/<USER>/logs

# 切回 appuser
USER appuser

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production

# 启动应用（使用构建后的 NestJS 应用）
CMD ["node", "dist/main.js"]


