name: Docker Image CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Build the Docker image
      run: |
        docker login registry.cn-hangzhou.aliyuncs.com -u ali_zhijunsoh -p dev{~Zu73G,b#
        docker build . --file Dockerfile --tag registry.cn-hangzhou.aliyuncs.com/gaoling_test/clipnest-render:latest
        docker push registry.cn-hangzhou.aliyuncs.com/gaoling_test/clipnest-render:latest

