// 这个文件包装了 Remotion 功能，避免在构建时的静态分析问题
import path from "path";
import fs from "fs";

// 动态导入 Remotion 模块以避免构建时的静态分析
export async function createRemotionBundle(compositionId: string) {
  const { bundle } = await import("@remotion/bundler");

  const entryPath = path.join(
    process.cwd(),
    "packages",
    "overlay-renderer",
    compositionId,
    "entry.js"
  );

  // 检查入口文件是否存在
  if (!fs.existsSync(entryPath)) {
    throw new Error(`Remotion entry file not found: ${entryPath}`);
  }

  return bundle(entryPath, undefined, {
    // Disable all platform-specific compositors
    webpackOverride: (config) => ({
      ...config,
      resolve: {
        ...config.resolve,
        fallback: {
          ...config.resolve?.fallback,
          // Explicitly disable ALL compositor packages
          "@remotion/compositor": false,
          "@remotion/compositor-darwin-arm64": false,
          "@remotion/compositor-darwin-x64": false,
          "@remotion/compositor-linux-x64": false,
          "@remotion/compositor-linux-arm64": false,
          "@remotion/compositor-win32-x64-msvc": false,
          "@remotion/compositor-windows-x64": false,
        },
      },
    }),
  });
}

export async function selectRemotionComposition(serveUrl: string, id: string, inputProps: Record<string, unknown>) {
  const { selectComposition } = await import("@remotion/renderer");
  return selectComposition({
    serveUrl,
    id,
    inputProps,
  });
}

export async function renderRemotionMedia(options: any) {
  const { renderMedia } = await import("@remotion/renderer");
  return renderMedia(options);
}
