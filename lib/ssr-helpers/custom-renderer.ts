import {RenderMediaOnProgress} from "@remotion/renderer";
import {createRemotionBundle, selectRemotionComposition, renderRemotionMedia} from "./remotion-wrapper";
import path from "path";
import fs from "fs";
import {v4 as uuidv4} from "uuid";
import {
    saveRenderState,
    updateRenderProgress,
    completeRender,
    failRender,
} from "./render-state";
import {sendMessage} from "@/utils/message/mq-sender";
import {uploadToOSS} from "@/utils/oss/oss-uploader";
import {getBaseUrl} from "../url-helper";

// Ensure the videos directory exists
const VIDEOS_DIR = path.join(process.cwd(), "output", "rendered-videos");
if (!fs.existsSync(VIDEOS_DIR)) {
    fs.mkdirSync(VIDEOS_DIR, {recursive: true});
}

// Track rendering progress
export const renderProgress = new Map<string, number>();
export const renderStatus = new Map<string, "rendering" | "done" | "error">();
export const renderErrors = new Map<string, string>();
export const renderUrls = new Map<string, string>();
export const renderSizes = new Map<string, number>();

/**
 * Custom renderer that uses browser-based rendering to avoid platform-specific dependencies
 */
export async function startRendering(
    compositionId: string,
    inputProps: Record<string, unknown>,
    taskId: string,
    taskName: string,
    userId: string,
    tenantId: string,
    tenantName: string,
    uploadUrl: string,
    token: string,
) {
    const renderId = taskId || uuidv4();

    // Initialize render state
    saveRenderState(renderId, {
        status: "rendering",
        progress: 0,
        timestamp: Date.now(),
    });

    // Start rendering asynchronously
    void (async () => {
        try {
            // Update progress as rendering proceeds
            updateRenderProgress(renderId, 0);

            // Get the base URL for serving media files
            const baseUrl = getBaseUrl();

            // Bundle the video
            const bundleLocation = await createRemotionBundle(compositionId);

            // Select the composition
            const composition = await selectRemotionComposition(
                bundleLocation,
                compositionId,
                {
                    ...inputProps,
                    // Pass the base URL to the composition for media file access
                    baseUrl,
                }
            );

            // Get the actual duration from inputProps or use composition's duration
            const actualDurationInFrames =
                (inputProps.durationInFrames as number) || composition.durationInFrames;
            console.log(`${tenantName} ${taskName} Using actual duration: ${actualDurationInFrames} frames`);

            // Render the video using chromium
            await renderRemotionMedia({
                codec: "h264",
                composition: {
                    ...composition,
                    // Override the duration to use the actual duration from inputProps
                    durationInFrames: actualDurationInFrames,
                },
                serveUrl: bundleLocation,
                outputLocation: path.join(VIDEOS_DIR, `${renderId}.mp4`),
                inputProps: {
                    ...inputProps,
                    baseUrl,
                },
                // Enhanced quality settings for maximum quality output
                chromiumOptions: {
                    headless: true,
                    disableWebSecurity: false,
                    ignoreCertificateErrors: false,
                },
                timeoutInMilliseconds: 300000, // 5 minutes
                onProgress: ((progress) => {
                    // Extract just the progress percentage from the detailed progress object
                    updateRenderProgress(renderId, progress.progress);
                    sendMessage('render-result', {
                        taskNo: renderId,
                        status: 'rendering',
                        progress: progress.progress * 100,
                        timestamp: Date.now(),
                    });
                }) as RenderMediaOnProgress,
                // Highest quality video settings
                crf: 1, // Lowest CRF for near-lossless quality (range 1-51, where 1 is highest quality)
                imageFormat: "png", // Use PNG for highest quality frame captures
                colorSpace: "bt709", // Better color accuracy
                x264Preset: "veryslow", // Highest quality compression
                jpegQuality: 100, // Maximum JPEG quality for any JPEG operations
            });

            // Get file size
            const videoPath = path.join(VIDEOS_DIR, `${renderId}.mp4`);
            const stats = fs.statSync(videoPath);
            const outputPath = `/rendered-videos/${renderId}.mp4`;
            completeRender(renderId, outputPath, stats.size);
            console.log(`${tenantName} ${taskName} Render ${renderId} completed successfully`);
            await sendMessage('render-result', {
                taskNo: renderId,
                status: 'completed',
                timestamp: Date.now(),
            });
            
            // Only attempt upload if uploadUrl is provided
            if (uploadUrl) {
                const uploadResult = await uploadToOSS({
                    stsFetchUrl: uploadUrl,
                    token: token,
                    tenantId: tenantId,
                    filePath: videoPath,
                    fileName: `${renderId}.mp4`,
                    task: renderId,
                });
                const {bucket, name, url, etag, objectId} = uploadResult;
                console.log(`${tenantName} ${taskName} Upload ${renderId} ${url} completed successfully`);
                await sendMessage('render-result', {
                    taskNo: renderId,
                    status: 'uploaded',
                    timestamp: Date.now(),
                    objectId: objectId,
                    url: url,
                    etag: etag,
                    bucket: bucket,
                    objectKey: name,
                });
            } else {
                console.log(`${tenantName} ${taskName} No upload URL provided, skipping upload`);
                await sendMessage('render-result', {
                    taskNo: renderId,
                    status: 'completed_local',
                    timestamp: Date.now(),
                });
            }
        } catch (error: any) {
            await sendMessage('render-result', {
                taskNo: renderId,
                status: 'failed',
                timestamp: Date.now(),
                errorMsg: error.message,
            });
            failRender(renderId, error.message);
            console.error(`${tenantName} ${taskName} Render ${renderId} failed:`, error);
        }
    })();

    return renderId;
}

/**
 * Get the current progress of a render
 */
export function getRenderProgress(renderId: string) {
    // Add logging to debug missing renders
    console.log("Checking progress for render:", renderId);
    console.log("Available render IDs:", Array.from(renderStatus.keys()));

    const progress = renderProgress.get(renderId) || 0;
    const status = renderStatus.get(renderId) || "rendering";
    const error = renderErrors.get(renderId);
    const url = renderUrls.get(renderId);
    const size = renderSizes.get(renderId);

    if (!renderStatus.has(renderId)) {
        throw new Error(`No render found with ID: ${renderId}`);
    }

    return {
        renderId,
        progress,
        status,
        error,
        url,
        size,
    };
}
