// Other types remain the same
import {z} from "zod";

export const CompositionProps = z.object({
  overlays: z.array(z.any()), // Replace with your actual Overlay type
  playerMetadata: z.object({
    durationInFrames: z.number(),
    width: z.number(),
    height: z.number(),
    fps: z.number(),
  }),
  src: z.string().optional(),
});

export const RenderRequest = z.object({
  id: z.string(),
  inputProps: CompositionProps,
});

export const ProgressRequest = z.object({
  bucketName: z.string(),
  id: z.string(),
});

export type ProgressResponse =
  | { type: "error"; message: string }
  | { type: "progress"; progress: number }
  | { type: "done"; url: string; size: number };
