#!/bin/bash

# 启动脚本
set -e

MODE=${1:-dev}

case $MODE in
  "dev")
    echo "🚀 启动开发环境..."
    npm start
    ;;
  "prod")
    echo "🚀 启动生产环境..."
    if [ ! -d "dist" ]; then
      echo "❌ 未找到构建产物，请先运行构建："
      echo "   npm run build:all"
      exit 1
    fi
    npm run start:prod
    ;;
  "docker")
    echo "🐳 使用 Docker 启动..."
    docker-compose -f docker-compose.dev.yml up
    ;;
  "docker-build")
    echo "🔨 构建并启动 Docker..."
    docker-compose -f docker-compose.dev.yml up --build
    ;;
  *)
    echo "用法: $0 [dev|prod|docker|docker-build]"
    echo ""
    echo "模式说明："
    echo "  dev           - 开发模式（默认）"
    echo "  prod          - 生产模式"
    echo "  docker        - Docker 模式"
    echo "  docker-build  - 构建并启动 Docker"
    exit 1
    ;;
esac
