#!/bin/bash

# Docker 构建脚本
set -e

# 配置
IMAGE_NAME="clipnest-render"
TAG=${1:-latest}
REGISTRY=${2:-"registry.cn-hangzhou.aliyuncs.com/gaoling_test"}

echo "🐳 开始构建 Docker 镜像..."
echo "镜像名称: $IMAGE_NAME"
echo "标签: $TAG"
echo "仓库: $REGISTRY"
echo ""

# 构建镜像
echo "🔨 构建 Docker 镜像..."
docker build -t $IMAGE_NAME:$TAG .

# 如果提供了仓库地址，则打标签
if [ ! -z "$REGISTRY" ]; then
    echo "🏷️  为镜像打标签..."
    docker tag $IMAGE_NAME:$TAG $REGISTRY/$IMAGE_NAME:$TAG
    
    echo "📤 推送镜像到仓库..."
    docker push $REGISTRY/$IMAGE_NAME:$TAG
fi

echo ""
echo "✅ Docker 镜像构建完成！"
echo ""
echo "🚀 运行命令："
echo "  本地运行: docker run -p 3000:3000 $IMAGE_NAME:$TAG"
echo "  使用 compose: docker-compose -f docker-compose.dev.yml up"
echo ""
echo "📊 镜像信息："
docker images | grep $IMAGE_NAME
