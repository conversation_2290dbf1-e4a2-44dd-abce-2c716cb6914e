#!/bin/bash

# 构建脚本
set -e

echo "🚀 开始构建 ClipNest Render 应用..."

# 清理之前的构建产物
echo "🧹 清理构建产物..."
rm -rf dist
rm -rf .next

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 构建 TypeScript 服务器代码
echo "🔨 构建 TypeScript 服务器代码..."
npx tsc --project tsconfig.build.json
npx tsc-alias -p tsconfig.build.json

# 构建 Next.js 应用
echo "⚡ 构建 Next.js 应用..."
npm run build

echo "✅ 构建完成！"
echo ""
echo "📁 构建产物："
echo "  - dist/          (服务器代码)"
echo "  - .next/         (Next.js 应用)"
echo ""
echo "🚀 启动命令："
echo "  开发环境: npm start"
echo "  生产环境: npm run start:prod"
