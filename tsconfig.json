{"compilerOptions": {"lib": ["es2020", "dom"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "incremental": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "target": "ES2020"}, "include": ["src/**/*", "lib/**/*", "utils/**/*", "packages/**/*", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}