# 集群部署样例,数据目录: ./data
version: '3.8'

services:
  ## clipnest_render  48088
  clipnest_render:
    image: registry.cn-hangzhou.aliyuncs.com/gaoling_test/clipnest-render:latest
    container_name: clipnest_render
    restart: on-failure
    privileged: true
    environment:
      - JVM_XMS=1000m
      - JVM_XMX=1000m
      - SPRING_PROFILES_ACTIVE=local
    network_mode: host
    logging:
      options:
        max-size: "10m"
        max-file: "10"
    volumes:
      - ./data/clipnest_render/logs:/var/logs
      - /etc/hosts:/etc/hosts
