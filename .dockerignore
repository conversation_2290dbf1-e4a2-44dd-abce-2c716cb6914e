# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建产物
.next
dist
out
build

# 开发文件
.git
.gitignore
README.md
LICENSE.md
*.md

# 测试文件
tests
coverage
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx
jest.config.js

# 开发工具配置
.vscode
.idea
*.swp
*.swo
*~

# 环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 临时文件
.tmp
.temp
*.tmp
*.temp

# 操作系统文件
.DS_Store
Thumbs.db

# Docker 相关
Dockerfile*
docker-compose*
.dockerignore

# 部署相关
deploy.mjs

# TypeScript 构建信息
tsconfig.tsbuildinfo

# 输出目录（如果不需要保留）
# output

# 包管理器锁文件（保留 package-lock.json，排除其他）
yarn.lock
pnpm-lock.yaml
