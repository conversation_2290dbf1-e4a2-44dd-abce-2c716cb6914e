// server.ts
import {SimpleConsumer, MessageView, SimpleConsumerOptions, Producer, ProducerOptions} from 'rocketmq-client-nodejs';
import {createServer, IncomingMessage, ServerResponse} from 'http';
import next from 'next';
import axios from 'axios';
import {getRenderProgress, startRendering} from '@/lib/ssr-helpers/custom-renderer';
import {sendMessage} from "@/utils/message/mq-sender";

const dev = process.env.NODE_ENV !== 'production';
const app = next({dev});
const handle = app.getRequestHandler();

const consumer = new SimpleConsumer(<SimpleConsumerOptions>{
    consumerGroup: 'nodejs-demo-group',
    endpoints: '************:8081',
    subscriptions: new Map().set('render', '*'),
});

// 启动 RocketMQ 客户端
async function startRocketMQ() {
    await consumer.startup();
    console.log('[RocketMQ] Consumer started');

    async function poll() {
        try {
            const messages: MessageView[] = await consumer.receive(1, 10000);
            if (messages.length > 0) {
                console.log('Received %d messages', messages.length);
                for (const message of messages) {
                    console.log(message);
                    console.log('body=%o', message.body.toString());
                    const renderId = await processMessage(message);
                    await consumer.ack(message);
                    if (renderId) {
                        //await waitForRenderCompletion(renderId);
                    }
                }
            }
        } catch (err) {
            console.error('[RocketMQ] Error while receiving messages', err);
        } finally {
            setTimeout(poll, 1000);
        }
    }

    poll();
}

interface RenderTaskMessage {
    taskNo: string;
    taskName: string;
    renderJson: string;
    renderConfig: string;
    userId: string;
    tenantId: string;
    tenantName: string;
    uploadUrl: string;
    token: string;

    [key: string]: any;
}

async function processMessage(message: MessageView) {
    console.log('Received MQ Message:', message.body.toString());

    let mqBody: RenderTaskMessage;
    try {
        mqBody = JSON.parse(message.body.toString());
    } catch (err) {
        console.error('Invalid JSON in message body', err);
        return;
    }
    if (!mqBody.renderJson) {
        console.warn('No renderJson found in message, skipping...');
        return;
    }
    if (!mqBody.taskNo) {
        console.warn('No taskNo found in message, skipping...');
        return;
    }
    if (!mqBody.tenantId) {
        console.warn('No tenantId found in message, skipping...');
        return;
    }
    if (!mqBody.uploadUrl) {
        console.warn('No uploadUrl found in message, skipping...');
        return;
    }
    if (!mqBody.token) {
        console.warn('No token found in message, skipping...');
        return;
    }
    const fetchUrlResult = await axios.get(mqBody.renderJson, {
        headers: {
            Authorization: `Bearer ${mqBody.token}`,
            'tenant-id': mqBody.tenantId,
        }
    });
    if (fetchUrlResult.data.code !== 0) {
        throw new Error(`Failed to fetch STS: ${fetchUrlResult.data.msg}`);
    }
    const jsonUrl = fetchUrlResult.data.data;
    console.log('jsonUrl:', jsonUrl);
    try {
        const response = await axios.get(jsonUrl, {timeout: 5000});
        const body = response.data;

        console.log('Received body:', JSON.stringify(body, null, 2));
        console.log('inputProps:', JSON.stringify(body.inputProps, null, 2));

        const renderId = await startRendering(
            body.id, body.inputProps, mqBody.taskNo, mqBody.taskName,
            mqBody.userId, mqBody.tenantId, mqBody.tenantName,
            mqBody.uploadUrl, mqBody.token
        );
        console.log(`✅ Render completed. RenderId: ${renderId}`);

        await sendMessage('render-result', {
            taskNo: mqBody.taskNo,
            status: 'assigned',
            timestamp: Date.now(),
        });
        return renderId;
    } catch (err) {
        console.error('❌ Error while processing render task:', err);
    }
}

async function waitForRenderCompletion(renderId: string, timeout = 10 * 60 * 1000) {
    const interval = 5000; // 每5秒检查一次
    let elapsed = 0;

    return new Promise<void>((resolve, reject) => {
        const timer = setInterval(() => {
            try {
                const status = getRenderProgress(renderId).status;
                console.log(`Render progress for ${renderId}: ${status}`);
                if (status === 'done') {
                    clearInterval(timer);
                    resolve();
                } else if (status === 'error') {
                    clearInterval(timer);
                    reject(new Error(`Render failed for ${renderId}`));
                }
                elapsed += interval;
                if (elapsed >= timeout) {
                    clearInterval(timer);
                    reject(new Error(`Render timeout for ${renderId}`));
                }
            } catch (e) {
                clearInterval(timer);
                reject(e);
            }
        }, interval);
    });
}

// 启动 HTTP 服务
app.prepare().then(() => {
    const server = createServer((req: IncomingMessage, res: ServerResponse) => {
        handle(req, res);
    });

    server.listen(3000, async (err?: Error) => {
        if (err) throw err;
        console.log('✅ Next.js running on http://localhost:3000');
        await startRocketMQ();
    });
});
