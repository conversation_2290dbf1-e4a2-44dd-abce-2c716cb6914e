import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { join } from 'path';

async function bootstrap() {
  // 使用 Fastify 作为底层 HTTP 适配器以获得更好性能
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({
      logger: true,
      // 支持文件上传
      bodyLimit: 100 * 1024 * 1024, // 100MB
    })
  );

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // 启用 CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // 注册文件上传支持
  await app.register(require('@fastify/multipart'), {
    limits: {
      fileSize: 100 * 1024 * 1024, // 100MB
    },
  });

  const port = process.env.PORT || 3000;
  await app.listen(port, '0.0.0.0');

  console.log(`✅ NestJS application is running on: http://localhost:${port}`);
}

bootstrap();
