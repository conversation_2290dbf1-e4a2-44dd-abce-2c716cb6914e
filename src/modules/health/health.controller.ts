import { Controller, Get, HttpStatus, HttpException } from '@nestjs/common';
import { HealthService, HealthStatus } from './health.service';

@Controller('api/health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  getHealth(): HealthStatus {
    const healthStatus = this.healthService.getHealthStatus();
    
    if (healthStatus.status === 'unhealthy') {
      throw new HttpException(healthStatus, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    return healthStatus;
  }
}
