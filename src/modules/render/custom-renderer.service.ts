import { Injectable, Logger } from '@nestjs/common';
import { RenderMediaOnProgress } from '@remotion/renderer';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

import { RenderStateService } from './render-state.service';
import { RemotionService } from './remotion.service';
import { UrlHelperService } from '../../common/services/url-helper.service';

/**
 * Custom Renderer Service
 * 
 * Migrated from lib/ssr-helpers/custom-renderer.ts
 * Custom renderer that uses browser-based rendering to avoid platform-specific dependencies
 */
@Injectable()
export class CustomRendererService {
  private readonly logger = new Logger(CustomRendererService.name);
  private readonly VIDEOS_DIR = path.join(process.cwd(), 'output', 'rendered-videos');

  // Track rendering progress - migrated from original
  public readonly renderProgress = new Map<string, number>();
  public readonly renderStatus = new Map<string, 'rendering' | 'done' | 'error'>();
  public readonly renderErrors = new Map<string, string>();
  public readonly renderUrls = new Map<string, string>();
  public readonly renderSizes = new Map<string, number>();

  constructor(
    private readonly renderStateService: RenderStateService,
    private readonly remotionService: RemotionService,
    private readonly urlHelperService: UrlHelperService,
  ) {
    // Ensure the videos directory exists
    if (!fs.existsSync(this.VIDEOS_DIR)) {
      fs.mkdirSync(this.VIDEOS_DIR, { recursive: true });
    }
  }

  /**
   * Start rendering process - migrated from original startRendering function
   */
  async startRendering(
    compositionId: string,
    inputProps: Record<string, unknown>,
    taskId: string,
    taskName: string,
    userId: string,
    tenantId: string,
    tenantName: string,
    uploadUrl: string,
    token: string,
  ): Promise<string> {
    const renderId = taskId || uuidv4();

    // Initialize render state
    this.renderStateService.saveRenderState(renderId, {
      status: 'rendering',
      progress: 0,
      timestamp: Date.now(),
    });

    // Initialize tracking maps
    this.renderProgress.set(renderId, 0);
    this.renderStatus.set(renderId, 'rendering');

    // Start rendering asynchronously
    void (async () => {
      try {
        // Update progress as rendering proceeds
        this.renderStateService.updateRenderProgress(renderId, 0);

        // Get the base URL for serving media files
        const baseUrl = this.urlHelperService.getBaseUrl();

        // Bundle the video
        const bundleLocation = await this.remotionService.createRemotionBundle(compositionId);

        // Select the composition
        const composition = await this.remotionService.selectRemotionComposition(
          bundleLocation,
          compositionId,
          {
            ...inputProps,
            // Pass the base URL to the composition for media file access
            baseUrl,
          }
        );

        // Get the actual duration from inputProps or use composition's duration
        const actualDurationInFrames =
          (inputProps.durationInFrames as number) || composition.durationInFrames;
        this.logger.log(`${tenantName} ${taskName} Using actual duration: ${actualDurationInFrames} frames`);

        // Render the video using chromium
        await this.remotionService.renderRemotionMedia({
          codec: 'h264',
          composition: {
            ...composition,
            // Override the duration to use the actual duration from inputProps
            durationInFrames: actualDurationInFrames,
          },
          serveUrl: bundleLocation,
          outputLocation: path.join(this.VIDEOS_DIR, `${renderId}.mp4`),
          inputProps: {
            ...inputProps,
            baseUrl,
          },
          // Enhanced quality settings for maximum quality output
          chromiumOptions: {
            headless: true,
            disableWebSecurity: false,
            ignoreCertificateErrors: false,
          },
          timeoutInMilliseconds: 300000, // 5 minutes
          onProgress: ((progress) => {
            // Extract just the progress percentage from the detailed progress object
            this.renderStateService.updateRenderProgress(renderId, progress.progress);
            this.renderProgress.set(renderId, progress.progress);
            
            // Send progress message (implement as needed)
            this.sendProgressMessage(renderId, progress.progress);
          }) as RenderMediaOnProgress,
          // Highest quality video settings
          crf: 1, // Lowest CRF for near-lossless quality (range 1-51, where 1 is highest quality)
          imageFormat: 'png', // Use PNG for highest quality frame captures
          colorSpace: 'bt709', // Better color accuracy
          x264Preset: 'veryslow', // Highest quality compression
          jpegQuality: 100, // Maximum JPEG quality for any JPEG operations
        });

        // Upload to OSS if needed
        await this.handleVideoUpload(renderId, uploadUrl, token, tenantId, taskName);

        this.logger.log(`${tenantName} ${taskName} ✅ Render ${renderId} completed successfully`);
      } catch (error: any) {
        await this.handleRenderError(renderId, error, tenantName, taskName);
      }
    })();

    return renderId;
  }

  /**
   * Get the current progress of a render - migrated from original
   */
  getRenderProgress(renderId: string) {
    // Add logging to debug missing renders
    this.logger.log('Checking progress for render:', renderId);
    this.logger.log('Available render IDs:', Array.from(this.renderStatus.keys()));

    const progress = this.renderProgress.get(renderId) || 0;
    const status = this.renderStatus.get(renderId) || 'rendering';
    const error = this.renderErrors.get(renderId);
    const url = this.renderUrls.get(renderId);
    const size = this.renderSizes.get(renderId);

    if (!this.renderStatus.has(renderId)) {
      throw new Error(`No render found with ID: ${renderId}`);
    }

    return {
      renderId,
      progress,
      status,
      error,
      url,
      size,
    };
  }

  private async handleVideoUpload(
    renderId: string,
    uploadUrl: string,
    token: string,
    tenantId: string,
    fileName: string,
  ): Promise<void> {
    // Implementation for video upload (OSS integration)
    // This would be implemented based on the original upload logic
    this.logger.log(`Handling video upload for render ${renderId}`);
    
    // Mark as completed for now
    this.renderStatus.set(renderId, 'done');
    this.renderStateService.completeRender(renderId, uploadUrl, 0);
  }

  private async handleRenderError(
    renderId: string,
    error: any,
    tenantName: string,
    taskName: string,
  ): Promise<void> {
    this.renderStatus.set(renderId, 'error');
    this.renderErrors.set(renderId, error.message);
    this.renderStateService.failRender(renderId, error.message);
    this.logger.error(`${tenantName} ${taskName} Render ${renderId} failed:`, error);
  }

  private async sendProgressMessage(renderId: string, progress: number): Promise<void> {
    // Implementation for sending progress messages
    // This would integrate with the message queue service
    this.logger.debug(`Render ${renderId} progress: ${Math.round(progress * 100)}%`);
  }
}
