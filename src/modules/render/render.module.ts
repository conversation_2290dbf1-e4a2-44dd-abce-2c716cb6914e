import { Modu<PERSON> } from '@nestjs/common';
import { RenderController } from './render.controller';
import { RenderService } from './render.service';
import { RenderStateService } from './render-state.service';
import { RemotionService } from './remotion.service';
import { CustomRendererService } from './custom-renderer.service';
import { UrlHelperService } from '../../common/services/url-helper.service';

@Module({
  controllers: [RenderController],
  providers: [
    RenderService,
    RenderStateService,
    RemotionService,
    CustomRendererService,
    UrlHelperService,
  ],
  exports: [
    RenderService,
    RenderStateService,
    RemotionService,
    CustomRendererService,
    UrlHelperService,
  ],
})
export class RenderModule {}
