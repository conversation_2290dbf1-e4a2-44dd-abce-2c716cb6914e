import { Injectable, Logger } from '@nestjs/common';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Remotion Service
 * 
 * Migrated from lib/ssr-helpers/remotion-wrapper.ts
 * 这个文件包装了 Remotion 功能，避免在构建时的静态分析问题
 */
@Injectable()
export class RemotionService {
  private readonly logger = new Logger(RemotionService.name);

  /**
   * 动态导入 Remotion 模块以避免构建时的静态分析
   */
  async createRemotionBundle(compositionId: string): Promise<string> {
    const { bundle } = await import('@remotion/bundler');

    const entryPath = path.join(
      process.cwd(),
      'packages',
      'overlay-renderer',
      compositionId,
      'entry.js'
    );

    // 检查入口文件是否存在
    if (!fs.existsSync(entryPath)) {
      throw new Error(`Remotion entry file not found: ${entryPath}`);
    }

    this.logger.log(`Creating Remotion bundle for composition: ${compositionId}`);
    this.logger.log(`Entry path: ${entryPath}`);

    return bundle(entryPath, undefined, {
      // Disable all platform-specific compositors
      webpackOverride: (config) => ({
        ...config,
        resolve: {
          ...config.resolve,
          fallback: {
            ...config.resolve?.fallback,
            // Explicitly disable ALL compositor packages
            '@remotion/compositor': false,
            '@remotion/compositor-darwin-arm64': false,
            '@remotion/compositor-darwin-x64': false,
            '@remotion/compositor-linux-x64': false,
            '@remotion/compositor-linux-arm64': false,
            '@remotion/compositor-win32-x64-msvc': false,
            '@remotion/compositor-windows-x64': false,
          },
        },
      }),
    });
  }

  async selectRemotionComposition(
    serveUrl: string, 
    id: string, 
    inputProps: Record<string, unknown>
  ) {
    const { selectComposition } = await import('@remotion/renderer');
    
    this.logger.log(`Selecting composition: ${id} from ${serveUrl}`);
    
    return selectComposition({
      serveUrl,
      id,
      inputProps,
    });
  }

  async renderRemotionMedia(options: any) {
    const { renderMedia } = await import('@remotion/renderer');
    
    this.logger.log('Starting Remotion media rendering...');
    
    return renderMedia(options);
  }
}
