import { IsString, IsObject, IsOptional } from 'class-validator';

export class RenderRequestDto {
  @IsString()
  id: string;

  @IsObject()
  inputProps: Record<string, unknown>;

  @IsOptional()
  @IsString()
  taskId?: string;

  @IsOptional()
  @IsString()
  taskName?: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  tenantId?: string;

  @IsOptional()
  @IsString()
  tenantName?: string;

  @IsOptional()
  @IsString()
  uploadUrl?: string;

  @IsOptional()
  @IsString()
  token?: string;
}

export class ProgressRequestDto {
  @IsString()
  id: string;

  @IsOptional()
  @IsString()
  bucketName?: string;
}

export class RenderResponseDto {
  renderId: string;
}

export class ProgressResponseDto {
  type: 'progress' | 'done' | 'error';
  progress?: number;
  url?: string;
  size?: number;
  message?: string;
}
