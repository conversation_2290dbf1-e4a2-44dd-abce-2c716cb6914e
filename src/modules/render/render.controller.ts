import { 
  <PERSON>, 
  Post, 
  Get, 
  Body, 
  Param, 
  HttpException, 
  HttpStatus,
  <PERSON><PERSON>,
  Header
} from '@nestjs/common';
import { FastifyReply } from 'fastify';
import { RenderService } from './render.service';
import { 
  RenderRequestDto, 
  ProgressRequestDto, 
  RenderResponseDto, 
  ProgressResponseDto 
} from './dto/render.dto';

@Controller('api/latest/ssr')
export class RenderController {
  constructor(private readonly renderService: RenderService) {}

  @Post('render')
  async render(@Body() renderRequest: RenderRequestDto): Promise<RenderResponseDto> {
    try {
      const result = await this.renderService.startRendering(renderRequest);
      return result;
    } catch (error) {
      throw new HttpException(
        { error: error instanceof Error ? error.message : 'Unknown error' },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('progress')
  getProgress(@Body() progressRequest: ProgressRequestDto): ProgressResponseDto {
    try {
      return this.renderService.getProgress(progressRequest.id);
    } catch (error) {
      throw new HttpException(
        { error: error instanceof Error ? error.message : 'Unknown error' },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('video/:id')
  @Header('Content-Type', 'video/mp4')
  async getVideo(
    @Param('id') id: string,
    @Res() reply: FastifyReply,
  ): Promise<void> {
    const { buffer, exists } = await this.renderService.getVideoFile(id);
    
    if (!exists) {
      reply.code(404).send('Video not found');
      return;
    }

    reply
      .header('Content-Type', 'video/mp4')
      .header('Content-Disposition', `attachment; filename="${id}.mp4"`)
      .send(buffer);
  }

  @Get('download/:id')
  @Header('Content-Type', 'video/mp4')
  async downloadVideo(
    @Param('id') id: string,
    @Res() reply: FastifyReply,
  ): Promise<void> {
    const { buffer, exists } = await this.renderService.getRenderedVideoFile(id);
    
    if (!exists) {
      reply.code(404).send('Video not found');
      return;
    }

    reply
      .header('Content-Type', 'video/mp4')
      .header('Content-Disposition', 'attachment; filename="rendered-video.mp4"')
      .send(buffer);
  }
}
