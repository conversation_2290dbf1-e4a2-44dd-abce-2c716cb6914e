import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

export interface RenderState {
  status: 'rendering' | 'done' | 'error';
  progress?: number;
  timestamp: number;
  url?: string;
  size?: number;
  error?: string;
}

@Injectable()
export class RenderStateService {
  private readonly RENDER_STATE_DIR = path.join(process.cwd(), 'output', 'render-state');

  constructor() {
    // 确保目录存在
    if (!fs.existsSync(this.RENDER_STATE_DIR)) {
      fs.mkdirSync(this.RENDER_STATE_DIR, { recursive: true });
    }
  }

  saveRenderState(renderId: string, state: RenderState): void {
    const filePath = path.join(this.RENDER_STATE_DIR, `${renderId}.json`);
    fs.writeFileSync(filePath, JSON.stringify(state));
  }

  getRenderState(renderId: string): RenderState | null {
    const filePath = path.join(this.RENDER_STATE_DIR, `${renderId}.json`);
    if (!fs.existsSync(filePath)) {
      return null;
    }
    return JSON.parse(fs.readFileSync(filePath, 'utf-8'));
  }

  updateRenderProgress(renderId: string, progress: number): void {
    const state = this.getRenderState(renderId) || {} as RenderState;
    state.progress = progress;
    state.status = 'rendering';
    this.saveRenderState(renderId, state);
  }

  completeRender(renderId: string, url: string, size: number): void {
    const state = this.getRenderState(renderId) || {} as RenderState;
    state.status = 'done';
    state.url = url;
    state.size = size;
    this.saveRenderState(renderId, state);
  }

  failRender(renderId: string, error: string): void {
    const state = this.getRenderState(renderId) || {} as RenderState;
    state.status = 'error';
    state.error = error;
    this.saveRenderState(renderId, state);
  }
}
