import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as fs from 'fs';
import { omit } from 'lodash';

import { RenderStateService } from './render-state.service';
import { RenderRequestDto, ProgressResponseDto } from './dto/render.dto';

// 动态导入渲染相关模块以避免构建时问题
const importRenderModules = async () => {
  const { startRendering } = await import('@/lib/ssr-helpers/custom-renderer');
  return { startRendering };
};

@Injectable()
export class RenderService {
  private readonly logger = new Logger(RenderService.name);
  private readonly VIDEOS_DIR = path.join(process.cwd(), 'output', 'rendered-videos');

  constructor(private readonly renderStateService: RenderStateService) {
    // 确保视频目录存在
    if (!fs.existsSync(this.VIDEOS_DIR)) {
      fs.mkdirSync(this.VIDEOS_DIR, { recursive: true });
    }
  }

  async startRendering(renderRequest: RenderRequestDto): Promise<{ renderId: string }> {
    try {
      this.logger.log(`Starting render for composition: ${renderRequest.id}`);
      this.logger.log(`InputProps: ${JSON.stringify(omit(renderRequest, 'inputProps.overlays'), null, 2)}`);

      // 动态导入渲染函数
      const { startRendering } = await importRenderModules();

      // 开始渲染过程
      const renderId = await startRendering(
        renderRequest.id,
        renderRequest.inputProps,
        renderRequest.taskId || uuidv4(),
        renderRequest.taskName || 'Untitled',
        renderRequest.userId || 'anonymous',
        renderRequest.tenantId || 'default',
        renderRequest.tenantName || 'Default Tenant',
        renderRequest.uploadUrl || '',
        renderRequest.token || '',
      );

      return { renderId };
    } catch (error) {
      this.logger.error(`Error in renderMedia: ${(error as Error).message}`, (error as Error).stack);
      throw error;
    }
  }

  getProgress(renderId: string): ProgressResponseDto {
    try {
      const state = this.renderStateService.getRenderState(renderId);

      if (!state) {
        return {
          type: 'error',
          message: `No render found with ID: ${renderId}`,
        };
      }

      switch (state.status) {
        case 'error':
          return {
            type: 'error',
            message: state.error || 'Unknown error occurred',
          };
        case 'done':
          return {
            type: 'done',
            url: state.url,
            size: state.size,
          };
        default:
          return {
            type: 'progress',
            progress: state.progress || 0,
          };
      }
    } catch (error) {
      this.logger.error(`Error in progress check: ${(error as Error).message}`, (error as Error).stack);
      throw error;
    }
  }

  async getVideoFile(id: string): Promise<{ buffer: Buffer | null; exists: boolean }> {
    const videoPath = path.join(process.cwd(), 'public', 'videos', `${id}.mp4`);

    if (!fs.existsSync(videoPath)) {
      return { buffer: null, exists: false };
    }

    const buffer = fs.readFileSync(videoPath);
    return { buffer, exists: true };
  }

  async getRenderedVideoFile(id: string): Promise<{ buffer: Buffer | null; exists: boolean }> {
    const videoPath = path.join(process.cwd(), 'public', 'rendered-videos', `${id}.mp4`);

    if (!fs.existsSync(videoPath)) {
      return { buffer: null, exists: false };
    }

    const buffer = fs.readFileSync(videoPath);
    return { buffer, exists: true };
  }
}
