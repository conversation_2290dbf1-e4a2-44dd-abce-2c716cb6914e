import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { SimpleConsumer, MessageView, SimpleConsumerOptions } from 'rocketmq-client-nodejs';
import axios from 'axios';
import { RenderService } from '../render/render.service';

interface RenderTaskMessage {
  taskNo: string;
  taskName: string;
  renderJson: string;
  renderConfig: string;
  userId: string;
  tenantId: string;
  tenantName: string;
  uploadUrl: string;
  token: string;
  [key: string]: any;
}

@Injectable()
export class MessageQueueService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MessageQueueService.name);
  private consumer: SimpleConsumer;
  private isPolling = false;

  constructor(private readonly renderService: RenderService) {
    this.consumer = new SimpleConsumer({
      consumerGroup: 'nodejs-demo-group',
      endpoints: '************:8081',
      subscriptions: new Map().set('render', '*'),
    } as SimpleConsumerOptions);
  }

  async onModuleInit() {
    await this.startRocketMQ();
  }

  async onModuleDestroy() {
    this.isPolling = false;
    if (this.consumer) {
      await this.consumer.shutdown();
      this.logger.log('[RocketMQ] Consumer shutdown');
    }
  }

  private async startRocketMQ() {
    try {
      await this.consumer.startup();
      this.logger.log('[RocketMQ] Consumer started');
      this.isPolling = true;
      this.poll();
    } catch (error) {
      this.logger.error('[RocketMQ] Failed to start consumer', error);
    }
  }

  private async poll() {
    while (this.isPolling) {
      try {
        const messages: MessageView[] = await this.consumer.receive(1, 10000);
        if (messages.length > 0) {
          this.logger.log(`Received ${messages.length} messages`);
          for (const message of messages) {
            this.logger.log(`Processing message: ${message.body.toString()}`);
            const renderId = await this.processMessage(message);
            await this.consumer.ack(message);
            if (renderId) {
              this.logger.log(`Message processed successfully, renderId: ${renderId}`);
            }
          }
        }
      } catch (err) {
        this.logger.error('[RocketMQ] Error while receiving messages', err);
      } finally {
        // 等待1秒后继续轮询
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  private async processMessage(message: MessageView): Promise<string | null> {
    try {
      let mqBody: RenderTaskMessage;
      try {
        mqBody = JSON.parse(message.body.toString());
      } catch (err) {
        this.logger.error('Invalid JSON in message body', err);
        return null;
      }

      // 验证必需字段
      const requiredFields = ['renderJson', 'taskNo', 'tenantId', 'uploadUrl', 'token'];
      for (const field of requiredFields) {
        if (!mqBody[field]) {
          this.logger.warn(`No ${field} found in message, skipping...`);
          return null;
        }
      }

      // 获取渲染配置
      const fetchUrlResult = await axios.get(mqBody.renderJson, {
        headers: {
          Authorization: `Bearer ${mqBody.token}`,
          'tenant-id': mqBody.tenantId,
        }
      });

      if (fetchUrlResult.data.code !== 0) {
        throw new Error(`Failed to fetch render config: ${fetchUrlResult.data.msg}`);
      }

      const jsonUrl = fetchUrlResult.data.data;
      this.logger.log(`Fetching render config from: ${jsonUrl}`);

      const response = await axios.get(jsonUrl, { timeout: 5000 });
      const body = response.data;

      this.logger.log(`Received render config for composition: ${body.id}`);

      // 开始渲染
      const result = await this.renderService.startRendering({
        id: body.id,
        inputProps: body.inputProps,
        taskId: mqBody.taskNo,
        taskName: mqBody.taskName,
        userId: mqBody.userId,
        tenantId: mqBody.tenantId,
        tenantName: mqBody.tenantName,
        uploadUrl: mqBody.uploadUrl,
        token: mqBody.token,
      });

      this.logger.log(`✅ Render started. RenderId: ${result.renderId}`);

      // 发送状态消息（这里可以集成消息发送逻辑）
      await this.sendMessage('render-result', {
        taskNo: mqBody.taskNo,
        status: 'assigned',
        timestamp: Date.now(),
      });

      return result.renderId;
    } catch (err) {
      this.logger.error('❌ Error while processing render task:', err);
      return null;
    }
  }

  private async sendMessage(topic: string, messageBody: Record<string, any>): Promise<void> {
    // 这里可以实现消息发送逻辑
    // 目前保持与原代码一致，暂时注释掉
    this.logger.log(`[MQ] Would send message to ${topic}:`, messageBody);
  }
}
