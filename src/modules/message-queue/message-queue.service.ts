import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SimpleConsumer, MessageView, SimpleConsumerOptions } from 'rocketmq-client-nodejs';
import axios from 'axios';
import { RenderService } from '../render/render.service';

export interface RenderTaskMessage {
  taskNo: string;
  taskName: string;
  renderJson: string;
  renderConfig: string;
  userId: string;
  tenantId: string;
  tenantName: string;
  uploadUrl: string;
  token: string;
  [key: string]: any;
}

@Injectable()
export class MessageQueueService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MessageQueueService.name);
  private consumer: SimpleConsumer;
  private isPolling = false;

  constructor(
    private readonly renderService: RenderService,
    private readonly configService: ConfigService,
  ) {
    // 从配置中获取 RocketMQ 设置，如果没有则使用默认值
    const consumerGroup = this.configService.get('ROCKETMQ_CONSUMER_GROUP', 'nodejs-demo-group');
    const endpoints = this.configService.get('ROCKETMQ_ENDPOINTS', '************:8081');

    this.consumer = new SimpleConsumer({
      consumerGroup,
      endpoints,
      subscriptions: new Map().set('render', '*'),
    } as SimpleConsumerOptions);
  }

  async onModuleInit() {
    await this.startRocketMQ();
  }

  async onModuleDestroy() {
    this.isPolling = false;
    if (this.consumer) {
      await this.consumer.shutdown();
      this.logger.log('[RocketMQ] Consumer shutdown');
    }
  }

  private async startRocketMQ() {
    try {
      await this.consumer.startup();
      this.logger.log('[RocketMQ] Consumer started');
      this.isPolling = true;
      this.poll();
    } catch (error) {
      this.logger.error('[RocketMQ] Failed to start consumer', error);
    }
  }

  private async poll() {
    while (this.isPolling) {
      try {
        const messages: MessageView[] = await this.consumer.receive(1, 10000);
        if (messages.length > 0) {
          this.logger.log(`Received ${messages.length} messages`);
          for (const message of messages) {
            this.logger.log(`Message details:`, message);
            this.logger.log(`Message body: ${message.body.toString()}`);
            const renderId = await this.processMessage(message);
            await this.consumer.ack(message);
            if (renderId) {
              this.logger.log(`Message processed successfully, renderId: ${renderId}`);
              // 可以在这里添加渲染完成等待逻辑
              // await this.waitForRenderCompletion(renderId);
            }
          }
        }
      } catch (err) {
        this.logger.error('[RocketMQ] Error while receiving messages', err);
      } finally {
        // 等待1秒后继续轮询
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  private async processMessage(message: MessageView): Promise<string | null> {
    this.logger.log('Received MQ Message:', message.body.toString());

    let mqBody: RenderTaskMessage;
    try {
      mqBody = JSON.parse(message.body.toString());
    } catch (err) {
      this.logger.error('Invalid JSON in message body', err);
      return null;
    }

    // 验证必需字段 - 与原 server.ts 完全一致
    if (!mqBody.renderJson) {
      this.logger.warn('No renderJson found in message, skipping...');
      return null;
    }
    if (!mqBody.taskNo) {
      this.logger.warn('No taskNo found in message, skipping...');
      return null;
    }
    if (!mqBody.tenantId) {
      this.logger.warn('No tenantId found in message, skipping...');
      return null;
    }
    if (!mqBody.uploadUrl) {
      this.logger.warn('No uploadUrl found in message, skipping...');
      return null;
    }
    if (!mqBody.token) {
      this.logger.warn('No token found in message, skipping...');
      return null;
    }

    try {
      // 获取渲染配置 - 与原 server.ts 完全一致
      const fetchUrlResult = await axios.get(mqBody.renderJson, {
        headers: {
          Authorization: `Bearer ${mqBody.token}`,
          'tenant-id': mqBody.tenantId,
        }
      });

      if (fetchUrlResult.data.code !== 0) {
        throw new Error(`Failed to fetch STS: ${fetchUrlResult.data.msg}`);
      }

      const jsonUrl = fetchUrlResult.data.data;
      this.logger.log('jsonUrl:', jsonUrl);

      const response = await axios.get(jsonUrl, { timeout: 5000 });
      const body = response.data;

      this.logger.log('Received body:', JSON.stringify(body, null, 2));
      this.logger.log('inputProps:', JSON.stringify(body.inputProps, null, 2));

      // 开始渲染 - 使用原始的 startRendering 函数
      const { startRendering } = await import('../../../lib/ssr-helpers/custom-renderer');
      const renderId = await startRendering(
        body.id, body.inputProps, mqBody.taskNo, mqBody.taskName,
        mqBody.userId, mqBody.tenantId, mqBody.tenantName,
        mqBody.uploadUrl, mqBody.token
      );

      this.logger.log(`✅ Render completed. RenderId: ${renderId}`);

      // 发送状态消息
      await this.sendMessage('render-result', {
        taskNo: mqBody.taskNo,
        status: 'assigned',
        timestamp: Date.now(),
      });

      return renderId;
    } catch (err) {
      this.logger.error('❌ Error while processing render task:', err);
      return null;
    }
  }

  /**
   * 等待渲染完成 - 从原 server.ts 迁移
   */
  private async waitForRenderCompletion(renderId: string, timeout = 10 * 60 * 1000): Promise<void> {
    const interval = 5000; // 每5秒检查一次
    let elapsed = 0;

    return new Promise<void>((resolve, reject) => {
      const timer = setInterval(async () => {
        try {
          // 使用原始的 getRenderProgress 函数
          const { getRenderProgress } = await import('../../../lib/ssr-helpers/custom-renderer');
          const status = getRenderProgress(renderId).status;
          this.logger.log(`Render progress for ${renderId}: ${status}`);

          if (status === 'done') {
            clearInterval(timer);
            resolve();
          } else if (status === 'error') {
            clearInterval(timer);
            reject(new Error(`Render failed for ${renderId}`));
          }

          elapsed += interval;
          if (elapsed >= timeout) {
            clearInterval(timer);
            reject(new Error(`Render timeout for ${renderId}`));
          }
        } catch (e) {
          clearInterval(timer);
          reject(e);
        }
      }, interval);
    });
  }

  /**
   * 发送消息 - 从原 utils/message/mq-sender.ts 迁移
   */
  private async sendMessage(topic: string, messageBody: Record<string, any>): Promise<void> {
    // 这里可以实现消息发送逻辑
    // 目前保持与原代码一致，暂时注释掉
    this.logger.log(`[MQ] Would send message to ${topic}:`, messageBody);

    // 如果需要发送消息，可以在这里实现 Producer 逻辑
    // const { sendMessage } = await import('@/utils/message/mq-sender');
    // await sendMessage(topic, messageBody);
  }
}
