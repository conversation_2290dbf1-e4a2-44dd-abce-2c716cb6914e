import { IsString, IsNotEmpty } from 'class-validator';

export class UploadResponseDto {
  success: boolean;
  id: string;
  fileName: string;
  serverPath: string;
  size: number;
  type: string;
}

export class DeleteMediaDto {
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  filePath: string;
}

export class DeleteResponseDto {
  success: boolean;
  message: string;
}
