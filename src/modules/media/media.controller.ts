import { 
  Controller, 
  Post, 
  Body, 
  Req, 
  HttpException, 
  HttpStatus,
  BadRequestException
} from '@nestjs/common';
import { FastifyRequest } from 'fastify';
import { MediaService } from './media.service';
import { UploadResponseDto, DeleteMediaDto, DeleteResponseDto } from './dto/media.dto';

@Controller('api/latest/local-media')
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Post('upload')
  async uploadFile(@Req() request: FastifyRequest & { file: () => Promise<any> }): Promise<UploadResponseDto> {
    try {
      // 获取上传的文件和表单数据
      const data = await request.file();

      if (!data) {
        throw new BadRequestException('No file uploaded');
      }

      // 从表单数据中获取 userId
      const userId = data.fields?.userId?.value as string;

      if (!userId) {
        throw new BadRequestException('userId is required');
      }

      return await this.mediaService.uploadFile(data, userId);
    } catch (error) {
      throw new HttpException(
        { error: error instanceof Error ? error.message : 'Failed to upload file' },
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('delete')
  async deleteFile(@Body() deleteRequest: DeleteMediaDto): Promise<DeleteResponseDto> {
    try {
      return await this.mediaService.deleteFile(deleteRequest);
    } catch (error) {
      throw new HttpException(
        { error: error instanceof Error ? error.message : 'Failed to delete file' },
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
