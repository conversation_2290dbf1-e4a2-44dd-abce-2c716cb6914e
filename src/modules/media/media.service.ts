import { Injectable, Logger, BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { writeFile, mkdir, unlink } from 'fs/promises';
import { existsSync } from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { UploadResponseDto, DeleteMediaDto, DeleteResponseDto } from './dto/media.dto';

@Injectable()
export class MediaService {
  private readonly logger = new Logger(MediaService.name);

  async uploadFile(file: any, userId: string): Promise<UploadResponseDto> {
    try {
      if (!file || !userId) {
        throw new BadRequestException('File and userId are required');
      }

      // 创建用户目录（如果不存在）
      const userDir = path.join(process.cwd(), 'public', 'users', userId);
      if (!existsSync(userDir)) {
        await mkdir(userDir, { recursive: true });
      }

      // 生成唯一文件名
      const fileId = uuidv4();
      const fileExtension = file.filename.split('.').pop();
      const fileName = `${fileId}.${fileExtension}`;
      const filePath = path.join(userDir, fileName);

      // 将文件保存到磁盘
      const buffer = await file.toBuffer();
      await writeFile(filePath, buffer);

      // 返回文件信息
      const publicPath = `/users/${userId}/${fileName}`;

      return {
        success: true,
        id: fileId,
        fileName: file.filename,
        serverPath: publicPath,
        size: buffer.length,
        type: file.mimetype,
      };
    } catch (error) {
      this.logger.error(`Error uploading file: ${(error as Error).message}`, (error as Error).stack);
      throw error;
    }
  }

  async deleteFile(deleteRequest: DeleteMediaDto): Promise<DeleteResponseDto> {
    try {
      const { userId, filePath } = deleteRequest;

      if (!userId || !filePath) {
        throw new BadRequestException('UserId and filePath are required');
      }

      // 安全检查：确保文件路径在用户目录内
      const userDirPrefix = `/users/${userId}/`;
      if (!filePath.startsWith(userDirPrefix)) {
        throw new ForbiddenException('Unauthorized file access');
      }

      // 转换公共路径为服务器路径
      const serverPath = path.join(process.cwd(), 'public', filePath);

      // 检查文件是否存在
      if (!existsSync(serverPath)) {
        throw new NotFoundException('File not found');
      }

      // 删除文件
      await unlink(serverPath);

      return {
        success: true,
        message: 'File deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Error deleting file: ${(error as Error).message}`, (error as Error).stack);
      throw error;
    }
  }
}
