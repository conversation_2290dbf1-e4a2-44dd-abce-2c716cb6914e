/**
 * Legacy Exports
 * 
 * This file provides backward compatibility exports for the migrated functions
 * to ensure existing imports continue to work during the transition period.
 */

// Re-export the original functions for backward compatibility
export * from '../../../lib/ssr-helpers/custom-renderer';
export * from '../../../lib/ssr-helpers/render-state';
export * from '../../../lib/ssr-helpers/remotion-wrapper';
export * from '../../../lib/url-helper';
export * from '../../../lib/types';

// Export utility functions that might be needed
export { getBaseUrl, toAbsoluteUrl } from '../../../lib/url-helper';

// Note: This file should be removed once all imports have been migrated to use NestJS services
