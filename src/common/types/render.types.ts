import { z } from 'zod';

/**
 * Render Types
 * 
 * Migrated from lib/types.ts
 */

export const CompositionProps = z.object({
  overlays: z.array(z.any()), // Replace with your actual Overlay type
  playerMetadata: z.object({
    durationInFrames: z.number(),
    width: z.number(),
    height: z.number(),
    fps: z.number(),
  }),
  src: z.string().optional(),
});

export const RenderRequest = z.object({
  id: z.string(),
  inputProps: CompositionProps,
});

export const ProgressRequest = z.object({
  bucketName: z.string(),
  id: z.string(),
});

export type ProgressResponse =
  | { type: 'error'; message: string }
  | { type: 'progress'; progress: number }
  | { type: 'done'; url: string; size: number };

// Export types for use in other modules
export type CompositionPropsType = z.infer<typeof CompositionProps>;
export type RenderRequestType = z.infer<typeof RenderRequest>;
export type ProgressRequestType = z.infer<typeof ProgressRequest>;
