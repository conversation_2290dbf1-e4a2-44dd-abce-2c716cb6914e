import { NextRequest, NextResponse } from 'next/server';
import {omit} from "lodash";

// 强制动态渲染，避免构建时静态分析
export const dynamic = 'force-dynamic';

/**
 * POST endpoint handler for rendering media using Remotion SSR
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("inputProps:", JSON.stringify(omit(body, 'inputProps.overlays'), null, 2));

    // 动态导入以避免构建时的问题
    const { startRendering } = await import("@/lib/ssr-helpers/custom-renderer");

    // Start the rendering process using our custom renderer
    const renderId = await startRendering(body.id, body.inputProps, body.taskId, body.taskName, body.userId, body.tenantId, body.tenantName, body.uploadUrl, body.token);

    return NextResponse.json({ renderId });
  } catch (error) {
    console.error("Error in renderMedia:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
