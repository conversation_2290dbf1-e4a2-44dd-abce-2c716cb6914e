import { NextRequest, NextResponse } from 'next/server';

// 强制动态渲染，避免构建时静态分析
export const dynamic = 'force-dynamic';

/**
 * POST endpoint handler for checking rendering progress
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id } = body;

    // 动态导入以避免构建时的问题
    const { getRenderState } = await import("@/lib/ssr-helpers/render-state");
    const state = getRenderState(id);

    if (!state) {
      return NextResponse.json({
        type: "error",
        message: `No render found with ID: ${id}`,
      });
    }

    switch (state.status) {
      case "error":
        return NextResponse.json({
          type: "error",
          message: state.error || "Unknown error occurred",
        });
      case "done":
        return NextResponse.json({
          type: "done",
          url: state.url,
          size: state.size,
        });
      default:
        return NextResponse.json({
          type: "progress",
          progress: state.progress || 0,
        });
    }
  } catch (error) {
    console.error("Error in progress check:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
