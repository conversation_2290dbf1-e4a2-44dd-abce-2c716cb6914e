{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "outDir": "./dist", "target": "es2020", "module": "commonjs", "moduleResolution": "node", "allowImportingTsExtensions": false, "declaration": false, "sourceMap": false}, "include": ["server.ts", "lib/**/*", "utils/**/*", "packages/**/*"], "exclude": ["node_modules", "dist", "app/**/*", "components/**/*", "tests/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}