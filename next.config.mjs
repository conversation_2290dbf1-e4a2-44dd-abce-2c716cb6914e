/** @type {import('next').NextConfig} */
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import path from 'path'

const nextConfig = {
    webpack: (config, {isServer}) => {
        config.stats = 'errors-only';

        // 👉 1. 增加 tsconfig 路径解析插件
        if (!config.resolve.plugins) {
            config.resolve.plugins = [];
        }
        config.resolve.plugins.push(new TsconfigPathsPlugin());

        config.resolve = {
            ...config.resolve,
            fallback: {
                ...config.resolve?.fallback,
                // Darwin (macOS)
                "@remotion/compositor-darwin-x64": false,
                "@remotion/compositor-darwin-arm64": false,

                // Linux
                "@remotion/compositor-linux-x64": false,
                "@remotion/compositor-linux-arm64": false,
                "@remotion/compositor-linux-x64-musl": false,
                "@remotion/compositor-linux-arm64-musl": false,
                "@remotion/compositor-linux-x64-gnu": false,
                "@remotion/compositor-linux-arm64-gnu": false,

                // Windows
                "@remotion/compositor-win32-x64": false,
                "@remotion/compositor-windows-x64": false,

                // Handle esbuild
                esbuild: false,
            },
            alias: {
                ...config.resolve.alias,
            }
        };

        // Add esbuild to external modules
        if (isServer) {
            config.externals = [...config.externals, "esbuild"];
            config.externals.push(({ request }, callback) => {
                if (request && request.endsWith('.node') || request.indexOf('@node-rs') !== -1) {
                    return callback(null, 'commonjs ' + request);
                }
                callback();
            });

            config.module.rules.push({
                test: /\.node$/,
                use: 'node-loader',
            });
        }

        return config;
    },
    experimental: {
        serverComponentsExternalPackages: [
            "@remotion/bundler",
            "@remotion/renderer",
            "@remotion/cli",
            "@remotion/captions",
            "@remotion/cloudrun",
            "@remotion/google-fonts",
            "@remotion/lambda",
            "@remotion/player",
            "@remotion/studio",
            "remotion",
            "esbuild",
            "fluent-ffmpeg",
            "sharp",
        ],
    },
};

export default nextConfig;
