// mq-sender.ts
// import {Producer, ProducerOptions} from 'rocketmq-client-nodejs';

// const producer = new Producer(<ProducerOptions>{
//     endpoints: '47.99.131.55:8081',
// });

let started = false;

export async function startProducer() {
    // if (!started) {
    //     await producer.startup();
    //     started = true;
    //     console.log('[RocketMQ] Producer started');
    // }
}

export async function sendMessage(topic: string, messageBody: Record<string, any>) {
    // if (!started) {
    //     await startProducer();
    // }
    // try {
    //     const result = await producer.send({
    //         topic,
    //         body: Buffer.from(JSON.stringify(messageBody)),
    //     });
    //     console.log(`[RocketMQ] Message sent to ${topic}`, result);
    //     return result;
    // } catch (err) {
    //     console.error(`[RocketMQ] Failed to send message to ${topic}`, err);
    //     throw err;
    // }
}
