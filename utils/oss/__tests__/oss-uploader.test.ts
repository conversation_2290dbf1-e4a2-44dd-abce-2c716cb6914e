// __tests__/oss-uploader.test.ts
import {uploadToOSS} from '../oss-uploader';
import fs from 'fs';
import path from 'path';

describe('OSS Uploader', () => {
    beforeAll(async () => {
    });

    it('should mp4 upload file successfully', async () => {

        const localFile = path.join(__dirname, 'test-video.mp4');
        const result = await uploadToOSS({
            filePath: localFile,
            fileName: 'test-video.mp4',
            task: 'b0c5260d60c74a448287a81784218857',
        });

        console.log('Upload result:', result);
        // 清理本地测试文件
        //fs.unlinkSync(localFile);
    });

    it('should image upload file successfully', async () => {

        const localFile = path.join(__dirname, 'test-image.jpg');
        const result = await uploadToOSS({
            filePath: localFile,
            fileName: 'test-image.jpg',
            task: 'b0c5260d60c74a448287a81784218857',
        });

        console.log('Upload result:', result);
        // 清理本地测试文件
        //fs.unlinkSync(localFile);
    });

    it('should mp3 upload file successfully', async () => {

        const localFile = path.join(__dirname, 'notice.mp3');
        const result = await uploadToOSS({
            filePath: localFile,
            fileName: 'notice.mp3',
            task: 'b0c5260d60c74a448287a81784218857',
        });

        console.log('Upload result:', result);
        // 清理本地测试文件
        //fs.unlinkSync(localFile);
    });
});
