// oss-uploader.ts
import OSS from 'ali-oss';
import axios from 'axios';

let client: OSS | null = null;
let stsExpireTime: number = 0; // 时间戳(ms)

interface STSResult {
    accessKeyId: string;
    secretAccessKey: string;
    securityToken: string;
    expiration: string;
    bucket: string;
    regionId: string;
    endpoint: string;
    callback?: string;
    callbackVar?: string;
    objectKey: string;
    objectId: string;
}

interface UploadResult {
    bucket: string;
    name: string;
    url: string;
    etag: string;
    objectId: string;
}

let currentSTS: STSResult | null = null;

export async function initOSSFromSTS(stsFetchUrl: string, token: string, tenantId: string, fileName: string, task?: string): Promise<STSResult> {
    try {
        const response = await axios.get(stsFetchUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
                'tenant-id': tenantId,
            },
            params: {
                fileName,
                task,
            },
        });

        console.log('response', response.data)
        if (response.data.code !== 0) {
            throw new Error(`Failed to fetch STS: ${response.data.msg}`);
        }
        const sts: STSResult = response.data.data;
        currentSTS = sts;

        client = new OSS({
            region: sts.regionId || 'oss-cn-hangzhou',
            bucket: sts.bucket,
            accessKeyId: sts.accessKeyId,
            accessKeySecret: sts.secretAccessKey,
            stsToken: sts.securityToken,
            secure: true,
        });

        stsExpireTime = new Date(sts.expiration).getTime() - 60 * 1000;
        return sts;
        console.log(`OSS client initialized. Expire at: ${new Date(stsExpireTime + 60 * 1000).toISOString()}`);
    } catch (err: any) {
        console.error('Failed to initialize OSS from STS:', err);
        throw err;
    }
}

export interface UploadToOSSParams {
    stsFetchUrl: string;
    token: string;
    tenantId: string;
    filePath: string;
    fileName: string;
    task?: string;
    partSizeMB?: number;
    parallel?: number;
}

/**
 * 自动调用 STS 接口获取上传参数，并上传文件
 */
export async function uploadToOSS(params: UploadToOSSParams): Promise<UploadResult> {
    const {stsFetchUrl, token, tenantId, filePath, fileName, task, partSizeMB = 5, parallel = 4} = params;

    const sts = await initOSSFromSTS(stsFetchUrl, token, tenantId, fileName, task);

    const {objectKey, callback} = currentSTS!;

    let callbackConfig: OSS.MultipartUploadOptions['callback'] | undefined;

    if (callback) {
        callbackConfig = {
            customValue: JSON.parse(atob(currentSTS!.callbackVar || '{}')),
            body: atob(callback),
            url: JSON.parse(atob(callback)).callbackUrl,
            contentType: 'application/x-www-form-urlencoded',
        };
    }
    console.log('callbackConfig:', callbackConfig);
    try {
        const result = await client!.multipartUpload(objectKey, filePath, {
            partSize: partSizeMB * 1024 * 1024,
            parallel,
            progress: (percent) => {
                console.log(`Uploading ${fileName}: ${Math.floor(percent * 100)}%`);
            },
            callback: callbackConfig,
        });
        const {bucket, name, res, etag} = result;
        const url = (res as any).requestUrls[0];
        console.log(`Upload success: ${result.name}`);
        return {
            bucket,
            name,
            url: url,
            etag,
            objectId: sts.objectId,
        };
    } catch (err: any) {
        console.error(`Upload failed for ${fileName}:`, err);
        throw err;
    }
}