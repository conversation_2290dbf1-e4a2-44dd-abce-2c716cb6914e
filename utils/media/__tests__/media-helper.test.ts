import * as fs from 'fs';
import * as path from 'path';
import {generateAutoTileImage, generateTileImage, getVideoMetadata} from '../media-helper'; // 假设你的实现文件名为 generateTileImage.ts

const TEST_VIDEO_PATH = path.resolve(__dirname, './test-video.mp4');
const OUTPUT_IMAGE_PATH = path.resolve(__dirname, './output-tile.jpg');

describe('generateTileImage', () => {
    beforeAll(() => {
        if (!fs.existsSync(TEST_VIDEO_PATH)) {
            throw new Error(`Missing test video at ${TEST_VIDEO_PATH}`);
        }
    });

    afterEach(() => {
        if (fs.existsSync(OUTPUT_IMAGE_PATH)) {
            //fs.unlinkSync(OUTPUT_IMAGE_PATH);
        }
    });

    it('should return metadata object for a valid video file', async () => {
        const metadata = await getVideoMetadata(TEST_VIDEO_PATH);
        console.log(metadata);
    });

    it('should generate a tile image from a video', async () => {
        const tileCols = 10;
        const tileRows = 3;
        const metadata = await getVideoMetadata(TEST_VIDEO_PATH);
        const frameCount = tileCols * tileRows;
        const frameWidth = Math.floor((metadata.width || 0) / 3);
        const frameHeight = Math.floor((metadata.height || 0) / 3);
        const durationSeconds = (metadata.duration || 1000) / 1000.0; // 防止除以0
        const fps = Number((frameCount / durationSeconds).toFixed(2)); // 保留两位小数
        await generateTileImage({
            inputVideoPath: TEST_VIDEO_PATH,
            outputImagePath: OUTPUT_IMAGE_PATH,
            frameWidth,
            frameHeight,
            tileCols,
            tileRows,
            fps,
        });

        expect(fs.existsSync(OUTPUT_IMAGE_PATH)).toBe(true);

        const stats = fs.statSync(OUTPUT_IMAGE_PATH);
        expect(stats.size).toBeGreaterThan(0);
    });

    it('should compute correct tileRows, fps and frame sizes based on duration interval', async () => {
        await generateAutoTileImage({
            inputVideoPath: TEST_VIDEO_PATH,
            outputImagePath: OUTPUT_IMAGE_PATH,
            duration: 100,  // 每200ms抽一帧，10秒共抽50帧
            tileCols: 10,   // 每行10张 → 共5行
            scale: 3,       // 1920/4=480, 1080/4=270
        });
    });

});
