version: '3.8'

services:
  clipnest-render:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: clipnest-render-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      # 挂载输出目录以便查看渲染结果
      - ./output:/app/output
      # 挂载日志目录
      - ./logs:/app/logs
    networks:
      - clipnest-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  clipnest-network:
    driver: bridge
