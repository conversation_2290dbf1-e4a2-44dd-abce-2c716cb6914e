{"name": "reactvideoeditor-pro", "version": "0.1.0", "private": true, "scripts": {"start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js", "build": "nest build", "build:server": "nest build", "build:all": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t clipnest-render .", "docker:run": "docker run -p 3000:3000 clipnest-render", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:dev:build": "docker-compose -f docker-compose.dev.yml up --build", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "deploy": "node deploy.mjs"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-fastify": "^10.0.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/config": "^3.0.0", "@nestjs/schedule": "^4.0.0", "@remotion/bundler": "v4.0.272", "@remotion/captions": "4.0.272", "@remotion/cli": "4.0.272", "@remotion/cloudrun": "v4.0.272", "@remotion/google-fonts": "4.0.272", "@remotion/lambda": "4.0.272", "@remotion/player": "v4.0.272", "@remotion/renderer": "v4.0.272", "@remotion/studio": "v4.0.272", "ali-oss": "^6.23.0", "axios": "^1.10.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^4.1.0", "fastify": "^4.0.0", "@fastify/multipart": "^8.0.0", "@fastify/static": "^7.0.0", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "lodash": "^4.17.21", "opentype.js": "^1.3.4", "reflect-metadata": "^0.1.13", "remotion": "4.0.272", "rocketmq-client-nodejs": "^1.0.1", "rxjs": "^7.8.1", "sharp": "^0.34.3", "uuid": "^10.0.0", "zod": "^3.22.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/ali-oss": "^6.16.11", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.12", "@types/node": "^20.19.9", "@types/opentype.js": "^1.3.8", "@types/uuid": "^10.0.0", "@types/jest": "^29.5.2", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}